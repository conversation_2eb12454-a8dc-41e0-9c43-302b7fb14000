ARG NODEJS_VERSION=20.19.1
ARG DEBIAN_VERSION=bookworm
ARG NODEJS_BASE_IMAGE=node:${NODEJS_VERSION}-${DEBIAN_VERSION}-slim

#
# ---- Base Node ----  ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----
FROM ${NODEJS_BASE_IMAGE} AS dependencies

RUN set -ex \
  && apt-get update && apt-get install -y git --no-install-recommends \
  && rm -rf /var/lib/apt/lists/*

# just in case pnpm is used
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
ENV COREPACK_ENABLE_DOWNLOAD_PROMPT=0
ENV NEXT_TELEMETRY_DISABLED=1

RUN \
  corepack disable &&\
  npm install -g corepack@latest --force &&\
  corepack enable

RUN \
  corepack npm install -g turbo@latest --force

#ENTRYPOINT ["/bin/bash"]
#CMD ["ls"]
# ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----
#
