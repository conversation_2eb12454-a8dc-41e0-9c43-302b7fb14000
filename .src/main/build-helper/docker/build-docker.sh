#!/bin/bash
#set -euxo pipefail
set -e

#echo "------------------------------------"
#echo "-"
#echo "printenv"
#printenv
#id
#gcloud -v >/dev/null 2>&1 || { echo >&2 "E: I require gcloud but it's not installed. Aborting."; exit 1; }
#gcloud config get account && gcloud config get project
#echo y | gcloud auth configure-docker europe-docker.pkg.dev,europe-west3-docker.pkg.dev,europe-west4-docker.pkg.dev
#echo "-"
#echo "------------------------------------"

DATETIME=$(date +"%Y%m%d_%H%M")
HOST=$(hostname -s)
DOMAIN=$(hostname -d)
USER=$(whoami)
USER_UID=$(id -u ${USER})
USER_GID=$(id -g ${USER})
#SCRIPT_DIR=$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )
DIR_BASENAME=$(basename "$0")
DIR_DIRNAME=$(dirname "$0")
#DIR_SCRIPT=$(dirname "$(readlink -f "$(type -P $0 || echo $0)")")
DIR=`pwd`

#https://stackoverflow.com/questions/59895/how-do-i-get-the-directory-where-a-bash-script-is-located-from-within-the-script
#https://stackoverflow.com/questions/********/how-to-get-script-directory-in-posix-sh
if [ -f "$0" ]; then script=$0; else script=$(command -v -- "$0"); fi
DIR_SCRIPT=$(dirname -- "$script")
DIR_SCRIPT=$(CDPATH=; cd -- "$DIR_SCRIPT" && pwd -P)

test -t 0 && DOCKER_USE_TTY="-it"

E2E_IMAGE="${E2E_IMAGE:-europe-west3-docker.pkg.dev/iac-dev-432418/mcr-microsoft-com/playwright:v1.50.1-noble}"
BUILD_IMAGE="${BUILD_IMAGE:-europe-docker.pkg.dev/iac-dev-432418/allo-docker/allo/node:20-slim-corepack-turbo}"

RUNTIME_SECRETS_INIT_IMAGE="${RUNTIME_SECRETS_INIT_IMAGE:-europe-docker.pkg.dev/iac-dev-432418/allo-docker/allo/secrets-init:0.5.3-alpine}"
RUNTIME_BASE_IMAGE="${RUNTIME_BASE_IMAGE:-node:20.19.0-alpine3.21}"


echo "------------------------------------"
echo "-"
echo "pwd                 : [$DIR]"
echo "basename            : [$DIR_BASENAME]"
echo "dirname             : [$DIR_DIRNAME]"
echo "dirname/readlink    : [$DIR_SCRIPT]"
echo "DATETIME            : [$DATETIME]"
echo "HOST                : [$HOST]"
echo "DOMAIN              : [$DOMAIN]"
echo "USER                : [$USER]"
echo "USER_UID            : [$USER_UID]"
echo "USER_GID            : [$USER_GID]"
echo "DOCKER_USE_TTY      : [$DOCKER_USE_TTY]"
echo "---"
echo "BUILD_IMAGE         : [$BUILD_IMAGE]"
echo "E2E_IMAGE           : [$E2E_IMAGE]"
echo "SECRETS_INIT_IMAGE  : [$RUNTIME_SECRETS_INIT_IMAGE]"
echo "BASE_IMAGE          : [$RUNTIME_BASE_IMAGE]"
echo "-"
echo "------------------------------------"

SCRIPT_BASE_DIR="$DIR"
cd $SCRIPT_BASE_DIR

export BUILD_APP="${BUILD_APP:-xyz}"
export BUILD_APP_IMAGE="${BUILD_APP_IMAGE:-test$BUILD_APP}"

OUTPUT_FOLDER="$SCRIPT_BASE_DIR/out/$BUILD_APP"

# ------------------------------------------------------

if [ ! -z ${BUILD_CLEAN_DIRECTORIES+x} ]; then
  rm -rf ${OUTPUT_FOLDER}

  rm -rf .pnpm-store
  rm -rf node_modules
  rm -rf .next
  rm -rf .turbo
  rm -rf dist
  rm -rf playwright-report
  rm -rf test-results

  rm -rf apps/*/.pnpm-store
  rm -rf apps/*/node_modules
  rm -rf apps/*/.next
  rm -rf apps/*/.turbo
  rm -rf apps/*/dist
  rm -rf apps/*/playwright-report
  rm -rf apps/*/test-results

  rm -rf packages/*/.pnpm-store
  rm -rf packages/*/node_modules
  rm -rf packages/*/.next
  rm -rf packages/*/.turbo
  rm -rf packages/*/dist
  rm -rf packages/*/playwright-report
  rm -rf packages/*/test-results
fi


# ------------------------------------------------------

mkdir -p ${OUTPUT_FOLDER}

# ------------------------------------------------------
# setup + authentication
# 
echo ".src/main/build-helper/docker/build-docker-setup-only.sh"
. .src/main/build-helper/docker/build-docker-setup-only.sh
echo ""

# ------------------------------------------------------
# drag build dependencies
#
echo ".src/main/build-helper/docker/build-docker-dep.sh"
. .src/main/build-helper/docker/build-docker-dep.sh
#find . -type f -name "package*.json" | grep -v "/out/" | grep -v node_modules | sort | xargs sha256sum > "$packageLockFile.sha256sum";
echo ""

# ------------------------------------------------------
# separate build of app compared to others

if ! test -f "lock.sha256sum" || ! sha256sum -c "lock.sha256sum";then
  rm -rf ${OUTPUT_FOLDER}
fi

mkdir -p ${OUTPUT_FOLDER}/full
cd $OUTPUT_FOLDER/full
rm -rf apps/$BUILD_APP/src
rm -rf apps/$BUILD_APP/public
cd $SCRIPT_BASE_DIR
echo ".src/main/build-helper/docker/build-docker-turbo-prune.sh"
. .src/main/build-helper/docker/build-docker-turbo-prune.sh
echo ""


# ------------------------------------------------------
# copy npmrc file to the separated folder

cd $SCRIPT_BASE_DIR
cp -r .src "$OUTPUT_FOLDER/full"
cp .npmrc "$OUTPUT_FOLDER/full"

# ------------------------------------------------------
# do build dependencies install on the separated folder

if ! test -f "lock.sha256sum" || ! sha256sum -c "lock.sha256sum";then
    rm -rf "$OUTPUT_FOLDER/full/yarn.lock"
    rm -rf "$OUTPUT_FOLDER/full/package-lock.json"
    rm -rf "$OUTPUT_FOLDER/full/pnpm-lock.yaml"

    rm -rf "$OUTPUT_FOLDER/full/yarn.lock.sha256sum"
    rm -rf "$OUTPUT_FOLDER/full/package-lock.json.sha256sum"
    rm -rf "$OUTPUT_FOLDER/full/pnpm-lock.yaml.sha256sum"

    rm -rf "$OUTPUT_FOLDER/full/apps/*/.pnpm-store"
    rm -rf "$OUTPUT_FOLDER/full/apps/*/node_modules"
    rm -rf "$OUTPUT_FOLDER/full/apps/*/.next"
    rm -rf "$OUTPUT_FOLDER/full/apps/*/.turbo"
    rm -rf "$OUTPUT_FOLDER/full/apps/*/dist"
    rm -rf "$OUTPUT_FOLDER/full/apps/*/playwright-report"
    rm -rf "$OUTPUT_FOLDER/full/apps/*/test-results"

    rm -rf "$OUTPUT_FOLDER/full/packages/*/.pnpm-store"
    rm -rf "$OUTPUT_FOLDER/full/packages/*/node_modules"
    rm -rf "$OUTPUT_FOLDER/full/packages/*/.next"
    rm -rf "$OUTPUT_FOLDER/full/packages/*/.turbo"
    rm -rf "$OUTPUT_FOLDER/full/packages/*/dist"
    rm -rf "$OUTPUT_FOLDER/full/packages/*/playwright-report"
    rm -rf "$OUTPUT_FOLDER/full/packages/*/test-results"
fi

cd $OUTPUT_FOLDER/full
echo "$SCRIPT_BASE_DIR/.src/main/build-helper/docker/build-docker-dep.sh"
. .src/main/build-helper/docker/build-docker-dep.sh

cd $SCRIPT_BASE_DIR
if ! test -f "lock.sha256sum" || ! sha256sum -c "lock.sha256sum";then
    find . -type f \( -name "yarn.lock" -o -name "package-lock.json" -o -name "pnpm-lock.yaml" -o -name "package.json" \) | grep -v "/out/" | grep -v node_modules | sort | xargs sha256sum > "lock.sha256sum";
fi

# ------------------------------------------------------

cd $OUTPUT_FOLDER/full
echo "$SCRIPT_BASE_DIR/.src/main/build-helper/docker/build-docker-build.sh"
. .src/main/build-helper/docker/build-docker-build.sh

# ------------------------------------------------------


mkdir -p apps/$BUILD_APP/.next/standalone/apps/$BUILD_APP/.next/static || true
cp -r apps/$BUILD_APP/.next/static apps/$BUILD_APP/.next/standalone/apps/$BUILD_APP/.next || true
mkdir -p apps/$BUILD_APP/.next/standalone/apps/$BUILD_APP/.next/public || true
cp -r apps/$BUILD_APP/public apps/$BUILD_APP/.next/standalone/apps/$BUILD_APP || true


if [ ! -z ${BUILD_APP_TEST+x} ]; then
  echo "$SCRIPT_BASE_DIR/.src/main/build-helper/docker/build-docker-test.sh"
  . .src/main/build-helper/docker/build-docker-test.sh
fi

docker build --rm \
    --build-arg BUILD_APP="$BUILD_APP" \
    --build-arg TURBO_APP="$BUILD_APP" \
    --build-arg APP_SOURCE_DIRECTORY="apps/$BUILD_APP" \
    --build-arg RUNTIME_SECRETS_INIT_IMAGE="$RUNTIME_SECRETS_INIT_IMAGE" \
    --build-arg RUNTIME_BASE_IMAGE="$RUNTIME_BASE_IMAGE" \
    -t "$BUILD_APP_IMAGE" \
    -f "apps/$BUILD_APP/.docker/Dockerfile" \
    .

cd $SCRIPT_BASE_DIR

if [ ! -z ${BUILD_APP_IMAGE_PUSH+x} ]; then
    docker push \
        "$BUILD_APP_IMAGE"
fi

if [ ! -z ${BUILD_APP_IMAGE_RUN+x} ]; then
  docker run -it --rm \
    --net=host \
    -e HOSTNAME=0.0.0.0 \
    -e PORT=3000 \
    -e NEXT_PUBLIC_ALLO_CDN_URL=https://cdn.dev.allo.restaurant \
    -e NEXT_PUBLIC_BASE_URL=https://app-dev.allo.restaurant \
    -e NEXT_INTERNAL_SERVICES_RESERVATION_SERVICE_URL=https://app-dev.allo.restaurant/reservation-service \
    -e NEXT_PUBLIC_SERVICES_RESERVATION_SERVICE_URL=https://app-dev.allo.restaurant/reservation-service \
    -e WRAPPER=/usr/bin/dumb-init \
    "$BUILD_APP_IMAGE"
fi