{"name": "allo-frontend", "private": true, "type": "module", "scripts": {"build": "turbo run build", "build:apps:kiosk": "turbo run build --filter=kiosk", "build:apps:reservations": "turbo run build --filter=reservations", "build:apps:webshop": "turbo run build --filter=webshop", "build:docker": "turbo prune kiosk --docker", "start:apps:kiosk": "turbo run start --filter=kiosk", "start:apps:reservations": "turbo run start --filter=reservations", "start:apps:webshop": "turbo run start --filter=webshop", "dev": "turbo run dev", "dev:apps:kiosk": "turbo run dev --filter=kiosk", "dev:apps:reservations": "turbo run dev --filter=reservations", "dev:apps:webshop": "turbo run dev --filter=webshop", "test": "turbo test", "test:watch": "turbo test:watch", "test:e2e": "turbo run test:e2e --only --env-mode=loose", "test:e2e:apps:kiosk": "turbo run test:e2e --only --env-mode=loose --filter=kiosk", "test:e2e:apps:reservations": "turbo run test:e2e --only --env-mode=loose --filter=reservations", "test:e2e:apps:webshop": "turbo run test:e2e --only --env-mode=loose --filter=webshop", "lint": "turbo run lint", "lint-staged": "lint-staged", "check-types": "turbo run check-types", "format": "prettier --write --ignore-unknown \"**/*.{json,js,ts,jsx,tsx,md}\"", "format:check": "prettier --check \"**/*.{json,js,ts,jsx,tsx,md}\"", "prepare": "husky install"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{json,js,ts,jsx,tsx}": ["prettier --write --ignore-unknown"]}, "devDependencies": {"@monorepo/eslint-prettier-config": "*", "corepack": "^0.32.0", "husky": "^8.0.0", "lint-staged": "^15.5.0", "prettier": "^3.5.2", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.11", "ts-jest": "^29.2.6", "turbo": "^2.4.4"}, "engines": {"node": ">=20"}, "nodeLinker": "node-modules", "packageManager": "npm@10.8.2", "workspaces": ["apps/*", "packages/*"], "overrides": {"react": "19.0.0", "react-dom": "19.0.0"}, "dependencies": {"sharp": "^0.32.6"}, "optionalDependencies": {"@img/sharp-linuxmusl-x64": "^0.33.2", "@img/sharp-linux-x64": "^0.33.2", "@img/sharp-wasm32": "^0.33.2", "@img/sharp-linux-arm64": "^0.33.2", "@img/sharp-darwin-x64": "^0.33.2", "@img/sharp-linux-s390x": "^0.33.2", "@img/sharp-linuxmusl-arm64": "^0.33.2", "@img/sharp-darwin-arm64": "^0.33.2", "@img/sharp-linux-arm": "^0.33.2", "@img/sharp-win32-x64": "^0.33.2", "@img/sharp-win32-ia32": "^0.33.2", "@tailwindcss/oxide-linux-x64-gnu": "^4.0.1", "@lightningcss/lightningcss-darwin-arm64": "^1.29.2", "@lightningcss/lightningcss-darwin-x64": "^1.29.2", "@lightningcss/lightningcss-freebsd-x64": "^1.29.2", "@lightningcss/lightningcss-linux-arm-gnueabihf": "^1.29.2", "@lightningcss/lightningcss-linux-arm64-gnu": "^1.29.2", "@lightningcss/lightningcss-linux-arm64-musl": "^1.29.2", "@lightningcss/lightningcss-linux-x64-gnu": "^1.29.2", "@lightningcss/lightningcss-linux-x64-musl": "^1.29.2", "@lightningcss/lightningcss-win32-arm64-msvc": "^1.29.2", "@lightningcss/lightningcss-win32-x64-msvc": "^1.29.2"}}