ARG NODEJS_VERSION=20.19.0
ARG ALPINE_VERSION=3.21
ARG SECRETS_INIT_IMAGE=europe-docker.pkg.dev/iac-dev-432418/allo-docker/allo/secrets-init:0.5.3-alpine
ARG NODEJS_ALPINE_BASE_IMAGE=node:${NODEJS_VERSION}-alpine${ALPINE_VERSION}
ARG E2E_IMAGE=europe-west3-docker.pkg.dev/iac-dev-432418/mcr-microsoft-com/playwright:v1.52.0-noble



######################################################
FROM ${SECRETS_INIT_IMAGE} AS secrets-init



######################################################
FROM ${NODEJS_ALPINE_BASE_IMAGE} AS base



######################################################
FROM base AS corepackbase
RUN \
      corepack disable &&\
      npm install -g corepack@latest --force &&\
      corepack enable



######################################################
FROM corepackbase AS corepackturbobase
RUN \
      corepack npm install -g turbo@latest --force



######################################################
FROM corepackturbobase AS turboprune
ARG TURBO_APP=kiosk
WORKDIR /app
ENV PATH /app/node_modules/.bin:$PATH


COPY apps ./apps
COPY packages ./packages
COPY package-lock*.json .
COPY package.json .
COPY *.json .
COPY *.*js* .

RUN \
      ls -lah &&\
      turbo prune $TURBO_APP --docker



######################################################
# Add lockfile and package.json's of isolated subworkspace
FROM corepackbase AS dependencies
RUN apk add --no-cache libc6-compat
RUN apk update
WORKDIR /app
ENV PATH /app/node_modules/.bin:$PATH


# First install the dependencies (as they change less often)
COPY --from=turboprune /app/out/json/ .
COPY --from=turboprune /app/out/pnpm-lock.yaml* ./pnpm-lock.yaml
COPY --from=turboprune /app/out/package-lock.json* ./package-lock.json

COPY .npmrc* .npmrc
RUN \
   export COREPACK_NPM_REGISTRY=$(npm config get registry --location project) &&\
   corepack npm ci --cache .npm --prefer-offline --no-audit --no-fund --location project



######################################################
FROM dependencies AS installer
RUN apk add --no-cache libc6-compat
RUN apk update
ARG TURBO_APP=kiosk
WORKDIR /app
ENV PATH /app/node_modules/.bin:$PATH
ENV NEXT_TELEMETRY_DISABLED 1

# Build the project
COPY --from=turboprune /app/out/full/ .
RUN \
    ls -la &&\
    echo %PATH &&\
    npm run build --filter=$TURBO_APP...
 


######################################################
FROM ${E2E_IMAGE} AS e2etest
ARG TURBO_APP=kiosk
ENV TURBO_APP=$TURBO_APP
WORKDIR /app
ENV PATH /app/node_modules/.bin:$PATH
ENV PLAYWRIGHT_BROWSERS_PATH=/ms-playwright
ENV NEXT_TELEMETRY_DISABLED 1

#RUN \
#      corepack npm install -g turbo@latest --force &&\
#      corepack npm install -g playwright@latest --force

COPY --from=installer /app .

#https://nextjs.org/docs/pages/api-reference/config/next-config-js/output#automatically-copying-traced-files
COPY --from=installer /app/apps/$TURBO_APP/.next/standalone ./apps/$TURBO_APP/.next/standalone
COPY --from=installer /app/apps/$TURBO_APP/.next/static ./apps/$TURBO_APP/.next/standalone/apps/$TURBO_APP/.next/static
COPY --from=installer /app/apps/$TURBO_APP/public ./apps/$TURBO_APP/.next/standalone/apps/$TURBO_APP/public

# TOGGLE THESE two lines if you want to see the app locally
#
RUN \
    npm run test:e2e
#CMD printenv && node ./apps/$TURBO_APP/.next/standalone/apps/$TURBO_APP/server.js


# FAIL FOR NOW
#RUN exit 1


######################################################
FROM base AS runner
ARG TURBO_APP=kiosk
ENV TURBO_APP=$TURBO_APP
WORKDIR /app
ENV NEXT_TELEMETRY_DISABLED 1

RUN    apk add --no-cache \
         bash \
         tini \
         libstdc++ dumb-init \
      && rm -rf /tmp/* \
      && rm -rvf /var/cache/* \
      # no need in --no-cache option above as we need to reuse APKINDEX and we delete afterwards with rm:
      && rm -fv /var/cache/apk/*


# Don't run production as root
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=secrets-init --chown=nextjs:nodejs --chmod=0755  /secrets-init ./secrets-init

# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=installer --chown=nextjs:nodejs /app/apps/$TURBO_APP/.next/standalone ./next/standalone
COPY --from=installer --chown=nextjs:nodejs /app/apps/$TURBO_APP/.next/static ./next/standalone/apps/$TURBO_APP/.next/static
COPY --from=installer --chown=nextjs:nodejs /app/apps/$TURBO_APP/public ./next/standalone/apps/$TURBO_APP/public

COPY --from=e2etest --chown=nextjs:nodejs /app/apps/$TURBO_APP/test-results* ./test-results
COPY --from=e2etest --chown=nextjs:nodejs /app/apps/$TURBO_APP/playwright-report* ./playwright-report


USER nextjs

EXPOSE 3000
ENV PORT 3000

ENV HOSTNAME "0.0.0.0"

#ENTRYPOINT ["/sbin/tini", "--"]
#ENTRYPOINT ["/usr/bin/dumb-init", "--"]
ENTRYPOINT ["/app/secrets-init", "--provider", "google", "--exit-early"]
CMD printenv && node next/standalone/apps/$TURBO_APP/server.js