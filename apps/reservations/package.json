{"name": "reservations", "version": "1.0.0", "type": "module", "private": true, "scripts": {"dev": "npm run types:api && NEXT_PUBLIC_ALLO_CDN_URL=https://cdn.dev.allo.restaurant NEXT_PUBLIC_BASE_URL=https://app-dev.allo.restaurant NEXT_INTERNAL_SERVICES_RESERVATION_SERVICE_URL=https://app-dev.allo.restaurant/reservation-service next dev --port 3002", "build": "next build", "build:docker": "turbo prune --docker", "start:next": "HOSTNAME=0.0.0.0 NEXT_PUBLIC_ALLO_CDN_URL=https://cdn.dev.allo.restaurant NEXT_PUBLIC_BASE_URL=https://app-dev.allo.restaurant NEXT_INTERNAL_SERVICES_RESERVATION_SERVICE_URL=https://app-dev.allo.restaurant/reservation-service next start --port=3000", "start": "HOSTNAME=0.0.0.0 NEXT_PUBLIC_ALLO_CDN_URL=https://cdn.dev.allo.restaurant NEXT_PUBLIC_BASE_URL=https://app-dev.allo.restaurant NEXT_PUBLIC_SERVICES_RESERVATION_SERVICE_URL=https://app-dev.allo.restaurant/reservation-service node .next/standalone/apps/reservations/server.js --port=3000", "lint": "next lint --max-warnings 0", "format": "prettier --write ./src", "check-types": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:e2e": "TEST_LOCALE=en TEST_SLUG=sushi-banana NEXT_PUBLIC_ALLO_CDN_URL=https://cdn.dev.allo.restaurant NEXT_PUBLIC_SERVICES_RESERVATION_SERVICE_URL=https://app-dev.allo.restaurant/reservation-service NEXT_INTERNAL_SERVICES_RESERVATION_SERVICE_URL=https://app-dev.allo.restaurant/reservation-service DEBUG=pw:webserver npx playwright@1.50.1 test", "test:e2e:ui": "TEST_LOCALE=en TEST_SLUG=sushi-banana NEXT_PUBLIC_ALLO_CDN_URL=https://cdn.dev.allo.restaurant NEXT_PUBLIC_SERVICES_RESERVATION_SERVICE_URL=https://app-dev.allo.restaurant/reservation-service NEXT_INTERNAL_SERVICES_RESERVATION_SERVICE_URL=https://app-dev.allo.restaurant/reservation-service npx playwright@1.50.1 test --ui", "test:e2e:debug": "npx playwright@1.50.1 test --debug", "types:api": "openapi-typescript https://app-dev.allo.restaurant/reservation-service/v3/api-docs -o src/lib/api/api.d.ts"}, "engines": {"node": ">=20 <24", "npm": ">=10"}, "packageManager": "npm@10.8.2", "nodeLinker": "node-modules", "dependencies": {"@allo/ui": "^0.0.29", "@fontsource-variable/bricolage-grotesque": "^5.1.1", "@fontsource-variable/inter": "^5.1.1", "@hookform/resolvers": "^4.1.3", "@monorepo/utils": "*", "date-fns": "^4.1.0", "date-fns-timezone": "^0.1.4", "date-fns-tz": "^3.2.0", "lucide-react": "^0.476.0", "next": "15.2.0", "next-intl": "^4.0.2", "next-runtime-env": "^3.2.2", "openapi-fetch": "^0.13.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "sharp": "^0.33.5", "swr": "^2.3.3", "zod": "^3.24.2"}, "devDependencies": {"@monorepo/eslint-prettier-config": "*", "@monorepo/jest-playwright-config": "*", "@monorepo/typescript-config": "*", "@next/eslint-plugin-next": "^15.2.0", "@playwright/test": "1.50.1", "@tailwindcss/postcss": "^4.0.9", "@types/jest": "^29.5.14", "@types/node": "^22.13.5", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "autoprefixer": "^10.4.20", "eslint": "^9.21.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "openapi-typescript": "^7.6.1", "postcss": "^8.5.3", "prettier": "^3.5.2", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.0.9", "ts-node": "^10.9.2", "typescript": "5.7.3"}}