import type { NextConfig } from 'next';
import createNextIntlPlugin from 'next-intl/plugin';

const nextConfig: NextConfig = {
  //output: process.env.BUILD_STANDALONE === "true" ? "standalone" : undefined, // important for packageing in docker
  output: 'standalone',
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    dangerouslyAllowSVG: true,
    //unoptimized: true,
    remotePatterns: [
      {
        hostname: 'storage.googleapis.com',
      },
      {
        hostname: 'unsplash.com',
      },
      {
        hostname: 'cdn.allo.restaurant',
      },
      {
        hostname: 'cdn.dev.allo.restaurant',
      },
    ],
  },
};

const withNextIntl = createNextIntlPlugin();

export default withNextIntl(nextConfig);
