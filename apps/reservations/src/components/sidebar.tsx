'use client';

import { Button, cn } from '@allo/ui';
import { useState } from 'react';

import { useScrollLock } from '~/lib/hooks/use-scroll-lock';

import { MobileHeader } from '~/components/mobile-header';

import { useTranslations } from 'next-intl';

export const Sidebar = ({ children }: { children: React.ReactNode }) => {
  const [isModalOpen, setModalOpen] = useState(false);

  const t = useTranslations('home');

  useScrollLock(isModalOpen);

  return (
    <>
      <aside
        className={cn(
          isModalOpen ? 'fixed inset-0 z-50 flex flex-col' : 'hidden',
          'bg-background-highlight h-screen w-full',
          'border-border-soft md:sticky md:top-0 md:block md:max-w-[375px] md:border-l'
        )}
      >
        <MobileHeader onClose={() => setModalOpen(false)}>
          {t('sidebar-title-mobile')}
        </MobileHeader>

        {children}
      </aside>

      {/* Mobile button */}
      {!isModalOpen && (
        <div className="sticky bottom-0 p-4 md:hidden">
          <Button className="w-full" variant="accent" onClick={() => setModalOpen(true)}>
            {t('sidebar-button-mobile')}
          </Button>
        </div>
      )}
    </>
  );
};
