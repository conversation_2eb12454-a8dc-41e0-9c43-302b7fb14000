import { Check } from 'lucide-react';
import Image, { StaticImageData } from 'next/image';
import { Restaurant } from '~/lib/api/types';
import delivery from './delivery.png';
import dineIn from './dine-in.png';
import takeaway from './takeaway.png';

export type Service = NonNullable<Restaurant['services']>[number];

interface ServiceOptionsProps {
  services: Service[];
}

const images: Record<Service, StaticImageData> = {
  DINE_IN: dineIn,
  TAKEAWAY: delivery,
  RESERVATION: delivery,
  EXPRESS: takeaway,
};

const labels: Record<Service, string> = {
  DINE_IN: 'Dine-in',
  TAKEAWAY: 'Takeaway',
  RESERVATION: 'Reservation',
  EXPRESS: 'Express',
};

export const ServiceOptions = ({ services }: ServiceOptionsProps) => {
  return (
    <ul>
      {services
        .filter((service) => service !== 'RESERVATION')
        .map((service) => (
          <li
            key={service}
            className="border-border-soft flex items-center justify-between gap-2 py-4 not-last:border-b"
          >
            <div className="flex items-center gap-2">
              <Image src={images[service]} alt={service} width={32} height={32} />
              <p>{labels[service]}</p>
            </div>
            <div className="bg-accent flex size-4 items-center justify-center rounded-full p-1 text-xs text-white">
              <Check strokeWidth={6} />
            </div>
          </li>
        ))}
    </ul>
  );
};
