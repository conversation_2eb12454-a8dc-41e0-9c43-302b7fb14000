'use client';

import {
  Button,
  Label,
  Listbox,
  ListboxEmpty,
  ListboxOption,
  ListboxOptions,
  ListboxTrigger,
  Spinner,
} from '@allo/ui';
import { addHours, format, isPast, isSameMinute, subHours } from 'date-fns';
import { CalendarX, Clock } from 'lucide-react';
import { Fragment, useMemo } from 'react';

import { useTranslations } from 'next-intl';
import { useTimeSlots } from './use-time-slots';

const SLOTS_HOUR_MARGIN = 2;

interface TimeSelectionProps {
  restaurantId: string;
  date: Date;
  guests: number;
  slot: Date | null;
  setSlot: (slot: Date | null) => void;
  setTableId?: (tableId: [] | null) => void | undefined;
  disclaimer?: string;
  reservationId?: string;
}

export const TimeSelection = ({
  restaurantId,
  date,
  slot,
  setSlot,
  disclaimer,
  guests,
  setTableId,
  reservationId,
}: TimeSelectionProps) => {
  const t = useTranslations('home');

  const { hour, setHour, hourOptions, slots, isLoading, fullDate, explanation } =
    useTimeSlots({
      restaurantId,
      guests,
      date,
      hourMargin: SLOTS_HOUR_MARGIN,
      reservationId,
    });
  const noSlots = useMemo(() => {
    return !isLoading && slots.length === 0;
  }, [isLoading, slots]);
  return (
    <div>
      <Label className="mb-2 inline-block" htmlFor="time-listbox">
        {t('sidebar-time-title')}
      </Label>
      <Listbox
        value={hour}
        onChange={(value) => {
          setHour(value);
          setSlot(null);
        }}
        disabled={isLoading}
      >
        <ListboxTrigger id="time-listbox">
          <span className="text-foreground-tertiary inline-flex size-4 items-center justify-center">
            {isLoading ? <Spinner size="sm" /> : <Clock />}
          </span>
          <span>{hour ? format(hour, 'HH:00') : 'Select time'}</span>
        </ListboxTrigger>
        <ListboxOptions className="max-h-[250px]">
          {hourOptions.map(({ date }) => (
            <ListboxOption
              key={date.toISOString()}
              value={date}
              disabled={isPast(addHours(date, 1))}
            >
              {format(date, 'HH:00')}
            </ListboxOption>
          ))}
          {hourOptions.length === 0 && <ListboxEmpty>No slots available</ListboxEmpty>}
        </ListboxOptions>
      </Listbox>

      {!noSlots ? (
        <div>
          <div className="mt-3 grid grid-cols-3 gap-3">
            {slots.map((item) => (
              <Button
                key={item.date.toISOString()}
                variant={slot && isSameMinute(item.date, slot) ? 'accent' : 'primary'}
                onClick={() => {
                  setTableId && setTableId(item.tableIds || []);
                  setSlot(
                    item.fullTime ||
                      new Date(
                        `${format(date, 'yyyy-MM-dd')}T${format(item.date, 'HH:mm')}`
                      )
                  );
                }}
              >
                {format(item.date, 'HH:mm')}
              </Button>
            ))}
          </div>
          {!isLoading && disclaimer && (
            <p className="text-foreground-secondary mt-5 text-xs">{disclaimer}</p>
          )}
        </div>
      ) : (
        <div className="border-border-soft bg-background-high mt-3 rounded-lg border p-3">
          {explanation?.description ? (
            <Fragment>
              <CalendarX className="text-foreground-tertiary" />
              <p className="text-foreground-secondary font-display mt-2 text-sm">
                {explanation.description}
              </p>
            </Fragment>
          ) : (
            <Fragment>
              <CalendarX className="text-foreground-tertiary" />
              <p className="text-foreground-secondary font-display mt-2 text-sm">
                {t('sidebar-time-not-available-times')}
                {fullDate &&
                  ` from ${format(subHours(fullDate, SLOTS_HOUR_MARGIN), 'HH:mm')} to ${format(
                    addHours(fullDate, SLOTS_HOUR_MARGIN),
                    'HH:mm'
                  )}`}
                .
              </p>
            </Fragment>
          )}
        </div>
      )}

      {slot && <input type="hidden" name="slot" value={format(slot, 'HH:mm')} />}
    </div>
  );
};
