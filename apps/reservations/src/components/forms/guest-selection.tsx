'use client';

import { Label, Listbox, ListboxOption, ListboxOptions, ListboxTrigger } from '@allo/ui';
import { useTranslations } from 'next-intl';

type GuestSelectionProps = {
  guests: number;
  setGuests: (value: number) => void;
  max?: number;
};

export const GuestSelection = ({ guests, setGuests, max = 60 }: GuestSelectionProps) => {
  const t = useTranslations('home');

  return (
    <div className="flex-1">
      <Label className="mb-2 inline-block" htmlFor="guests-button">
        {t('sidebar-guests-title')}
      </Label>
      <Listbox value={guests} onChange={setGuests}>
        <ListboxTrigger id="guests-button">
          {guests === 1
            ? `1 ${t('sidebar-guest-label')}`
            : `${guests} ${t('sidebar-guests-label')}`}
        </ListboxTrigger>
        <ListboxOptions>
          {new Array(max).fill(0).map((_, i) => (
            <ListboxOption key={i} value={i + 1}>
              {i + 1}
            </ListboxOption>
          ))}
        </ListboxOptions>
      </Listbox>
      <input type="hidden" name="guests" value={guests} />
    </div>
  );
};
