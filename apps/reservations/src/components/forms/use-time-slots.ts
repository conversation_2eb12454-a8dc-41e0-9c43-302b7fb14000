import { addHours, format, isToday, setHours } from 'date-fns';
import { useEffect, useMemo, useState } from 'react';
import useSWR from 'swr';
import { getTimeSuggestions } from '~/lib/data';
import { findAdjacentSlots, findClosestSlot, getFullHours } from '~/lib/slots';

export const useTimeSlots = ({
  restaurantId,
  guests,
  date,
  hourMargin,
  reservationId,
}: {
  restaurantId: string;
  guests: number;
  date: Date;
  hourMargin: number;
  reservationId?: string;
}) => {
  // using swr might seem a bit overkill, but it's a lightweight way to properly handle client requests with revalidation on window focus withou having to re-invent the wheel.
  const { data, isLoading } = useSWR(
    ['time-slots', restaurantId, guests, date],
    async () => {
      const { data } = await getTimeSuggestions(
        restaurantId,
        date,
        guests,
        reservationId
      );

      return {
        slots:
          data?.slots
            ?.filter((slot) => !!slot.fullTime)
            .map((slot) => ({
              date: new Date(slot.fullTime as string),
              available: slot.type === 'AVAILABLE',
              tableIds: slot.tableIds,
              fullTime: slot.fullTime,
            })) ?? [],
        explanation: data?.explanation,
      };
    }
  );

  // hour for the select that narrows down the slots
  const [hour, setHour] = useState<Date | null>(null);

  useEffect(() => {
    if (!data?.slots) return;

    // auto-select the best hour
    const options = getFullHours(data.slots);
    const target = isToday(date) ? addHours(new Date(), 1) : setHours(date, 18);
    const closest = findClosestSlot(options, target);

    const hasValidHour =
      hour &&
      options.some((option) => {
        return (
          option.date.getHours() === hour.getHours() &&
          option.date.getMinutes() === hour.getMinutes()
        );
      });
    const shouldUpdateHour = !hour || !hasValidHour;

    if (closest && shouldUpdateHour) {
      setHour(closest.date);
    }
  }, [data, date, hour]);

  const hourOptions = useMemo(() => getFullHours(data?.slots ?? []), [data]);

  // convert the date + hour into a strict date
  const fullDate = useMemo(() => {
    if (!hour) return null;

    return new Date(`${format(date, 'yyyy-MM-dd')}T${format(hour, 'HH:mm')}`);
  }, [date, hour]);

  const slots = useMemo(() => {
    if (!fullDate) return [];

    return findAdjacentSlots(data?.slots ?? [], fullDate, {
      hourMargin,
    });
  }, [data, fullDate, hourMargin]);

  return {
    slots,
    isLoading,
    hour,
    setHour,
    hourOptions,
    fullDate,
    explanation: data?.explanation,
  };
};
