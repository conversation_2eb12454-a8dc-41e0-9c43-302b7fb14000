'use client';

import { Button } from '@allo/ui';
import { useTranslations } from 'next-intl';
import { useParams } from 'next/navigation';
import { useState } from 'react';
import { DateSelection } from './date-selection';
import { GuestSelection } from './guest-selection';
import { TimeSelection } from './time-selection';

export const BookForm = ({
  restaurantId,
  maxGuests,
}: {
  restaurantId: string;
  maxGuests?: number;
}) => {
  const t = useTranslations('home');

  const [isNavigating, setIsNavigating] = useState(false);

  const [guests, setGuests] = useState(2);
  const [date, setDate] = useState(new Date());
  const [slot, setSlot] = useState<Date | null>(null);
  const [tableId, setTableId] = useState<[] | null>(null);

  const { locale, slug } = useParams();

  return (
    <form
      inert={isNavigating}
      action={`/${locale}/${slug}/book`}
      className="flex flex-1 flex-col md:h-full"
      onSubmit={() => {
        setIsNavigating(true);
      }}
    >
      <div className="overflow-y-auto overscroll-contain p-4 md:p-6">
        <h2 className="font-display mb-5 hidden text-base md:block">
          {t('sidebar-title')}
        </h2>

        <div className="flex flex-col gap-5">
          <div className="flex gap-2">
            <GuestSelection guests={guests} setGuests={setGuests} max={maxGuests} />
            <DateSelection date={date} setDate={setDate} />
          </div>
          <TimeSelection
            restaurantId={restaurantId}
            guests={guests}
            date={date}
            slot={slot}
            setTableId={setTableId}
            setSlot={setSlot}
            disclaimer={t('sidebar-disclaimer')}
          />
        </div>
      </div>

      {guests && date && slot && (
        <div className="flex flex-1 flex-col justify-end">
          <div className="border-border-soft sticky bottom-0 p-4 md:p-6">
            <Button
              type="submit"
              className="w-full"
              variant="accent"
              isLoading={isNavigating}
            >
              {t('sidebar-button')}
            </Button>
          </div>
        </div>
      )}

      {tableId && <input type="hidden" name="tableId" value={tableId} />}
      {slot && <input type="hidden" name="timeSlot" value={slot.toString()} />}
    </form>
  );
};
