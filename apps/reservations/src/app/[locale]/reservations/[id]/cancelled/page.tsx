import { getTranslations } from 'next-intl/server';
import { notFound } from 'next/navigation';
import { ClientRedirect } from '~/components/client-redirect';
import { Language } from '~/lib/api/types';
import { getReservation } from '~/lib/data';

export default async function CancelledPage({
  params,
}: {
  params: Promise<{ id: string; locale: Language }>;
}) {
  const { id, locale } = await params;
  const { data: reservation } = await getReservation(id, locale);

  if (!reservation || reservation.status !== 'CANCELLED' || !reservation.restaurantSlug) {
    return notFound();
  }

  const t = await getTranslations('bookForm');

  return (
    <div className="flex flex-col items-center justify-center h-screen max-w-md mx-auto text-center">
      <h1 className="text-2xl">{t('cancel-success-title')}</h1>
      <p className="mt-2 mb-4">{t('cancel-success-text')}</p>
      <ClientRedirect target={`/${reservation.restaurantSlug}`} className="text-accent" />
    </div>
  );
}
