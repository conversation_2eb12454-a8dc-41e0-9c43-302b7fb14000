import { Button } from '@allo/ui';
import { format } from 'date-fns';
import { getTranslations } from 'next-intl/server';
import { notFound } from 'next/navigation';
import { ReservationCard } from '~/components/reservation-card';
import { Link } from '~/i18n/navigation';
import { Language } from '~/lib/api/types';
import { getReservation } from '~/lib/data';

export default async function ReservationPage({
  params,
}: {
  params: Promise<{ id: string; locale: Language }>;
}) {
  const t = await getTranslations('bookForm');

  const { id, locale } = await params;
  const { data: reservation } = await getReservation(id, locale);
  if (
    !reservation ||
    reservation.status !== 'CONFIRMED' ||
    !reservation.restaurantName ||
    !reservation.restaurantSlug
  ) {
    return notFound();
  }

  const dateSlot = new Date(format(reservation.startTime, 'yyyy-MM-dd HH:mm z'));
  return (
    <div className="@container flex min-h-screen flex-col">
      <div className="flex-1 md:p-6">
        <ReservationCard
          restaurantName={reservation.restaurantName}
          guests={reservation.people}
          slot={dateSlot}
          locale={locale}
        >
          <div className="mx-auto max-w-sm text-center">
            <h2 className="mt-4 text-xl">{t('book-success-title')}</h2>

            <p className="text-foreground-secondary mt-2 text-sm">
              {t('book-success-text')}
            </p>
          </div>

          <div className="mt-10 flex flex-col gap-4">
            <Button asChild>
              <Link href={`/reservations/${reservation.id}/edit`}>
                {t('book-success-edit-button-text')}
              </Link>
            </Button>
            <Button asChild variant="accent">
              <Link href={`/${reservation.restaurantSlug}`}>
                {t('book-success-button-text')}
              </Link>
            </Button>
          </div>
        </ReservationCard>
      </div>
    </div>
  );
}
