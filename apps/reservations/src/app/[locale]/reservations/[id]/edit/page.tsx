import { Divider } from '@allo/ui';
import { format } from 'date-fns';
import { notFound } from 'next/navigation';
import { CancelReservation } from '~/components/forms/cancel-reservation';
import { EditForm } from '~/components/forms/edit-form';
import { ReservationCard } from '~/components/reservation-card';
import { Language } from '~/lib/api/types';
import { getReservation } from '~/lib/data';

export default async function EditReservationPage({
  params,
}: {
  params: Promise<{ id: string; locale: Language }>;
}) {
  const { id, locale } = await params;
  const { data: reservation } = await getReservation(id, locale);

  if (
    !reservation ||
    reservation.status !== 'CONFIRMED' ||
    !reservation.id ||
    !reservation.restaurantName ||
    !reservation.restaurantId
  ) {
    return notFound();
  }
  const dateSlot = new Date(format(reservation.startTime, 'yyyy-MM-dd HH:mm z'));
  return (
    <div className="@container flex min-h-screen flex-col">
      <div className="flex-1 md:p-6">
        <ReservationCard
          restaurantName={reservation.restaurantName}
          guests={reservation.people}
          slot={dateSlot}
          locale={locale}
        >
          <EditForm
            initialData={{
              guests: reservation.people,
              slot: new Date(reservation.startTime),
            }}
            restaurantId={reservation.restaurantId}
            reservationId={reservation.id}
          />
        </ReservationCard>

        <Divider className="my-6 mx-auto w-full max-w-[500px]" />

        <CancelReservation reservationId={reservation.id} />
      </div>
    </div>
  );
}
