import { hasLocale } from 'next-intl';
import { notFound } from 'next/navigation';
import { locales } from '~/i18n';
import { routing } from '~/i18n/routing';

export const generateStaticParams = async () => {
  return locales.map((locale) => ({ locale }));
};

export default async function LocaleLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;

  if (!hasLocale(routing.locales, locale)) return notFound();

  return children;
}
