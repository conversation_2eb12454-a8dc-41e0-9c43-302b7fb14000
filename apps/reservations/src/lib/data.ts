import { format, formatISO, isSameMinute } from 'date-fns';
import { defaultLocale } from '~/i18n';
import { client } from './api';
import { Language } from './api/types';
import { isLocaleAvailable, isSlugValid } from './utils/locale';

export const getRestaurant = async (slug: string, locale: Language) => {
  if (!isLocaleAvailable(locale) || !isSlugValid(slug)) {
    return { data: null };
  }

  return client.GET('/v1/restaurants/slug/{slug}', {
    params: {
      header: {
        'content-language': locale || defaultLocale,
      },
      path: { slug },
    },
  });
};

export const getRestaurantMetadata = async ({
  params,
}: {
  params: Promise<{
    slug: string;
    locale: Language;
  }>;
}) => {
  const { slug, locale } = await params;

  const { data } = await getRestaurant(slug, locale);

  return {
    title: data && data?.name ? `${data?.name} | allO Reservations` : `allO Reservations`,
  };
};

export const createReservation = async (
  restaurantId: string,
  {
    name,
    email,
    phone,
    guests,
    slot,
    specialRequests,
    tableId,
  }: {
    name: string;
    email: string;
    phone: string;
    guests: number;
    slot: Date | undefined;
    specialRequests?: string;
    tableId: string;
  }
) => {
  const dateFnsObjIsoString = slot?.toISOString?.() as string;
  const tableIds = tableId.split(',');

  return client.POST('/v1/restaurants/{restaurantId}/customer/reservations', {
    params: {
      path: {
        restaurantId,
      },
    },
    body: {
      customers: [
        {
          firstName: name,
          email,
          phone,
        },
      ],
      reservation: {
        people: guests,
        startTime: dateFnsObjIsoString,
        note: specialRequests,
        tableIds: tableIds,
      },
    },
  });
};

export const getReservation = async (id: string, locale: Language) => {
  return client.GET('/v1/customer/reservations/{reservationId}', {
    params: {
      header: { 'content-language': locale || defaultLocale },
      path: { reservationId: id },
    },
  });
};

export const updateReservation = async (
  reservationId: string,
  { guests, slot }: { guests: number; slot: Date }
) => {
  const dateFnsObjIsoString = formatISO(format(slot, 'yyyy-MM-dd HH:mm z'));
  return client.PATCH('/v1/customer/reservations/{reservationId}', {
    params: {
      path: {
        reservationId,
      },
    },
    body: {
      reservation: {
        people: guests,
        startTime: dateFnsObjIsoString, //slot.toISOString(),//.replace('.000Z', '+02:00'),
      },
    },
  });
};

export const getTimeSuggestions = async (
  restaurantId: string,
  date: Date,
  guests: number,
  reservationId?: string
) => {
  return client.GET('/v1/restaurants/{restaurantId}/customer/time-suggestions', {
    params: {
      path: { restaurantId },
      query: {
        day: format(date, 'yyyy-MM-dd'),
        people: guests,
        reservationId: reservationId,
      },
    },
  });
};

export const validateTimeSlot = async (
  restaurantId: string,
  config: {
    date: Date;
    guests: number;
    slot: string;
  }
) => {
  const { data } = await client.GET(
    '/v1/restaurants/{restaurantId}/customer/time-suggestions',
    {
      params: {
        path: { restaurantId },
        query: {
          day: format(config.date, 'yyyy-MM-dd'),
          people: config.guests,
        },
      },
    }
  );

  const fullDate = new Date(`${format(config.date, 'yyyy-MM-dd')}T${config.slot}`);

  return data?.slots?.some(
    (slot) => slot.fullTime && isSameMinute(slot.fullTime, fullDate)
  );
};

export const cancelReservation = async (id: string) => {
  return client.PATCH('/v1/customer/reservations/{reservationId}', {
    params: {
      path: {
        reservationId: id,
      },
    },
    body: {
      reservation: {
        people: 0,
        startTime: new Date().toISOString(),
        status: 'CANCELLED',
      },
    },
  });
};
