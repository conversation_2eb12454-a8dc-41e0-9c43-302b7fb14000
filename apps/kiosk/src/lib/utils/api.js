import axios from 'axios';
import { baseUrl, CURRENT_ORDER_CART_KEY, X_ALLO_CART } from '../constants';

export const api = axios.create({
  baseURL: baseUrl,
  headers: {
    'Content-Type': 'application/json',
  },
});

const HEADERS_BY_KEY = {
  cart: {
    headerKey: X_ALLO_CART,
    localStorageKey: CURRENT_ORDER_CART_KEY,
  },
};

api.interceptors.request.use((config) => {
  if (config.params?.headers?.length) {
    config.params.headers.map((headerKey) => {
      const header = HEADERS_BY_KEY[headerKey];

      if (header) {
        const value = localStorage.getItem(header.localStorageKey);
        config.headers[header.headerKey] = value;
      }
    });

    delete config.params.headers;
  }

  return config;
});
