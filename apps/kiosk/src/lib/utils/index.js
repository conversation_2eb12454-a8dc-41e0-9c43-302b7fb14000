import { isEmpty } from '@monorepo/utils';

export const formatToLocaleNumber = (val) => {
  if (!val || isNaN(val)) {
    val = 0;
  }

  try {
    return val.toLocaleString('de-DE', {
      maximumFractionDigits: 4,
      minimumFractionDigits: 2,
    });
  } catch (e) {
    return val.toFixed(2).toLocaleString();
  }
};

export const byId = (arr = [], key = '') => {
  if (isEmpty(arr)) {
    return {};
  }
  return arr.reduce((acc, next) => {
    acc[next[key] || next.id] = next;
    return acc;
  }, {});
};
