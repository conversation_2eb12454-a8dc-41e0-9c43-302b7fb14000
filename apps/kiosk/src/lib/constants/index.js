import { isEmpty } from '@monorepo/utils';
import { env } from 'next-runtime-env';
import { IconFlagChina } from '~/components/icons/flags/IconFlagChina';
import { IconFlagEnglishUS } from '~/components/icons/flags/IconFlagEnglishUS';
import { IconFlagGerman } from '~/components/icons/flags/IconFlagGerman';
import { IconFlagTurkey } from '~/components/icons/flags/IconFlagTurkey';
import { IconFlagVietnam } from '~/components/icons/flags/IconFlagVietnam';
import IconWarning from '~/components/icons/IconWarning';
import IconRemarkCelery from '~/components/icons/remarks/IconRemarkCelery';
import IconRemarkCrustaceans from '~/components/icons/remarks/IconRemarkCrustaceans';
import IconRemarkEggs from '~/components/icons/remarks/IconRemarkEggs';
import IconRemarkFish from '~/components/icons/remarks/IconRemarkFish';
import IconRemarkGluten from '~/components/icons/remarks/IconRemarkGluten';
import IconRemarkLupins from '~/components/icons/remarks/IconRemarkLupins';
import IconRemarkMilk from '~/components/icons/remarks/IconRemarkMilk';
import IconRemarkMollusks from '~/components/icons/remarks/IconRemarkMollusks';
import IconRemarkMustard from '~/components/icons/remarks/IconRemarkMustard';
import IconRemarkNuts from '~/components/icons/remarks/IconRemarkNuts';
import IconRemarkPeanuts from '~/components/icons/remarks/IconRemarkPeanuts';
import IconRemarkSesame from '~/components/icons/remarks/IconRemarkSesame';
import IconRemarkSoybeans from '~/components/icons/remarks/IconRemarkSoybeans';
import IconRemarkSulphur from '~/components/icons/remarks/IconRemarkSulphur';

export const paymentMethodTypes = {
  CASH: 'CASH',
  ALLO_PAY: 'ALLO_PAY',
};

export const ADDED_ITEM_SCREEN_TIMEOUT = 1000;
export const ADDED_DISCOUNT_SCREEN_TIMEOUT = 1000;
export const MENU_CARD_ITEM_CLICK_TIMEOUT = 200;
export const DEFAULT_BUTTON_CLICK_TIMEOUT = 100;

export const TOAST_NOTIFICATION_Z_INDEX = 1104;
export const GLOBAL_LOADING_MODAL_INDEX = 1103;
export const FULL_PAGE_MODAL_INDEX = 1102;
export const BOTTOM_PANEL_Z_INDEX = 1101;
export const BUTTON_DURING_LOADING_SCREEN_Z_INDEX = 1100;

export const routes = {
  INITIALIZE: 'INITIALIZE',
  REGISTER: 'REGISTER',
  IDLE: 'IDLE',
  CREATE: 'CREATE',
  DISCOVER: 'DISCOVER',
  CATEGORIES: 'CATEGORIES',
  ITEM_ADDED: 'ITEM_ADDED',
  PROMOTIONS: 'PROMOTIONS',
  ORDER_SUMMARY: 'ORDER_SUMMARY',
  SUGGESTIONS: 'SUGGESTIONS',
  CHECKOUT: 'CHECKOUT',
  PAYMENT_INSTRUCTIONS: 'PAYMENT_INSTRUCTIONS',
  COMPLETED: 'COMPLETED',
  CHECKOUT_PAYMENT_METHOD: 'CHECKOUT_PAYMENT_METHOD',
};

export const pairedStatus = {
  IS_PAIRED: 'IS_PAIRED',
  NOT_PAIRED: 'NOT_PAIRED',
  NOT_INITIALIZED: 'NOT_INITIALIZED',
};

export const cartStatus = {
  OPEN: 'OPEN',
  PAYING: 'PAYING',
};

export const PAIRING_TOKEN_KEY = '_allO_token';
export const PAIRING_LOCALSTORAGE_KEY = '_allO_pairing_data';
export const CURRENT_ORDER_CART_KEY = '_allo_current_cart';

export const systemLanguages = {
  de: {
    value: 'de',
    label: 'Deutsch',
    flag: <IconFlagGerman />,
  },
  en: {
    value: 'en',
    label: 'English',
    flag: <IconFlagEnglishUS />,
  },
  zh: {
    value: 'zh',
    label: '中文',
    flag: <IconFlagChina />,
  },
  tr: {
    value: 'tr',
    label: 'Türkçe',
    flag: <IconFlagTurkey />,
  },
  vi: {
    value: 'vi',
    label: 'Tiếng Việt',
    flag: <IconFlagVietnam />,
  },
  sq: {
    value: 'sq',
    label: 'Shqip',
    flag: <IconFlagVietnam />,
  },
  pt: {
    value: 'pt',
    label: 'Português',
    flag: <IconFlagVietnam />,
  },
  ko: {
    value: 'ko',
    label: '한국어',
    flag: <IconFlagVietnam />,
  },
  ja: {
    value: 'ja',
    label: '日本語',
    flag: <IconFlagVietnam />,
  },
  hi: {
    value: 'hi',
    label: 'हिंदी',
    flag: <IconFlagVietnam />,
  },
  es: {
    value: 'es',
    label: 'Español',
    flag: <IconFlagVietnam />,
  },
};

export const systemLanguageOptions = Object.values(systemLanguages);

export const defaultLanguageValues = [
  systemLanguages.de.value,
  systemLanguages.en.value,
  systemLanguages.zh.value,
  systemLanguages.tr.value,
];

export const defaultSystemLanguages = [
  systemLanguages.de,
  systemLanguages.en,
  systemLanguages.zh,
  systemLanguages.tr,
];

export const devicePairingModes = {
  NONE: 'none',
  CAMERA: 'camera',
  MANUAL: 'manual',
};

export const onboardingViews = {
  NONE: -1,
  INITIAL: 0,
  NO_CAMERA: 1,
  PIN_VERIFICATION: 2,
  ORDERING_MODE_SELECTION: 3,
  ORDERING_MODE_STEPS: 4,
  NUMBER_OF_PEOPLE: 5,
  SOUP: 6,
  MENU_DISCOVERY: 7,
  MENU_GROUPS: 8,
  MENU_CATEGORIES: 9,
  END_FLOW: 10,
};

export const defaultDishImageSrc = '/dish_default_icon.png';
export const defaultBeverageImageSrc = '/beverage_default_icon.png';

export const resolveItemImage = (menuItem) => {
  const { thumbnailUrl, category } = menuItem || {};
  if (thumbnailUrl) {
    return thumbnailUrl;
  }
  if (category === 'DISH') {
    return defaultDishImageSrc;
  }
  if (category === 'BEVERAGE') {
    return defaultBeverageImageSrc;
  }
};

export const resolveItemVideo = () =>
  'https://ik.imagekit.io/sblaw92as/DE10555SBA-TEST/menu/gallery/Screen%20Recording%202024-11-17%20at%2023.49.46.mov?tr=f-mp4,vc-h264,ac-none';

export const remarkIcons = {
  GLUTEN: {
    value: 'GLUTEN',
    icon: <IconRemarkGluten />,
  },
  CRUSTACEANS: {
    value: 'CRUSTACEANS',
    icon: <IconRemarkCrustaceans />,
  },
  EGGS: {
    value: 'EGGS',
    icon: <IconRemarkEggs />,
  },
  FISH: {
    value: 'FISH',
    icon: <IconRemarkFish />,
  },
  PEANUTS: {
    value: 'PEANUTS',
    icon: <IconRemarkPeanuts />,
  },
  SOYBEANS: {
    value: 'SOYBEANS',
    icon: <IconRemarkSoybeans />,
  },
  MILK: {
    value: 'MILK',
    icon: <IconRemarkMilk />,
  },
  NUTS: {
    value: 'NUTS',
    icon: <IconRemarkNuts />,
  },
  CELERY: {
    value: 'CELERY',
    icon: <IconRemarkCelery />,
  },
  MUSTARD: {
    value: 'MUSTARD',
    icon: <IconRemarkMustard />,
  },
  SESAME: {
    value: 'SESAME',
    icon: <IconRemarkSesame />,
  },
  SULPHUR: {
    value: 'SULPHUR',
    icon: <IconRemarkSulphur />,
  },
  LUPINS: {
    value: 'LUPINS',
    icon: <IconRemarkLupins />,
  },
  MOLLUSKS: {
    value: 'MOLLUSKS',
    icon: <IconRemarkMollusks />,
  },
  DEFAULT: {
    icon: <IconWarning />,
  },
};

export const baseUrl = `${env('NEXT_PUBLIC_BASE_URL') || process.env.NEXT_PUBLIC_BASE_URL || ''}`;

export const POSTHOG_ENABLED =
  env('NEXT_PUBLIC_POSTHOG_ENABLED') ||
  process.env.NEXT_PUBLIC_POSTHOG_ENABLED ||
  'false';

export const defaultMerchantIconUrl = '/merchant_default_icon.png';

export const cdnUrl =
  env('NEXT_PUBLIC_ALLO_CDN_URL') || process.env.NEXT_PUBLIC_ALLO_CDN_URL || '';

export const diningOptionValues = {
  IN_HOUSE: 'IN_HOUSE',
  TO_GO: 'TO_GO',
};

export const mediaBaseUrl = `${cdnUrl}/c/p/twicpics/`;

export const transformMediaUrl = (url) => {
  if (!url) {
    return null;
  }
  return `${mediaBaseUrl}${url?.replace(
    'https://storage.googleapis.com/leviee_public/',
    ''
  )}`;
};

export const transformItemToData = (item) => {
  try {
    const extrasArray = item.extras || [];
    const optionsArray = item.options || [];

    const selectedExtras = {};
    extrasArray.forEach((extra) => {
      const items = {};
      extra.items.forEach((item) => {
        items[item.id] = {
          qtd: item.qtd | 0,
          id: item.id,
          unitPrice: item.unitPrice,
        };
      });
      selectedExtras[extra.id] = { ...items };
    });

    const selectedOptions = {};
    optionsArray.forEach((option) => {
      const items = {};
      option.items.forEach((item) => {
        items[item.id] = {
          qtd: item.qtd | 0,
          id: item.id,
          unitPrice: item.unitPrice,
        };
      });
      selectedOptions[option.id] = { ...items };
    });
    return {
      id: item.id,
      code: item.code,
      cartItemId: item.cartItemId,
      notes: item.notes || '',
      qtd: item.qtd,
      selectedExtras: selectedExtras,
      selectedNotes: [],
      selectedOptions: selectedOptions,
    };
  } catch (_ignore) {
    return {};
  }
};

export const setItemDefaultOptions = (item) => {
  try {
    const data = JSON.parse(JSON.stringify(item));
    if (data && data.options) {
      for (let i = 0; i < data.options.length; i++) {
        if (data.options[i].qtd === 1 && !isEmpty(data.options[i].items)) {
          if (!isEmpty(data.options[i].items[0])) {
            data.options[i].items[0].qtd = 1;
          }
        }
      }
    }

    return data;
  } catch (_ignore) {
    return item;
  }
};

export const calculateItemTotalWithAdditions = (item) => {
  try {
    const { qtd, unitPrice, options = [], extras = [] } = item;
    let optionsPrice = 0;
    options.forEach((option) => {
      option.items.forEach((item) => {
        let localPrice = item.unitPrice;
        let localQtd = item.qtd | 0;
        optionsPrice = optionsPrice + localPrice * localQtd;
      });
    });

    let extrasPrice = 0;
    extras.forEach((extra) => {
      extra.items.forEach((item) => {
        let localPrice = item.unitPrice;
        let localQtd = item.qtd | 0;
        optionsPrice = optionsPrice + localPrice * localQtd;
      });
    });
    return (unitPrice + optionsPrice + extrasPrice) * (qtd || 1);
  } catch (_ignore) {
    return 0;
  }
};

export const getItemSelectedOptions = (item) => {
  try {
    if (!item || !item.options) {
      return null;
    }

    const selectedOptions = {};
    item.options.forEach((option) => {
      const items = {};
      option.items.forEach((item) => {
        if (item.qtd) {
          items[item.id] = {
            qtd: item.qtd | 0,
            id: item.id,
            unitPrice: item.unitPrice,
          };
        }
      });
      if (!isEmpty(items)) {
        selectedOptions[option.id] = { ...items };
      }
    });
    return selectedOptions;
  } catch (_ignore) {
    return null;
  }
};
export const getItemSelectedExtras = (item) => {
  try {
    if (!item || !item.extras) {
      return null;
    }

    const selectedExtras = {};
    item.extras.forEach((extra) => {
      const items = {};
      extra.items.forEach((item) => {
        if (item.qtd) {
          items[item.id] = {
            qtd: item.qtd | 0,
            id: item.id,
            unitPrice: item.unitPrice,
          };
        }
      });
      if (!isEmpty(items)) {
        selectedExtras[extra.id] = { ...items };
      }
    });
    return selectedExtras;
  } catch (_ignore) {
    return null;
  }
};

export const hydrateMenuItemWithCartItem = (menuItem, cartItem) => {
  if (!menuItem) {
    return {};
  }
  if (!cartItem) {
    return menuItem;
  }
  try {
    const selectedOptions = getItemSelectedOptions(cartItem);
    const selectedExtras = getItemSelectedExtras(cartItem);
    menuItem.qtd = cartItem.qtd;
    if (!isEmpty(selectedOptions) && !isEmpty(menuItem.options)) {
      menuItem.options.forEach((option) => {
        if (!isEmpty(selectedOptions[option.id]) && !isEmpty(option.items)) {
          option.items.forEach((item) => {
            if (selectedOptions[option.id][item.id]) {
              item.qtd = selectedOptions[option.id][item.id].qtd || 0;
            }
          });
        }
      });
    }
    if (!isEmpty(selectedExtras) && !isEmpty(menuItem.extras)) {
      menuItem.extras.forEach((extra) => {
        if (!isEmpty(selectedExtras[extra.id]) && !isEmpty(extra.items)) {
          extra.items.forEach((item) => {
            if (selectedExtras[extra.id][item.id]) {
              item.qtd = selectedExtras[extra.id][item.id].qtd || 0;
            }
          });
        }
      });
    }
    menuItem.cartItemId = cartItem.id;
    return menuItem;
  } catch (_ignore) {
    return menuItem;
  }
};

export * from './services';
