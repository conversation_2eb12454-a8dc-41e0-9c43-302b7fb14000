import { default as de } from './translations/de.json' with { type: 'json' };
import { default as en } from './translations/en.json' with { type: 'json' };
import { default as es } from './translations/es.json' with { type: 'json' };
import { default as hi } from './translations/hi.json' with { type: 'json' };
import { default as ja } from './translations/ja.json' with { type: 'json' };
import { default as ko } from './translations/ko.json' with { type: 'json' };
import { default as pt } from './translations/pt.json' with { type: 'json' };
import { default as sq } from './translations/sq.json' with { type: 'json' };
import { default as tr } from './translations/tr.json' with { type: 'json' };
import { default as vi } from './translations/vi.json' with { type: 'json' };
import { default as zh } from './translations/zh.json' with { type: 'json' };

import { isEmpty } from '@monorepo/utils';

export const i18n = { en, de, tr, zh, vi, ja, ko, pt, es, hi, sq };

export const getI18n = (locale, text, vars) => {
  const resolvedTranslation = i18n?.[locale]?.[text];
  if (!resolvedTranslation) {
    return `${locale}-${text}`;
  }

  let finalTranslation = resolvedTranslation;
  if (vars && !isEmpty(vars)) {
    Object.keys(vars).map((key) => {
      let resolvedKey;
      resolvedKey = vars[key];
      finalTranslation = finalTranslation.replace(`{{${key}}}`, resolvedKey);
    });
  }

  return finalTranslation;
};
