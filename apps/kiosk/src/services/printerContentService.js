import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { baseUrl, PAIRING_TOKEN_KEY } from '~/lib/constants';
import { X_ALLO_ORDERING_DEVICE } from '../lib/constants';

const orderApiController = `gluttony-api/restaurants`;

export const printerContentApi = createApi({
  reducerPath: 'printerContentApi',
  baseQuery: fetchBaseQuery({
    baseUrl: `${baseUrl}/${orderApiController}`,
    prepareHeaders: (headers, { getState }) => {
      const state = getState();
      const pairingToken = localStorage.getItem(PAIRING_TOKEN_KEY);
      const _deviceId = state?.device?.id;
      headers.set(X_ALLO_ORDERING_DEVICE, pairingToken);
      return headers;
    },
  }),
  entityTypes: ['PrinterContent'],

  endpoints: (build) => ({
    getPrinterContent: build.query({
      query: (args) => {
        const { restaurantId, deviceId } = args;
        return {
          url: `/${restaurantId}/printing-queue/next/ordering-device?orderingDeviceId=${deviceId}`,
        };
      },
      keepUnusedDataFor: 0.0001,
      provides: ['PrinterContent'],
    }),
  }),
});

export const { useGetPrinterContentQuery } = printerContentApi;
