import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { baseUrl, CURRENT_ORDER_CART_KEY, PAIRING_TOKEN_KEY } from '~/lib/constants';
import { X_ALLO_CART, X_ALLO_ORDERING_DEVICE } from '../lib/constants';

const restaurantApiController = `restaurant-api/ordering-devices`;

export const cartContentApi = createApi({
  reducerPath: 'cartContentApi',
  baseQuery: fetchBaseQuery({
    baseUrl: `${baseUrl}/${restaurantApiController}`,
    prepareHeaders: (headers, { getState }) => {
      const state = getState();
      const pairingToken = localStorage.getItem(PAIRING_TOKEN_KEY);
      const currentCart = localStorage.getItem(CURRENT_ORDER_CART_KEY);
      headers.set(X_ALLO_ORDERING_DEVICE, pairingToken);
      headers.set(X_ALLO_CART, currentCart);
      return headers;
    },
  }),
  endpoints: (builder) => ({
    getCartContent: builder.query({
      query: () => `/_me?withRestaurant=false`,
      keepUnusedDataFor: 0.0001,
    }),
  }),
});

export const { useGetCartContentQuery } = cartContentApi;
