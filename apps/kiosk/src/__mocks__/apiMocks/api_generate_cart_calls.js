import MockAdapter from 'axios-mock-adapter';
import { api } from '~/lib/utils/api';

export const apiGenerateCartResult = () => ({
  id: '679a36975db7ad7283583a1e',
  createdAt: '2025-01-29T14:09:27.840207219Z',
  modifiedAt: '2025-01-29T14:09:27.840207219Z',
  status: 'OPEN',
  restaurantId: '62b1b639230d5d186d059699',
  total: 0.0,
  customer: {
    id: '679a36975db7ad7283583a1d',
    creationTime: '2025-01-29T14:09:27.840+00:00',
    modificationTime: '2025-01-29T14:09:27.840+00:00',
    firstName: 'aox-kiosk-final-day',
    lastName: 'Device',
    restaurantIds: ['62b1b639230d5d186d059699'],
    orderingDeviceId: '01JJBXF0M5BFNXHA9M3GZDD6YK',
  },
  totalDiscounts: 0.0,
  itemsTotal: 0.0,
});

export const mockNetworkResponseGenerateCart = () => {
  const mock = new MockAdapter(api);
  const responseData = apiGenerateCartResult();
  mock
    .onPost(`/gluttony-api/ordering-devices/order-carts/_generate-new-cart`)
    .reply(200, responseData);
};
