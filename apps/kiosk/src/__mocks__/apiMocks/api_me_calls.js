import MockAdapter from 'axios-mock-adapter';
import { api } from '~/lib/utils/api';

export const meCallWithNoCartResults = () => ({
  id: '01JJBXF0M5BFNXHA9M3GZDD6YK',
  name: 'aox-kiosk-final-day',
  type: 'KIOSK',
  paired: true,
  restaurant: {
    id: '62b1b639230d5d186d059699',
    serializationType: 'DE',
    name: 'SEEN Restaurant 1',
    nameI18n: {},
    descriptionI18n: {},
    timezone: 'Europe/Berlin',
    logoUrl: '',
    hasPickup: false,
    hasDelivery: false,
    hasScan: false,
    mode: 'LIVE',
    isOpen: false,
  },
  kioskConfiguration: {
    languages: ['en', 'de', 'zh', 'es'],
    paymentMethods: ['CASH'],
    heroImageUrl:
      'https://storage.googleapis.com/leviee_public/restaurants/62b1b639230d5d186d059699/images/01JHNN90HJ06ZABTGX8YV1WFCX.png',
    topImageUrl:
      'https://storage.googleapis.com/leviee_public/restaurants/62b1b639230d5d186d059699/images/01JJ2ECRYJWHCYP92ME72XDT89.png',
    allowPromotions: true,
    allowGiftCards: false,
  },
});

export const meCallWithCartResults = () => ({
  id: '01JJBXF0M5BFNXHA9M3GZDD6YK',
  name: 'aox-kiosk-final-day',
  type: 'KIOSK',
  paired: true,
  restaurant: {
    id: '62b1b639230d5d186d059699',
    serializationType: 'DE',
    name: 'SEEN Restaurant 1',
    nameI18n: {},
    descriptionI18n: {},
    timezone: 'Europe/Berlin',
    logoUrl: '',
    hasPickup: false,
    hasDelivery: false,
    hasScan: false,
    mode: 'LIVE',
    isOpen: false,
  },
  cart: {
    id: '679a36975db7ad7283583a1e',
    status: 'OPEN',
    restaurantId: '62b1b639230d5d186d059699',
    total: 0.0,
    itemsTotal: 0.0,
    totalDiscounts: 0.0,
    customer: {
      id: '679a36975db7ad7283583a1d',
      creationTime: '2025-01-29T14:09:27.84Z',
      modificationTime: '2025-01-29T14:09:27.84Z',
      firstName: 'aox-kiosk-final-day',
      lastName: 'Device',
      restaurantIds: ['62b1b639230d5d186d059699'],
    },
  },
  kioskConfiguration: {
    languages: ['en', 'de', 'zh', 'es'],
    paymentMethods: ['CASH'],
    heroImageUrl:
      'https://storage.googleapis.com/leviee_public/restaurants/62b1b639230d5d186d059699/images/01JHNN90HJ06ZABTGX8YV1WFCX.png',
    topImageUrl:
      'https://storage.googleapis.com/leviee_public/restaurants/62b1b639230d5d186d059699/images/01JJ2ECRYJWHCYP92ME72XDT89.png',
    allowPromotions: true,
    allowGiftCards: false,
  },
});

export const mockNetworkResponseMeWithoutCart = () => {
  const mock = new MockAdapter(api);
  const responseData = meCallWithNoCartResults();
  mock
    .onGet(`/restaurant-api/ordering-devices/_me?withRestaurant=true`)
    .reply(200, responseData);
};

export const mockNetworkResponseMeWithCart = () => {
  const mock = new MockAdapter(api);
  const responseData = meCallWithCartResults();
  mock
    .onGet(`/restaurant-api/ordering-devices/_me?withRestaurant=true`)
    .reply(200, responseData);
};
