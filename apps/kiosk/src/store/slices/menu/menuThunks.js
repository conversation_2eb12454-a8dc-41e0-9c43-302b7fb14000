import { createAsyncThunk } from '@reduxjs/toolkit';
import { RESTAURANT_API } from '~/lib/constants/services';
import { api } from '~/lib/utils/api';

export const getMenuGroups = createAsyncThunk('menu/groups', () =>
  api.get(`/${RESTAURANT_API}/menu-groups?limit=50`)
);

export const getMenuCategories = createAsyncThunk('menu/versioned-menu', (version) =>
  api.get(`/${RESTAURANT_API}/versioned-menus`, {
    params: { version },
  })
);
