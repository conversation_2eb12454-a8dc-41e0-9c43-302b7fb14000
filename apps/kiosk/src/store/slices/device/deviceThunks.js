import { createAsyncThunk } from '@reduxjs/toolkit';
import { ORDER_API, RESTAURANT_API } from '~/lib/constants/services';
import { api } from '~/lib/utils/api';

export const initializeDevice = createAsyncThunk(
  'device/_me',
  (_, { dispatch: _dispatch }) => {
    return api.get(`/${RESTAURANT_API}/_me?withRestaurant=true`, {
      params: {
        headers: ['cart'],
      },
    });
  }
);

export const cancelPayment = createAsyncThunk(
  'device/_cancel-payment',
  (_, { dispatch: _dispatch }) => {
    return api.post(
      `/${ORDER_API}/current/_cancel-payment`,
      {},
      {
        params: {
          headers: ['cart'],
        },
      }
    );
  }
);

export const resetDevice = createAsyncThunk(
  'device/_reset',
  (_, { dispatch: _dispatch }) => {
    return api.post(
      `/${ORDER_API}/current/reset`,
      {},
      {
        params: {
          headers: ['cart'],
        },
      }
    );
  }
);

export const registerDevice = createAsyncThunk('device/_pair', (arg) =>
  api.put(`/${RESTAURANT_API}/${arg?.serialNumber}/_pair`, { code: arg?.pairingCode }, {})
);
