import { createAsyncThunk } from '@reduxjs/toolkit';
import { ORDER_API } from '~/lib/constants/services';
import { api } from '~/lib/utils/api';
import { createAppSlice } from '~/store/createAppSlice';

const initialState = {};

export const checkoutInitialState = initialState;

export const requestPayment = createAsyncThunk('checkout/payment', (arg) => {
  return api.post(
    `/${ORDER_API}/current/_request-payment`,
    {
      paymentChannel: arg?.method,
    },
    {
      params: {
        headers: ['cart'],
      },
    }
  );
});

export const checkoutSlice = createAppSlice({
  name: 'checkout',
  initialState,
  reducers: (_create) => ({
    // setOpen: create.reducer((state, data) => {
    //   state.open = data?.payload;
    // }),
  }),
  extraReducers: (_builder) => {
    // builder.addCase(initializeDevice.fulfilled, (state, action) => {
    //   const { data } = action?.payload;
    //   state.cartHasItems = !isEmpty(data?.cart?.items);
    // });
  },
  selectors: {
    selectCartHasItems: (state) => state.cartHasItems,
  },
});

export const { selectCartHasItems } = checkoutSlice.selectors;
