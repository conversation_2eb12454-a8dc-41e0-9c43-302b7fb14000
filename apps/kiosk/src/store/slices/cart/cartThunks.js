import { createAsyncThunk } from '@reduxjs/toolkit';

import { ORDER_API, RESTAURANT_API } from '~/lib/constants/services';
import { api } from '~/lib/utils/api';

export const generateCart = createAsyncThunk('cart/_generate', (arg) =>
  api.post(`/${ORDER_API}/_generate-new-cart`, { toGo: arg?.toGo }, {})
);

export const refreshCart = createAsyncThunk('cart/_refresh', () => {
  return api.get(`/${RESTAURANT_API}/_me?withRestaurant=false`, {
    params: {
      headers: ['cart'],
    },
  });
});

export const addPromoCode = createAsyncThunk(
  'cart/_add-promo-code',
  (arg, { getState: _getState }) => {
    return api.post(
      `/${ORDER_API}/current/coupons`,
      { promoCode: arg?.promoCode },
      {
        params: {
          headers: ['cart'],
        },
      }
    );
  }
);
