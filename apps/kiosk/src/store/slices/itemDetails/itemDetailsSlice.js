import { isEmpty } from '@monorepo/utils';
import {
  calculateItemTotalWithAdditions,
  getItemSelectedExtras,
  getItemSelectedOptions,
  hydrateMenuItemWithCartItem,
  setItemDefaultOptions,
} from '~/lib/constants';
import { byId } from '~/lib/utils';
import { createAppSlice } from '~/store/createAppSlice';
import { resetDevice } from '~/store/slices/device/deviceSlice';
import { getMenuCategories } from '~/store/slices/menu/menuSlice';
import { addItem, updateItem } from './itemDetailsThunks';

const initialState = {
  loading: false,
  item: null,
  additions: [],
  selectedExtras: {},
  selectedOptions: {},
  qtd: 1,
  total: 0,
  showItemAddedConfirmationScreen: false,
  isSuggestion: false, // key set when item we are adding is a suggestion
  menuItemsByCode: {}, // do not reset with initialState
  fulfilled: false, // Sum of fulfilled in additions
  unfulfilledAdditionsIds: [],
};

export const itemDetailsInitialState = initialState;

const getInitialState = (state) => {
  if (!state) return initialState;
  return { ...initialState, menuItemsByCode: state.menuItemsByCode };
};

const calculateRootFulfilled = (state) => {
  let fulfilled = true;
  const unfulfilledAdditionsIds = [];

  state.item?.options?.forEach?.((option) => {
    if (option.fulfilled === false) {
      unfulfilledAdditionsIds.push(option.id);
      fulfilled = false;
    }
  });

  // state.item?.extras?.forEach?.((extra) => {
  //   if (extra.fulfilled === false) {
  //     unfulfilledAdditionsIds.push(extra.id);
  //     fulfilled = false;
  //   }
  // });

  return {
    fulfilled,
    unfulfilledAdditionsIds,
  };
};

const calculateFulfilledInOptionsExtras = (state) => {
  if (state.item && state.item.options && state.item.options.length > 0) {
    // Calculate the fulfilled flag on the appropriate options
    state.item.options = state.item.options.map((option) => {
      const tempOption = { ...option };
      if (tempOption.qtd === 1) {
        return tempOption; // We dont care about this one.
      } else {
        const fulfilledOptionItemsQtd = tempOption.items.reduce((acc, item) => {
          acc += item.qtd || 0;
          return acc;
        }, 0);
        tempOption['fulfilled'] = tempOption.qtd === fulfilledOptionItemsQtd;
      }
      return tempOption;
    });
  }

  return state.item;
};

export const itemDetailsSlice = createAppSlice({
  name: 'itemDetails',
  initialState,
  reducers: (create) => ({
    setItem: create.reducer((state, data) => {
      const payload = data?.payload;
      if (!payload) {
        return getInitialState(state);
      } else {
        const isCartItem = !!payload.cartItemId;
        let menuItem = payload;
        // check if menu item or cart item
        if (isCartItem) {
          if (isEmpty(state.menuItemsByCode)) {
            console.log('--missing menu items by code--');
            return state;
          }
          if (!menuItem.code) return state;
          if (isEmpty(state.menuItemsByCode[menuItem.code])) return state;
          const nonReferencingMenuItem = JSON.parse(
            JSON.stringify(state.menuItemsByCode[menuItem.code])
          ); //important so menu item is not updated
          menuItem = hydrateMenuItemWithCartItem(nonReferencingMenuItem, payload);
          state.qtd = payload.qtd;
        }

        state.isSuggestion = payload.isSuggestion;

        // set default options
        const menuItemWithDefaultOptions = isCartItem
          ? menuItem
          : setItemDefaultOptions(menuItem);

        // calculate total with default options
        state.total = calculateItemTotalWithAdditions({
          ...menuItemWithDefaultOptions,
          qtd: state.qtd,
        });
        // set selected options
        state.selectedOptions = getItemSelectedOptions(menuItemWithDefaultOptions);
        // set selected extras
        state.selectedExtras = getItemSelectedExtras(menuItemWithDefaultOptions);
        // set item
        state.item = menuItemWithDefaultOptions;

        // collecting all additions for the sidebar nav
        const { options, extras } = menuItemWithDefaultOptions;
        if (!isEmpty(options) || !isEmpty(extras)) {
          state.additions = [].concat(options || []).concat(extras || []);
        }
      }

      state.item = calculateFulfilledInOptionsExtras(state);
      const { fulfilled, unfulfilledAdditionsIds } = calculateRootFulfilled(state);
      state.fulfilled = fulfilled;
      state.unfulfilledAdditionsIds = unfulfilledAdditionsIds;
      return state;
    }),
    updateAdditionItem: create.reducer((state, data) => {
      const { additionType, additionIndex, additionItemIndex, decrement } =
        data?.payload || {};
      if (!additionType || isNaN(additionIndex) || isNaN(additionItemIndex)) {
        return state;
      }

      // option has required-qtd
      // option item has min max
      if (additionType === 'options') {
        // if indexes exist
        if (state?.item?.options[additionIndex]?.items[additionItemIndex]) {
          const option = state.item.options[additionIndex];
          const optionItems = option.items;
          const optionItemsQtd = optionItems.reduce((acc, item) => {
            acc += item.qtd || 0;
            return acc;
          }, 0);
          const optionItem = optionItems[additionItemIndex];
          const optionItemQtd = optionItem.qtd || 0;
          const optionItemMax = optionItem.max;

          // this option makes all option items radio
          if (option.qtd === 1) {
            // if already selected, ignore
            if (option?.items[additionItemIndex]?.qtd) {
              return state;
            }

            // reset all elements
            for (let i = 0; i < option?.items?.length; i++) {
              state.item.options[additionIndex].items[i].qtd = 0;
            }

            // toggle active specific radio
            state.item.options[additionIndex].items[additionItemIndex].qtd = 1;
          }

          // this is checkbox or quantity
          if (option.qtd > 1) {
            // this makes current option item checkbox
            if (optionItemMax === 1) {
              // if limit reached and item is not already selected then return
              if (optionItemsQtd === option.qtd && !optionItemQtd) {
                return state;
              }
              state.item.options[additionIndex].items[additionItemIndex].qtd =
                optionItemQtd ? 0 : 1;
            }

            // this makes current option quantity selector
            if (optionItemMax > 1) {
              if (decrement) {
                state.item.options[additionIndex].items[additionItemIndex].qtd =
                  optionItemQtd ? optionItemQtd - 1 : optionItemQtd;
              } else {
                // if limit reached do not allow any more addition
                if (optionItemsQtd === option.qtd) {
                  return state;
                }
                state.item.options[additionIndex].items[additionItemIndex].qtd =
                  optionItemQtd + 1;
              }
            }

            const fulfilledOptionItemsQtd = state.item.options[
              additionIndex
            ]?.items.reduce((acc, item) => {
              acc += item.qtd || 0;
              return acc;
            }, 0);
            state.item.options[additionIndex].fulfilled =
              option.qtd === fulfilledOptionItemsQtd;
          }
        }
      }

      // extra has max
      // extra item has max
      if (additionType === 'extras') {
        if (state?.item?.extras[additionIndex]?.items[additionItemIndex]) {
          const extra = state.item.extras[additionIndex];
          const extraItems = extra.items;
          const extraItemsQtd = extraItems.reduce((acc, item) => {
            acc += item.qtd || 0;
            return acc;
          }, 0);
          const extraItem = extraItems[additionItemIndex];
          const extraItemQtd = extraItem.qtd || 0;
          const extraItemMax = extraItem.max;

          // this extra makes all items checkboxes
          if (extra.max === 1) {
            // if total reached and item is not selected, do nothing
            if (extraItemsQtd && !extraItemQtd) {
              return state;
            }
            state.item.extras[additionIndex].items[additionItemIndex].qtd = extraItemQtd
              ? 0
              : 1;
          }
          // this is checkbox or quantity
          if (extra.max > 1) {
            if (extraItemMax === 1) {
              // if limit reached and item is not already selected then return
              if (extraItemsQtd === extra.max && !extraItemQtd) {
                return state;
              }
              state.item.extras[additionIndex].items[additionItemIndex].qtd = extraItemQtd
                ? 0
                : 1;
            }
            if (extraItemMax > 1) {
              if (decrement) {
                state.item.extras[additionIndex].items[additionItemIndex].qtd =
                  extraItemQtd ? extraItemQtd - 1 : extraItemQtd;
              } else {
                // if limit reached do not allow any more addition
                if (extraItemsQtd === extra.max) {
                  return state;
                }
                state.item.extras[additionIndex].items[additionItemIndex].qtd =
                  extraItemQtd + 1;
              }
            }
          }

          const fulfilledExtraItemsQtd = state.item.extras[additionIndex]?.items.reduce(
            (acc, item) => {
              acc += item.qtd || 0;
              return acc;
            },
            0
          );
          state.item.extras[additionIndex].fulfilled =
            extra.max === fulfilledExtraItemsQtd;
        }
      }

      const { fulfilled, unfulfilledAdditionsIds } = calculateRootFulfilled(state);
      state.fulfilled = fulfilled;
      state.unfulfilledAdditionsIds = unfulfilledAdditionsIds;

      state.total = calculateItemTotalWithAdditions({
        ...state.item,
        qtd: state.qtd,
      });
      // set selected options
      state.selectedOptions = getItemSelectedOptions(state.item);
      // set selected extras
      state.selectedExtras = getItemSelectedExtras(state.item);
    }),
    setQtd: create.reducer((state, data) => {
      const { qtd } = data?.payload || {};
      state.qtd = qtd;
      state.total = calculateItemTotalWithAdditions({ ...state.item, qtd });
    }),
    hideImageAddedConfirmationScreen: create.reducer((state, _data) => {
      state.showItemAddedConfirmationScreen = false;
    }),
  }),
  extraReducers: (builder) => {
    builder
      .addCase(addItem.pending, (state, _action) => {
        state.loading = true;
      })
      .addCase(addItem.fulfilled, (state, _action) => {
        state = getInitialState(state);
        state.showItemAddedConfirmationScreen = true;
        return state;
      })
      .addCase(addItem.rejected, (_state, _action) => {})
      .addCase(updateItem.pending, (state, _action) => {
        state.loading = true;
      })
      .addCase(updateItem.fulfilled, (state, _action) => {
        state = getInitialState(state);
        state.showItemAddedConfirmationScreen = true;
        return state;
      })
      .addCase(updateItem.rejected, (_state, _action) => {})
      .addCase(resetDevice.fulfilled, (state, _action) => {
        return getInitialState(state);
      })
      .addCase(getMenuCategories.fulfilled, (state, action) => {
        const { data } = action?.payload || {};
        const allItems = (data?.items || []).reduce((acc, next) => {
          acc = (acc || []).concat(next.items || []);
          return acc;
        }, []);
        state.menuItemsByCode = byId(allItems, 'code');
      });
  },
  selectors: {
    selectItem: (itemDetails) => itemDetails.item,
    selectAdditions: (itemDetails) => itemDetails.additions,
    selectSelectedOptions: (itemDetails) => itemDetails.selectedOptions,
    selectSelectedExtras: (itemDetails) => itemDetails.selectedExtras,
    selectQtd: (state) => state.qtd,
    selectTotal: (state) => state.total,
    selectIsSuggestion: (state) => state.isSuggestion,
    selectShowItemAddedConfirmationScreen: (itemDetails) =>
      itemDetails.showItemAddedConfirmationScreen,
    selectMenuItemsByCode: (menu) => menu.menuItemsByCode,
    selectIsItemDetailsEditing: (state) => {
      try {
        console.log('cart item id: ', state.item.cartItemId);
        return !!state.item.cartItemId;
      } catch (_e) {
        return false;
      }
    },
    selectIsFulfilled: (state) => state.fulfilled,
    selectUnfulfilledAdditionsIds: (state) => state.unfulfilledAdditionsIds,
    selectRemarksFromItemCode: (state, code) => {
      const item = state.menuItemsByCode[code];
      if (item) {
        return item.remarks || [];
      } else {
        return [];
      }
    },
  },
});

export const { setItem, updateAdditionItem, setQtd, hideImageAddedConfirmationScreen } =
  itemDetailsSlice.actions;

export const {
  selectItem,
  selectQtd,
  selectTotal,
  selectShowItemAddedConfirmationScreen,
  selectAdditions,
  selectSelectedOptions,
  selectSelectedExtras,
  selectIsSuggestion,
  selectIsItemDetailsEditing,
  selectIsFulfilled,
  selectUnfulfilledAdditionsIds,
  selectRemarksFromItemCode,
} = itemDetailsSlice.selectors;

export * from './itemDetailsThunks';
