import { combineSlices, configureStore } from '@reduxjs/toolkit';
import { PAIRING_LOCALSTORAGE_KEY, PAIRING_TOKEN_KEY } from '~/lib/constants';
import { api } from '~/lib/utils/api';
import { cartContentApi } from '~/services/cartContentService';
import { printerContentApi } from '~/services/printerContentService';
import { listenerMiddleware } from '~/store/listeners';
import { cartInitialState, cartSlice } from '~/store/slices/cart/cartSlice';
import {
  checkoutInitialState,
  checkoutSlice,
} from '~/store/slices/checkout/checkoutSlice';
import { deviceInitialState, deviceSlice } from '~/store/slices/device/deviceSlice';
import { itemDetailsSlice } from '~/store/slices/itemDetails/itemDetailsSlice';
import { menuInitialState, menuSlice } from '~/store/slices/menu/menuSlice';
import { X_ALLO_ORDERING_DEVICE } from '../lib/constants';

export const rootReducer = combineSlices(
  cartSlice,
  deviceSlice,
  menuSlice,
  itemDetailsSlice,
  checkoutSlice
);

const combinedInitialState = {
  cart: cartInitialState,
  device: deviceInitialState,
  menu: menuInitialState,
  checkout: checkoutInitialState,
};

const registrationMiddleware = (_store) => (next) => (action) => {
  if ('device/_pair/fulfilled'.match(action?.type)) {
    console.log('registrationMiddleware', action.type, action.payload);
    localStorage.setItem(PAIRING_TOKEN_KEY, action?.payload?.data?.token);
    localStorage.setItem(PAIRING_LOCALSTORAGE_KEY, JSON.stringify(action?.payload?.data));
    api.defaults.headers.common[X_ALLO_ORDERING_DEVICE] = action?.payload?.data?.token;
  }

  if ('device/_pair/failed'.match(action?.type)) {
    localStorage.removeItem(PAIRING_TOKEN_KEY);
    localStorage.removeItem(PAIRING_LOCALSTORAGE_KEY);
  }

  if ('device/setLanguage'.match(action?.type)) {
    api.defaults.headers.common['content-language'] = action?.payload;
  }

  return next(action);
};

const reHydrateStore = () => {
  if (typeof window === 'undefined') {
    return combinedInitialState;
  }

  const pairingToken = localStorage.getItem(PAIRING_TOKEN_KEY);
  if (pairingToken !== null) {
    api.defaults.headers.common[X_ALLO_ORDERING_DEVICE] = pairingToken;
    return {
      ...combinedInitialState,
      device: { ...deviceInitialState, pairingToken },
    };
  }
  return combinedInitialState;
};

// `makeStore` encapsulates the store configuration to allow
// creating unique store instances, which is particularly important for
// server-side rendering (SSR) scenarios. In SSR, separate store instances
// are needed for each request to prevent cross-request state pollution.
export const makeStore = () => {
  return configureStore({
    reducer: {
      [cartSlice.reducerPath]: cartSlice.reducer,
      [deviceSlice.reducerPath]: deviceSlice.reducer,
      [menuSlice.reducerPath]: menuSlice.reducer,
      [itemDetailsSlice.reducerPath]: itemDetailsSlice.reducer,
      [checkoutSlice.reducerPath]: checkoutSlice.reducer,
      [printerContentApi.reducerPath]: printerContentApi.reducer,
      [cartContentApi.reducerPath]: cartContentApi.reducer,
    },
    // Adding the api middleware enables caching, invalidation, polling,
    // and other useful features of `rtk-query`.
    middleware: (getDefaultMiddleware) => {
      return getDefaultMiddleware({ serializableCheck: false })
        .concat(registrationMiddleware)
        .concat(listenerMiddleware.middleware)
        .concat(printerContentApi.middleware)
        .concat(cartContentApi.middleware);
    },
    preloadedState: reHydrateStore(),
  });
};

export const store = makeStore();
