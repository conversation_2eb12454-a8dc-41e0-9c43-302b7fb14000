import { Box } from '@allo/ui-lib';
import { useEffect } from 'react';
import { SummaryBottomBar } from '~/screens/summary/components/SummaryBottomBar';
import { SummaryItemList } from '~/screens/summary/components/SummaryItemList';
import { SummarySidebar } from '~/screens/summary/components/SummarySidebar';
import { SummaryTopBar } from '~/screens/summary/components/SummaryTopBar';
import { useAppDispatch } from '~/store/hooks';
import { refreshCart } from '~/store/slices/cart/cartSlice';

const Summary = () => {
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(refreshCart());
  }, [dispatch]);

  return (
    <Box
      sx={{
        height: '100%',
        overflow: 'hidden',
        display: 'flex',
        maxWidth: '1080px',
        margin: '0 auto',
        backgroundColor: (theme) => theme.palette.background,
      }}
    >
      <SummarySidebar />
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          flex: 2,
          position: 'relative',
        }}
      >
        <SummaryTopBar />
        <SummaryItemList />
        <SummaryBottomBar />
      </Box>
    </Box>
  );
};

export default Summary;
