import { Box, Button, Typography } from '@allo/ui-lib';
import Image from 'next/image';
import PropTypes from 'prop-types';
import IconBack_28 from '~/components/icons/IconBack_28';
import { routes } from '~/lib/constants';
import { getI18n } from '~/lib/i18n/i18n';
import { useAppDispatch, useAppSelector } from '~/store/hooks';
import { selectCartHasItems } from '~/store/slices/cart/cartSlice';
import {
  selectLanguage,
  selectRestaurant,
  setScreen,
  setShowCancellationScreen,
  toggleAccessibility,
} from '~/store/slices/device/deviceSlice';

export const SummarySidebar = ({ isAddingDiscount }) => {
  const dispatch = useAppDispatch();
  const restaurant = useAppSelector(selectRestaurant);

  const language = useAppSelector(selectLanguage);
  const cartHasItems = useAppSelector(selectCartHasItems);

  const handleBack = () => {
    if (isAddingDiscount) {
      dispatch(setScreen(routes.ORDER_SUMMARY));
    } else {
      dispatch(setScreen(routes.DISCOVER));
    }
  };

  const handleToggleAccessibility = () => {
    dispatch(toggleAccessibility());
  };

  const handleResetCart = () => {
    dispatch(setShowCancellationScreen());
  };

  return (
    <Box
      sx={{
        position: 'relative',
        width: '160px',
        minWidth: '160px',
        borderRight: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        backgroundColor: (theme) => theme?.palette?.highlight,
      }}
    >
      <Box
        sx={{
          height: '160px',
          minHeight: '160px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          borderBottom: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
        }}
      >
        <Image src={restaurant.logoUrl} width={80} height={80} alt={'restaurant logo'} />
      </Box>
      <Button
        variant="none"
        onClick={handleBack}
        sx={{
          height: '80px',
          minHeight: '80px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          borderBottom: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
        }}
      >
        <IconBack_28 />
      </Button>
      <Box sx={{ flex: 1 }} />
      <Box>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            width: '160px',
            height: '80px',
            borderTop: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
          }}
        >
          <Button
            variant={'none'}
            onClick={handleToggleAccessibility}
            disabled
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              width: '100%',
              height: '100%',
              fontFamily: 'inherit',
              opacity: 0.4,
            }}
            _renderTypography={false}
          >
            <Typography sx={{ fontSize: '17px', lineHeight: '104%' }}>
              {getI18n(language, 'accessibility')}
            </Typography>
          </Button>
        </Box>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            width: '160px',
            height: '80px',
            borderTop: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
          }}
        >
          <Button
            variant={'none'}
            onClick={handleResetCart}
            sx={{
              display: 'flex',
              padding: '20px',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              width: '100%',
              height: '100%',
              fontFamily: 'inherit',
            }}
            _renderTypography={false}
          >
            <Typography sx={{ fontSize: '17px', lineHeight: '104%' }}>
              {getI18n(language, cartHasItems ? 'cancel' : 'restart')}
            </Typography>
          </Button>
        </Box>
      </Box>
    </Box>
  );
};

SummarySidebar.propTypes = {
  isAddingDiscount: PropTypes.bool,
};
