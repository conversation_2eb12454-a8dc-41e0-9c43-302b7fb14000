import { Box } from '@allo/ui-lib';
import { SummaryItem } from '~/screens/summary/components/SummaryItem';
import { useAppSelector } from '~/store/hooks';
import { selectCartItems } from '~/store/slices/cart/cartSlice';

export const SummaryItemList = () => {
  const cartItems = useAppSelector(selectCartItems);

  return (
    <Box
      sx={{
        height: '100%',
        overflow: 'auto',
        paddingBottom: '400px',
      }}
    >
      {cartItems.map((item) => (
        <SummaryItem key={`item_${item.id}`} item={item} />
      ))}
    </Box>
  );
};
