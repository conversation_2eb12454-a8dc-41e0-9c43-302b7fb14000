import { Box, Typography } from '@allo/ui-lib';
import PropTypes from 'prop-types';
import { getI18n } from '~/lib/i18n/i18n';
import { useAppSelector } from '~/store/hooks';
import { selectToGo } from '~/store/slices/cart/cartSlice';
import { selectLanguage } from '~/store/slices/device/deviceSlice';

export const SummaryTopBar = ({ isAddingDiscount }) => {
  const isToGo = useAppSelector(selectToGo);
  const language = useAppSelector(selectLanguage);

  return (
    <Box
      sx={{
        height: '160px',
        minHeight: '160px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'start',
        borderBottom: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
      }}
    >
      <Typography sx={{ fontSize: '35px', fontWeight: 500, paddingLeft: '48px' }}>
        {getI18n(
          language,
          isAddingDiscount
            ? 'discount'
            : isToGo
              ? 'your-order-to-go'
              : 'your-order-to-eat-here'
        )}
      </Typography>
    </Box>
  );
};

SummaryTopBar.propTypes = {
  isAddingDiscount: PropTypes.bool,
};
