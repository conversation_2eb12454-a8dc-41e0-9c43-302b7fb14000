import { Box } from '@allo/ui-lib';
import PropTypes from 'prop-types';
import { transformItemToData } from '~/lib/constants';
import { SummaryItemActions } from '~/screens/summary/components/SummaryItemActions';
import { SummaryItemContent } from '~/screens/summary/components/SummaryItemContent';
import { SummaryItemImage } from '~/screens/summary/components/SummaryItemImage';
import { useAppDispatch } from '~/store/hooks';
import {
  removeItemQtd,
  setItem,
  updateItemQtd,
} from '~/store/slices/itemDetails/itemDetailsSlice';

export const SummaryItem = ({ item }) => {
  const dispatch = useAppDispatch();

  const handleAdd = () => {
    const payload = transformItemToData(item);
    payload.qtd = 1;
    dispatch(updateItemQtd({ item: payload }));
  };

  const handleRemove = () => {
    const payload = transformItemToData(item);
    payload.qtd = 1;
    dispatch(removeItemQtd({ item: payload }));
  };

  const handleEdit = () => {
    dispatch(setItem({ ...item, cartItemId: item.id }));
  };

  return (
    <Box>
      <Box
        sx={{
          display: 'flex',
          gap: '16px',
          padding: '24px 48px',
          alignItems: 'start',
          justifyContent: 'space-between',
        }}
      >
        <SummaryItemImage thumbnailUrl={item?.thumbnailUrl} />
        <SummaryItemContent
          code={item?.code}
          id={item?.id}
          name={item?.name}
          optionItems={item?.optionItems}
          extraItems={item?.extraItems}
          handleModify={handleEdit}
        />
        <SummaryItemActions
          value={item?.qtd}
          total={item?.total}
          increment={handleAdd}
          decrement={handleRemove}
        />
      </Box>
    </Box>
  );
};

SummaryItem.propTypes = {
  item: PropTypes.object.isRequired,
};
