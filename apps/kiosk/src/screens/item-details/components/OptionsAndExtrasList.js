import { Box } from '@allo/ui-lib';
import PropTypes from 'prop-types';
import { Addition } from '~/screens/item-details/components/Addition';

export const OptionsAndExtrasList = ({ items, type }) => {
  return (
    <Box>
      {items.map((item, index) => (
        <Addition key={item.id} data={item} type={type} additionIndex={index} />
      ))}
    </Box>
  );
};

OptionsAndExtrasList.propTypes = {
  items: PropTypes.array.isRequired,
  type: PropTypes.string.isRequired,
};
