import { Box, Button, Typography, TypographyVariant } from '@allo/ui-lib';
import { isEmpty } from '@monorepo/utils';
import Image from 'next/image';
import { GlobalLoading } from '~/components/GlobalLoading';
import IconBack_28 from '~/components/icons/IconBack_28';
import IconMinus from '~/components/icons/IconMinus';
import IconPlus from '~/components/icons/IconPlus';
import ImageWithFallback from '~/components/ImageWithFallback';
import {
  BUTTON_DURING_LOADING_SCREEN_Z_INDEX,
  FULL_PAGE_MODAL_INDEX,
  routes,
} from '~/lib/constants';
import { getI18n } from '~/lib/i18n/i18n';
import { formatToLocaleNumber } from '~/lib/utils';
import { OptionsAndExtrasList } from '~/screens/item-details/components/OptionsAndExtrasList';
import { Remarks } from '~/screens/item-details/components/Remarks';
import { useAppDispatch, useAppSelector } from '~/store/hooks';
import { selectCartHasItems } from '~/store/slices/cart/cartSlice';
import {
  selectGlobalLoading,
  selectLanguage,
  selectRestaurant,
  setScreen,
  setShowCancellationScreen,
  toggleAccessibility,
} from '~/store/slices/device/deviceSlice';
import {
  addItem,
  selectAdditions,
  selectIsFulfilled,
  selectIsItemDetailsEditing,
  selectIsSuggestion,
  selectItem,
  selectQtd,
  selectTotal,
  selectUnfulfilledAdditionsIds,
  setItem,
  setQtd,
  updateItem,
} from '~/store/slices/itemDetails/itemDetailsSlice';

export const ItemDetails = () => {
  const dispatch = useAppDispatch();
  const restaurant = useAppSelector(selectRestaurant);
  const language = useAppSelector(selectLanguage);
  const cartHasItems = useAppSelector(selectCartHasItems);

  const item = useAppSelector(selectItem);
  const additions = useAppSelector(selectAdditions);
  const { options = [], extras = [] } = item || {};
  const qtd = useAppSelector(selectQtd);
  const total = useAppSelector(selectTotal);
  const globalLoading = useAppSelector(selectGlobalLoading);
  const isSuggestion = useAppSelector(selectIsSuggestion);
  const isItemEditing = useAppSelector(selectIsItemDetailsEditing);
  const _isItemFulfilled = useAppSelector(selectIsFulfilled);
  const unfulfilledAdditionsIds = useAppSelector(selectUnfulfilledAdditionsIds);
  const handleAddItem = () => {
    // Anchor link to the 1st item on unfulfilled.
    if (unfulfilledAdditionsIds.length > 0) {
      _scrollToHash(unfulfilledAdditionsIds[0]);
    } else {
      if (item.cartItemId) {
        dispatch(updateItem());
      } else {
        dispatch(addItem());
      }
    }
  };

  const handleCloseItem = () => {
    if (isSuggestion) {
      dispatch(setScreen(routes.SUGGESTIONS));
    }
    dispatch(setItem(null));
  };

  const decreaseQtd = () => {
    if (qtd === 1) {
      return;
    }
    dispatch(setQtd({ qtd: qtd - 1 }));
  };

  const increaseQtd = () => {
    const newQtd = qtd + 1;
    dispatch(setQtd({ qtd: newQtd }));
  };

  const handleToggleAccessibility = () => {
    dispatch(toggleAccessibility());
  };

  const handleResetCart = () => {
    dispatch(setShowCancellationScreen());
  };

  if (isEmpty(item)) {
    return null;
  }

  const _scrollToHash = (id) => {
    const element = document.getElementById(id);
    element?.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
      inline: 'nearest',
    });
  };

  return (
    <Box
      sx={{
        zIndex: FULL_PAGE_MODAL_INDEX,
        height: '100vh',
        width: '100%',
        position: 'fixed',
        top: 0,
        left: 0,
      }}
    >
      <Box
        sx={{
          height: '100%',
          overflow: 'auto',
          maxWidth: '1080px',
          margin: '0 auto',
          backgroundColor: (theme) => theme.palette.background,
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            height: '100%',
            overflow: 'hidden',
          }}
        >
          <Box
            sx={{
              width: '160px',
              borderRight: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
              display: 'flex',
              flexDirection: 'column',
              backgroundColor: (theme) => theme.palette.highlight,
            }}
          >
            <Box
              sx={{
                zIndex: BUTTON_DURING_LOADING_SCREEN_Z_INDEX,
                height: '160px',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                borderBottom: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
              }}
            >
              <Image
                src={restaurant.logoUrl}
                width={80}
                height={80}
                alt={'restaurant logo'}
              />
            </Box>
            <Button
              variant="none"
              onClick={handleCloseItem}
              sx={{
                height: '80px',
                minHeight: '80px',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                borderBottom: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
              }}
            >
              <IconBack_28 />
            </Button>
            {additions?.map((addition) => (
              <Button
                onClick={() => _scrollToHash(addition.id)}
                variant={'none'}
                key={`option_menu_${addition?.id}`}
                sx={{
                  padding: '0 16px',
                  height: '160px',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  borderBottom: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
                }}
              >
                <Typography
                  variant={TypographyVariant.medium_27}
                  sx={{
                    fontSize: 19,
                    fontWeight: 400,
                    margin: '0 auto',
                    width: '100%',
                    textAlign: 'center',
                    lineHeight: '104%',
                    letterSpacing: '0.048px',
                  }}
                >
                  {addition?.name}
                </Typography>
              </Button>
            ))}
            <Box sx={{ flex: 1 }} />
            <Box>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  width: '160px',
                  height: '80px',
                  borderTop: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
                }}
              >
                <Button
                  variant={'none'}
                  onClick={handleToggleAccessibility}
                  disabled
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '100%',
                    height: '100%',
                    opacity: 0.4,
                  }}
                >
                  <Typography sx={{ fontSize: '17px', lineHeight: '104%' }}>
                    {getI18n(language, 'accessibility')}
                  </Typography>
                </Button>
              </Box>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  width: '160px',
                  height: '80px',
                  borderTop: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
                }}
              >
                <Button
                  variant={'none'}
                  onClick={handleResetCart}
                  sx={{
                    display: 'flex',
                    padding: '20px',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '100%',
                    height: '100%',
                    fontFamily: (theme) => theme?.typography?.regular_17?.fontFamily,
                  }}
                >
                  <Typography
                    sx={{
                      fontSize: '17px',
                      lineHeight: '104%',
                      fontWeight: '400px',
                    }}
                  >
                    {getI18n(language, cartHasItems ? 'cancel' : 'restart')}
                  </Typography>
                </Button>
              </Box>
            </Box>
          </Box>
          <Box
            sx={{
              flex: 1,
              overflow: 'auto',
              height: 'calc(100% - 160px)',
            }}
          >
            <Box
              sx={{
                zIndex: 3,
                background: (theme) => theme?.palette.background,
              }}
            >
              <Box
                sx={{
                  width: '100%',
                  height: '720px',
                  overflow: 'hidden',
                  flexDirection: 'column',
                  display: 'flex',
                  justifyContent: 'center',
                  // backgroundImage: `url(${item?.thumbnailUrl})`,
                  // backgroundRepeat: "no-repeat",
                  // backgroundPosition: "center",
                  // backgroundSize: "cover",
                  // backgroundAttachment: "fixed",
                }}
              >
                <Box>
                  {item?.thumbnailUrl && (
                    <ImageWithFallback
                      src={item?.thumbnailUrl}
                      height={720}
                      width={920}
                      alt={'itm details'}
                    />
                  )}
                </Box>
              </Box>
              <Box
                sx={{
                  zIndex: 3,
                  flex: 2,
                  height: '160px',
                  minHeight: '160px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'left',
                  padding: '48px',
                  borderBottom: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
                }}
              >
                <Typography
                  variant={TypographyVariant.medium_27}
                  sx={{
                    fontSize: 35,
                    fontWeight: 500,
                    width: '100%',
                    lineHeight: '104%',
                    letterSpacing: '-0.35px',
                  }}
                >
                  {item?.name}
                </Typography>
              </Box>
              {item?.description && (
                <Box
                  sx={{
                    padding: '48px',
                    borderBottom: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
                  }}
                >
                  <Typography
                    variant={TypographyVariant.medium_27}
                    sx={{
                      fontSize: 21,
                      fontWeight: 400,
                      width: '100%',
                      lineHeight: '142%',
                      letterSpacing: '0.053px',
                    }}
                  >
                    {item?.description}
                  </Typography>
                </Box>
              )}
              {item?.remarks && item?.remarks?.length > 0 && (
                <Remarks remarks={item.remarks} />
              )}
              {!isEmpty(options) && (
                <OptionsAndExtrasList items={options} type={'options'} />
              )}
              {!isEmpty(extras) && (
                <OptionsAndExtrasList items={extras} type={'extras'} />
              )}
            </Box>
          </Box>
          <Box
            sx={{
              position: 'absolute',
              bottom: 0,
              left: 160,
              right: 0,
              height: '160px',
              borderTop: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
              backgroundColor: (theme) => theme?.palette?.highlight,
            }}
          >
            <Box
              sx={{
                width: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                maxWidth: '1080px',
                margin: '0 auto',
                gap: '20px',
                backgroundColor: (theme) => theme?.palette?.highlight,
              }}
            >
              <Box
                sx={{
                  height: '106px',
                  marginLeft: '24px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                }}
              >
                <Button
                  className={'button'}
                  onClick={decreaseQtd}
                  disabled={qtd === 1}
                  sx={{
                    width: '112px',
                    height: '106px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderRadius: '16px',
                    border: '1px solid #EFEDEC',
                    boxShadow: '0px 5px 0px #F6F5F4, 0px 6px 0px 0px #EFEDEC',
                    backgroundColor: (theme) => theme.palette.highlight,
                    '&:active': {
                      boxShadow: 'none',
                      border: '1px solid #EFEDEC',
                    },
                    '&:focus': {
                      boxShadow: '0px 5px 0px #F6F5F4, 0px 6px 0px 0px #EFEDEC',
                      border: '1px solid #EFEDEC',
                    },
                  }}
                  variant="secondary"
                >
                  <IconMinus height={'28'} width={'28'} />
                </Button>
                <Typography
                  sx={{
                    width: '80px',
                    fontSize: '25px',
                    fontWeight: '400',
                    lineHeight: 'unset',
                    textAlign: 'center',
                  }}
                >
                  {qtd}
                </Typography>
                <Button
                  className={'button'}
                  onClick={increaseQtd}
                  sx={{
                    width: '112px',
                    height: '106px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderRadius: '16px',
                    border: '1px solid #EFEDEC',
                    boxShadow: '0px 5px 0px #F6F5F4, 0px 6px 0px 0px #EFEDEC',
                    backgroundColor: (theme) => theme.palette.highlight,
                    '&:active': {
                      boxShadow: 'none',
                      border: '1px solid #EFEDEC',
                    },
                    '&:focus': {
                      boxShadow: '0px 5px 0px #F6F5F4, 0px 6px 0px 0px #EFEDEC',
                      border: '1px solid #EFEDEC',
                    },
                  }}
                  variant="secondary"
                >
                  <IconPlus height={'28'} width={'28'} />
                </Button>
              </Box>
              <Box
                sx={{
                  height: '160px',
                  width: '100%',
                  backgroundColor: (theme) => theme?.palette?.highlight,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'end',
                  paddingRight: '24px',
                }}
              >
                <Button
                  className={'button-primary'}
                  sx={{
                    zIndex: BUTTON_DURING_LOADING_SCREEN_Z_INDEX,
                    height: '106px',
                    width: '100%',
                    borderRadius: '16px',
                    border: 'none',
                    boxShadow: '0px 6px 0px #E76444',
                    backgroundColor: '#FF7452',
                    fontFamily: 'inherit',
                    cursor: 'pointer',
                    '&:active': {
                      boxShadow: 'none',
                      border: 'none',
                    },
                    '&:focus': {
                      boxShadow: '0px 6px 0px #E76444',
                      border: 'none',
                    },
                  }}
                  onClick={handleAddItem}
                  variant="none"
                  _renderTypography={false}
                >
                  <Box
                    component="span"
                    sx={{
                      display: 'flex',
                      gap: '8px',
                    }}
                  >
                    {globalLoading ? (
                      <>
                        <div className={'loader'} />
                      </>
                    ) : (
                      <>
                        <Typography
                          sx={{
                            fontSize: '25px',
                            fontWeight: '400',
                            color: (theme) => theme?.palette?.accent_foreground,
                          }}
                        >
                          {isItemEditing
                            ? getI18n(language, 'update-order')
                            : getI18n(language, 'add-to-order')}
                          <span style={{ marginLeft: '6px' }}>{`·`}</span>
                          <span style={{ marginLeft: '6px' }}>
                            €{formatToLocaleNumber(total)}
                          </span>
                        </Typography>
                      </>
                    )}
                  </Box>
                </Button>
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
      <GlobalLoading />
    </Box>
  );
};
