import { Box, Button, Typography } from '@allo/ui-lib';
import { isEmpty } from '@monorepo/utils';
import { useEffect } from 'react';
import { FULL_PAGE_MODAL_INDEX, routes } from '~/lib/constants';
import { getI18n } from '~/lib/i18n/i18n';
import { CategoriesMenuItem } from '~/screens/categories/components/CategoriesMenuItem';
import { useAppDispatch, useAppSelector } from '~/store/hooks';
import { selectLanguage, setScreen } from '~/store/slices/device/deviceSlice';
import { selectSuggestions } from '~/store/slices/menu/menuSlice';

// Suggestions are displayType === "CARD" && !recommended
const Suggestions = () => {
  const dispatch = useAppDispatch();
  const language = useAppSelector(selectLanguage);
  const suggestions = useAppSelector(selectSuggestions);

  useEffect(() => {
    if (isEmpty(suggestions)) {
      dispatch(setScreen(routes.CHECKOUT));
    }
  }, [dispatch, suggestions]);

  const handleGoToCheckout = () => {
    dispatch(setScreen(routes.CHECKOUT));
  };

  const handleGoBack = () => {
    dispatch(setScreen(routes.ORDER_SUMMARY));
  };

  if (isEmpty(suggestions)) {
    return null;
  }

  return (
    <Box
      sx={{
        height: '100%',
        overflow: 'hidden',
        display: 'flex',
        maxWidth: '1080px',
        margin: '0 auto',
        backgroundColor: (theme) => theme.palette.background,
      }}
    >
      hello
      <Box
        sx={{
          zIndex: FULL_PAGE_MODAL_INDEX,
          height: '100vh',
          width: '100%',
          position: 'fixed',
          top: 0,
          left: 0,
        }}
      >
        <Box
          sx={{
            height: '100%',
            overflow: 'auto',
            maxWidth: '1080px',
            margin: '0 auto',
            backgroundColor: (theme) => theme.palette.background,
          }}
        >
          <Box
            sx={{
              marginTop: '320px',
              marginRight: '55px',
              marginLeft: '55px',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Typography
              sx={{
                fontSize: '40px',
                textAlign: 'center',
                fontWeight: 400,
                lineHeight: '42px',
                letterSpacing: '-1.5%',
              }}
            >
              {getI18n(language, 'want-to-add-something-else')}
            </Typography>
            <Box
              sx={{
                marginTop: '24px',
                padding: '48px',
                width: '100%',
              }}
            >
              <Box
                sx={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(3, minmax(auto, 1fr))',
                  columnGap: '20px',
                  rowGap: '20px',
                }}
              >
                {suggestions.map((i) => (
                  <CategoriesMenuItem item={i} key={`item_${i.code}`} isSuggestion />
                ))}
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                <Button
                  className={'button'}
                  variant={'secondary'}
                  onClick={handleGoBack}
                  sx={{
                    width: '112px',
                    height: '106px',
                    marginTop: '24px',
                    boxShadow: '0px 5px 0px #F6F5F4, 0px 6px 0px 0px #EFEDEC',
                    fontFamily: 'inherit',
                    cursor: 'pointer',
                    border: `1px solid #EFEDEC`,
                    borderRadius: '16px',
                    '&:active': {
                      boxShadow: 'none',
                      borderBottom: 'none',
                      border: `1px solid #EFEDEC`,
                    },
                  }}
                  _renderTypography={false}
                >
                  <svg
                    width="28"
                    height="28"
                    viewBox="0 0 28 28"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M13.4247 8.42416L13.8489 7.99989L13.0004 7.15137L12.5761 7.57564L6.57612 13.5758C6.4636 13.6883 6.40039 13.8409 6.40039 14C6.40039 14.1592 6.46361 14.3118 6.57613 14.4243L12.5761 20.4242L13.0004 20.8484L13.8489 19.9999L13.4247 19.5756L8.44889 14.6L20.1253 14.6L20.7253 14.6L20.7253 13.4L20.1253 13.4L8.44895 13.4L13.4247 8.42416Z"
                      fill="#151413"
                    />
                  </svg>
                </Button>
                <Button
                  className={'button'}
                  variant={'secondary'}
                  onClick={handleGoToCheckout}
                  sx={{
                    flex: 1,
                    width: '100%',
                    height: '106px',
                    marginTop: '24px',
                    paddingLeft: '56px',
                    paddingRight: '56px',
                    boxShadow: '0px 5px 0px #F6F5F4, 0px 6px 0px 0px #EFEDEC',
                    fontFamily: 'inherit',
                    cursor: 'pointer',
                    border: `1px solid #EFEDEC`,
                    borderRadius: '16px',
                    '&:active': {
                      boxShadow: 'none',
                      borderBottom: 'none',
                      border: `1px solid #EFEDEC`,
                    },
                  }}
                  _renderTypography={false}
                >
                  <Typography
                    sx={{
                      fontSize: '21px',
                      textAlign: 'center',
                      fontWeight: 400,
                      lineHeight: '22px',
                    }}
                  >
                    {getI18n(language, 'no-thanks')}!
                  </Typography>
                </Button>
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default Suggestions;
