import { Box, Button, Typography, TypographyVariant } from '@allo/ui-lib';
import Image from 'next/image';
import { useEffect } from 'react';
import { routes } from '~/lib/constants';
import { getI18n } from '~/lib/i18n/i18n';
import { useGetCartContentQuery } from '~/services/cartContentService';
import { useAppDispatch, useAppSelector } from '~/store/hooks';
import { setOrderNumber } from '~/store/slices/cart/cartSlice';
import {
  cancelPayment,
  resetDevice,
  selectIsPaymentCanceling,
  selectLanguage,
  selectRestaurant,
  setScreen,
} from '~/store/slices/device/deviceSlice';

const PaymentInstructions = () => {
  const dispatch = useAppDispatch();
  const restaurant = useAppSelector(selectRestaurant);
  const language = useAppSelector(selectLanguage);
  const isPaymentCanceling = useAppSelector(selectIsPaymentCanceling);
  const { data, isFetching } = useGetCartContentQuery(
    {},
    {
      pollingInterval: 2000,
      skipPollingIfUnfocused: true,
    }
  );

  useEffect(() => {
    if (data) {
      if (data?.cart?.status === 'COMPLETED') {
        dispatch(setOrderNumber(data?.cart?.order?.number));
        dispatch(setScreen(routes.COMPLETED));
      }
    }
  }, [data, dispatch, isFetching]);

  const _resetDeviceHandler = () => {
    dispatch(resetDevice());
  };

  const cancelPaymentHandler = () => {
    dispatch(cancelPayment());
  };

  return (
    <Box
      style={{
        height: '100%',
        maxWidth: '1080px',
        margin: '0 auto',
        backgroundColor: (theme) => theme.palette.background,
        position: 'relative',
      }}
    >
      <Image
        style={{
          borderRadius: 'inherit',
          objectFit: 'cover',
          position: 'absolute',
          top: '0px',
          left: '0px',
          width: '100%',
          height: '100%',
        }}
        width={1080}
        height={1920}
        src={'/payment-instructions-image.png'}
        alt={'payment-instruction-image'}
        sizes="100vw"
      />
      {/*<Image*/}
      {/*  style={{*/}
      {/*    position: "absolute",*/}
      {/*    bottom: "80px",*/}
      {/*    right: "80px",*/}
      {/*  }}*/}
      {/*  src={"/arrow-down-image.png"}*/}
      {/*  alt={"arrow-down"}*/}
      {/*  width={113}*/}
      {/*  height={160}*/}
      {/*/>*/}
      <Box
        sx={{
          position: 'absolute',
          left: '80px',
          top: '80px',
        }}
      >
        <Image src={restaurant.logoUrl} width={80} height={80} alt={'restaurant logo'} />
      </Box>
      <Typography
        variant={TypographyVariant.medium_27}
        sx={{
          fontSize: 80,
          fontWeight: 500,
          margin: '0 auto',
          width: 'calc(100% - 80px)',
          textAlign: 'left',
          lineHeight: '106%',
          letterSpacing: '0.16px',
          position: 'absolute',
          top: '218px',
          left: '80px',
        }}
      >
        {getI18n(language, 'follow-instructions-in-terminal-to-complete-payment')}
      </Typography>
      <Image
        style={{
          position: 'absolute',
          bottom: '80px',
          left: '80px',
        }}
        width={338}
        height={40}
        src={'/payment-method-icons.png'}
        alt={'payment methods'}
      />
      <Box
        style={{
          position: 'absolute',
          bottom: '160px',
          left: '80px',
          display: 'flex',
          gap: '16px',
        }}
      >
        <Button
          onClick={cancelPaymentHandler}
          sx={{
            minWidth: '120px',
            height: '72px',
            paddingLeft: '24px',
            paddingRight: '24px',
            marginTop: '40px',
            borderRadius: '16px',
            border: 'none',
            boxShadow: '0px 6px 0px #E76444',
            backgroundColor: '#FF7452',
            fontFamily: 'inherit',
            cursor: 'pointer',
            '&:active': {
              boxShadow: 'none',
              border: 'none',
            },
            '&:focus': {
              boxShadow: '0px 6px 0px #E76444',
              border: 'none',
            },
          }}
          variant="primary"
          _renderTypography={false}
        >
          {isPaymentCanceling ? (
            <div
              style={{
                display: 'block',
                position: 'absolute',
                top: '35%',
                left: 'calc(50% - 12px)',
                width: '25px',
              }}
              className={'loader'}
            />
          ) : (
            <Typography
              sx={{
                fontSize: '21px',
                fontWeight: '400',
                color: (theme) => theme?.palette?.accent_foreground,
              }}
            >
              {getI18n(language, 'cancel-payment-button')}
            </Typography>
          )}
        </Button>
      </Box>
    </Box>
  );
};

export default PaymentInstructions;
