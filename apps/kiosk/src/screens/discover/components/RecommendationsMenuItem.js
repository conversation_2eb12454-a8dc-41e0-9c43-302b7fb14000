import { Box, Button, Typography } from '@allo/ui-lib';
import PropTypes from 'prop-types';
import ImageWithFallback from '~/components/ImageWithFallback';
import { MENU_CARD_ITEM_CLICK_TIMEOUT, routes } from '~/lib/constants';
import { formatToLocaleNumber } from '~/lib/utils';
import { useAppDispatch } from '~/store/hooks';
import { setScreen } from '~/store/slices/device/deviceSlice';
import { setItem } from '~/store/slices/itemDetails/itemDetailsSlice';

const RecommendationsMenuItem = ({ item, isSuggestion }) => {
  const dispatch = useAppDispatch();

  const handleSelectItem = () => {
    setTimeout(() => {
      dispatch(setItem({ ...item, isSuggestion }));
      if (isSuggestion) {
        dispatch(setScreen(routes.ORDER_SUMMARY));
      }
    }, MENU_CARD_ITEM_CLICK_TIMEOUT);
  };

  const thumbnailUrl = item?.thumbnailUrl || null;

  return (
    <Button
      className={'menu-card'}
      variant="none"
      onClick={handleSelectItem}
      sx={{
        fontFamily: 'inherit',
      }}
    >
      <Box
        className={'menu-card-inner'}
        sx={{
          borderRadius: '16px',
          border: '1px solid #EFEDEC',
          boxShadow: '0px 5px 0px #F6F5F4, 0px 6px 0px 0px #EFEDEC',
          backgroundColor: (theme) => theme.palette.highlight,
          padding: '8px',
          width: '100%',
          height: '100%',
          display: 'flex',
          flexDirection: 'row',
        }}
      >
        <Box sx={{ width: '134px' }}>
          <Box
            className={'menu-card-img-container'}
            onClick={handleSelectItem}
            sx={{
              borderRadius: '8px',
              overflow: 'hidden',
              display: 'flex',
              flexDirection: 'column',
              position: 'relative',
              width: '100%',
              height: '134px',
            }}
          >
            <ImageWithFallback
              alt={'image' + item.id}
              src={thumbnailUrl}
              width={134}
              fallbackSrc={'/item-no-image.png'}
              height={134}
            />
          </Box>
        </Box>
        <Box
          sx={{
            padding: '8px',
            marginLeft: '8px',
            marginRight: '8px',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'space-between',
            flex: 1,
          }}
        >
          <Box>
            <Box>
              <Typography
                sx={{
                  fontSize: '17px',
                  lineHeight: 'unset',
                  fontWeight: '400',
                }}
              >
                {item.name}
              </Typography>
            </Box>
            <Box
              sx={{
                marginTop: '4px',
              }}
            >
              <Typography
                sx={{
                  fontSize: '15px',
                  lineHeight: 'unset',
                  fontWeight: '400',
                  color: (theme) => theme?.palette?.foreground_60,
                }}
              >
                {item.description}
              </Typography>
            </Box>
          </Box>
          <Box
            sx={{
              padding: '16px 0px 0px',
            }}
          >
            <Typography
              sx={{
                fontSize: '15px',
                fontWeight: '400',
              }}
            >
              €{formatToLocaleNumber(item?.unitPrice)}
            </Typography>
          </Box>
        </Box>
      </Box>
    </Button>
  );
};

RecommendationsMenuItem.propTypes = {
  item: PropTypes.object.isRequired,
  isSuggestion: PropTypes.bool,
};

export default RecommendationsMenuItem;
