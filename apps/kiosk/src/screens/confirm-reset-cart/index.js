import { Box, Button, Typography } from '@allo/ui-lib';
import Image from 'next/image';
import { FULL_PAGE_MODAL_INDEX } from '~/lib/constants';
import { getI18n } from '~/lib/i18n/i18n';
import { useAppDispatch, useAppSelector } from '~/store/hooks';
import {
  closeCancellationScreen,
  resetDevice,
  selectLanguage,
  selectShowCancellationScreen,
} from '~/store/slices/device/deviceSlice';

const ConfirmResetCart = () => {
  const dispatch = useAppDispatch();
  const language = useAppSelector(selectLanguage);
  const showCancellationScreen = useAppSelector(selectShowCancellationScreen);

  const handleResetCart = () => {
    dispatch(resetDevice());
  };

  const handleBack = () => {
    dispatch(closeCancellationScreen());
  };

  if (!showCancellationScreen) return null;

  return (
    <Box
      sx={{
        zIndex: FULL_PAGE_MODAL_INDEX,
        height: '100vh',
        width: '100%',
        position: 'fixed',
        top: 0,
        left: 0,
      }}
    >
      <Box
        sx={{
          height: '100%',
          overflow: 'auto',
          maxWidth: '1080px',
          margin: '0 auto',
          backgroundColor: (theme) => theme.palette.background,
        }}
      >
        <Box
          sx={{
            marginTop: '430px',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Image
              src={'/question-mark.png'}
              alt={'question-mark-image'}
              width={160}
              height={160}
            />
            <Typography
              sx={{
                marginTop: '72px',
                marginBottom: '56px',
                fontSize: '80px',
                width: '916px',
                textAlign: 'center',
                fontWeight: 400,
                lineHeight: '83px',
              }}
            >
              {getI18n(language, 'are-you-sure-you-want-to-cancel')}
            </Typography>
          </Box>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '16px',
              margin: '0 auto',
              width: '600px',
            }}
          >
            <Button
              className={'button'}
              variant={'secondary'}
              onClick={handleBack}
              sx={{
                flex: 1,
                height: '106px',
                paddingLeft: '56px',
                paddingRight: '56px',
                boxShadow: '0px 5px 0px #F6F5F4, 0px 6px 0px 0px #EFEDEC',
                fontFamily: 'inherit',
                cursor: 'pointer',
                border: `1px solid #EFEDEC`,
                borderRadius: '16px',
                '&:active': {
                  boxShadow: 'none',
                  borderBottom: 'none',
                  border: `1px solid #EFEDEC`,
                },
              }}
              _renderTypography={false}
            >
              <Typography
                sx={{
                  fontSize: '21px',
                  textAlign: 'center',
                  fontWeight: 400,
                  lineHeight: '83px',
                }}
              >
                {getI18n(language, 'no')}
              </Typography>
            </Button>
            <Button
              variant={'none'}
              className={'button-primary'}
              onClick={handleResetCart}
              sx={{
                flex: 1,
                height: '106px',
                paddingLeft: '56px',
                paddingRight: '56px',
                borderRadius: '16px',
                border: 'none',
                boxShadow: '0px 6px 0px #E76444',
                backgroundColor: '#FF7452',
                fontFamily: 'inherit',
                cursor: 'pointer',
                '&:active': {
                  boxShadow: 'none',
                  border: 'none',
                },
                '&:focus': {
                  boxShadow: '0px 6px 0px #E76444',
                  border: 'none',
                },
              }}
              _renderTypography={false}
            >
              <Typography
                sx={{
                  fontSize: '21px',
                  textAlign: 'center',
                  fontWeight: 400,
                  lineHeight: '83px',
                  color: (theme) => theme?.palette?.accent_foreground,
                }}
              >
                {getI18n(language, 'yes')}
              </Typography>
            </Button>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default ConfirmResetCart;
