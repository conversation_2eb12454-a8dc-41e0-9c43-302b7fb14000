import {
  Price,
  ProductCard,
  ProductCardContent,
  ProductCardCover,
  ProductCardDescription,
  ProductCardFooter,
  ProductCardTitle,
} from '@allo/ui';
import PropTypes from 'prop-types';
import ImageWithFallback from '~/components/ImageWithFallback';
import { MENU_CARD_ITEM_CLICK_TIMEOUT, routes } from '~/lib/constants';
import { useAppDispatch } from '~/store/hooks';
import { setScreen } from '~/store/slices/device/deviceSlice';
import { setItem } from '~/store/slices/itemDetails/itemDetailsSlice';

export const CategoriesMenuItem = ({ item, isSuggestion }) => {
  const dispatch = useAppDispatch();

  const handleSelectItem = () => {
    setTimeout(() => {
      dispatch(setItem({ ...item, isSuggestion }));
      if (isSuggestion) {
        dispatch(setScreen(routes.ORDER_SUMMARY));
      }
    }, MENU_CARD_ITEM_CLICK_TIMEOUT);
  };

  const thumbnailUrl = item?.thumbnailUrl || null;

  return (
    <ProductCard
      onClick={handleSelectItem}
      depth
      style={{ cursor: 'pointer', flexBasis: '31.7%', marginBottom: 20 }}
    >
      <ProductCardCover className="h-auto overflow-visible">
        <ImageWithFallback
          style={{
            borderRadius: 'inherit',
            objectFit: 'cover',
            width: '100%',
          }}
          alt={'image' + item.id}
          src={thumbnailUrl}
          width={0}
          sizes="100vw"
          fallbackSrc={'/item-no-image.png'}
          height={342}
        />
      </ProductCardCover>
      <ProductCardContent className="justify-between h-full">
        <ProductCardTitle>
          <p className="line-clamp-2">{item.name}</p>
        </ProductCardTitle>
        <ProductCardDescription>{item.description}</ProductCardDescription>
        <ProductCardFooter>
          <Price amount={item?.unitPrice} />
        </ProductCardFooter>
      </ProductCardContent>
    </ProductCard>
  );
};

CategoriesMenuItem.propTypes = {
  item: PropTypes.object.isRequired,
  isSuggestion: PropTypes.bool,
};
