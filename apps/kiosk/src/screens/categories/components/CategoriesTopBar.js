import { Box, Typography } from '@allo/ui-lib';

export const CategoriesTopBar = ({ title = 'Burgers' }) => {
  return (
    <Box
      sx={{
        flex: 2,
        height: '160px',
        minHeight: '160px',
        borderBottom: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'left',
        padding: '48px',
      }}
    >
      <Typography sx={{ fontSize: '40px', fontWeight: 500 }}>{title}</Typography>
    </Box>
  );
};
