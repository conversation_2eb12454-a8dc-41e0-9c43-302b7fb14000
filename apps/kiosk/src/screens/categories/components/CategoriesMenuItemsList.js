import { Box, Typography } from '@allo/ui-lib';
import React, { useEffect } from 'react';
import { CategoriesMenuItemsGrid } from '~/screens/categories/components/CategoriesMenuItemsGrid';
import { useAppDispatch, useAppSelector } from '~/store/hooks';
import { selectCartHasItems } from '~/store/slices/cart/cartSlice';
import {
  selectSelectedMenuCategoryId,
  selectSelectedVersionedItems,
  setSelectedMenuCategoryId,
} from '~/store/slices/menu/menuSlice';

export const CategoriesMenuItemsList = () => {
  const dispatch = useAppDispatch();
  const checkoutOpen = useAppSelector(selectCartHasItems);
  const selectedVersionedMenuItems = useAppSelector(selectSelectedVersionedItems);
  const selectedMenuCategoryId = useAppSelector(selectSelectedMenuCategoryId);
  const [selectedMenuId, setSelectedMenuId] = React.useState(0);
  const [item, setItem] = React.useState(selectedVersionedMenuItems[selectedMenuId]);

  useEffect(() => {
    if (selectedMenuCategoryId) {
      const indexOfSelectedMenuGroup = selectedVersionedMenuItems.findIndex(
        (item) => item?.id === selectedMenuCategoryId
      );

      const selectedMenuId =
        indexOfSelectedMenuGroup !== -1 ? indexOfSelectedMenuGroup : 0;
      setSelectedMenuId(selectedMenuId);
    }
  }, [selectedMenuCategoryId, selectedVersionedMenuItems]);

  useEffect(() => {
    dispatch(setSelectedMenuCategoryId({ id: item?.id || null }));
  }, [dispatch]);

  useEffect(() => {
    setItem(selectedVersionedMenuItems[selectedMenuId]);
  }, [selectedMenuId, selectedVersionedMenuItems]);

  return (
    <Box
      sx={{
        height: checkoutOpen ? 'calc(100% - 320px)' : 'calc(100% - 160px)',
        overflowY: 'auto',
        padding: '48px',
      }}
    >
      {item && (
        <Box>
          <Box id={item.id} key={`grid_${item.id}`}>
            <Box sx={{ display: 'flex', alignItems: 'baseline', gap: '6px' }}>
              {item?.emoji && (
                <Box component={'span'} sx={{ fontSize: 25 }}>
                  {item?.emoji}
                </Box>
              )}
              <Typography
                sx={{
                  fontSize: '25px',
                  fontWeight: '500',
                  lineHeight: '106%',
                  paddingTop: '8px',
                }}
              >
                {item.title}
              </Typography>
            </Box>
            <CategoriesMenuItemsGrid item={item} />
          </Box>
        </Box>
      )}
    </Box>
  );
};
