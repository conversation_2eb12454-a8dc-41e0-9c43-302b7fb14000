import { Box, Button, Typography } from '@allo/ui-lib';
import { useEffect } from 'react';
import { routes } from '~/lib/constants';
import { getI18n } from '~/lib/i18n/i18n';
import { formatToLocaleNumber } from '~/lib/utils';
import { CategoriesMenuItemsList } from '~/screens/categories/components/CategoriesMenuItemsList';
import { CategoriesSidebar } from '~/screens/categories/components/CategoriesSidebar';
import { CategoriesTopBar } from '~/screens/categories/components/CategoriesTopBar';
import { useAppDispatch, useAppSelector } from '~/store/hooks';
import {
  refreshCart,
  selectCartHasItems,
  selectCartTotalPrice,
} from '~/store/slices/cart/cartSlice';
import { selectLanguage, setScreen } from '~/store/slices/device/deviceSlice';
import { selectSelectedMenuGroup } from '~/store/slices/menu/menuSlice';

const Categories = () => {
  const dispatch = useAppDispatch();
  const selectedMenuGroup = useAppSelector(selectSelectedMenuGroup);

  const cartHasItems = useAppSelector(selectCartHasItems);
  const cartTotal = useAppSelector(selectCartTotalPrice);

  const language = useAppSelector(selectLanguage);

  useEffect(() => {
    dispatch(refreshCart());
  }, [dispatch]);

  useEffect(() => {
    if (!open) {
      dispatch(refreshCart());
    }
  }, [dispatch]);

  const handleCheckout = () => {
    dispatch(setScreen(routes.ORDER_SUMMARY));
  };

  return (
    <Box
      sx={{
        height: '100%',
        overflow: 'auto',
        maxWidth: '1080px',
        margin: '0 auto',
        backgroundColor: (theme) => theme.palette.background,
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          height: '100%',
          overflow: 'hidden',
        }}
      >
        <CategoriesSidebar />
        <Box sx={{ flex: 1, position: 'relative' }}>
          <CategoriesTopBar title={selectedMenuGroup.name} />
          <CategoriesMenuItemsList selectedMenuGroup={selectedMenuGroup} />
          {cartHasItems && (
            <Box
              sx={{
                position: 'absolute',
                bottom: 0,
                left: 0,
                width: '100%',
              }}
            >
              <Box
                sx={{
                  height: '160px',
                  width: '100%',
                  borderTop: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
                  backgroundColor: (theme) => theme?.palette?.highlight,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'end',
                  paddingRight: '24px',
                }}
              >
                <Button
                  sx={{
                    height: '106px',
                    paddingLeft: '56px',
                    paddingRight: '56px',
                    borderRadius: '16px',
                    border: 'none',
                    boxShadow: '0px 6px 0px #E76444',
                    backgroundColor: '#FF7452',
                    fontFamily: 'inherit',
                    cursor: 'pointer',
                    '&:active': {
                      boxShadow: 'none',
                      border: 'none',
                    },
                    '&:focus': {
                      boxShadow: '0px 6px 0px #E76444',
                      border: 'none',
                    },
                  }}
                  onClick={handleCheckout}
                  variant="none"
                  _renderTypography={false}
                >
                  <Box
                    component="span"
                    sx={{
                      display: 'flex',
                      gap: '8px',
                    }}
                  >
                    <Typography
                      sx={{
                        fontSize: '25px',
                        fontWeight: '400',
                        color: (theme) => theme?.palette?.accent_foreground,
                      }}
                    >
                      {getI18n(language, 'checkout')}
                      <span style={{ marginLeft: '6px' }}>{`·`}</span>
                      <span style={{ marginLeft: '6px' }}>
                        €{formatToLocaleNumber(cartTotal)}
                      </span>
                    </Typography>
                  </Box>
                </Button>
              </Box>
            </Box>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default Categories;
