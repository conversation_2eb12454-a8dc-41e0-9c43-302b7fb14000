import { Box, Button, Typography } from '@allo/ui-lib';
import Image from 'next/image';
import { SidebarGeneric } from '~/components/SidebarGeneric';
import { TopBarGeneric } from '~/components/TopBarGeneric';
import { paymentMethodTypes, routes } from '~/lib/constants';
import { getI18n } from '~/lib/i18n/i18n';
import { useAppDispatch, useAppSelector } from '~/store/hooks';
import { requestPayment } from '~/store/slices/checkout/checkoutSlice';
import { selectLanguage, selectPaymentMethods } from '~/store/slices/device/deviceSlice';

export const Checkout = () => {
  const dispatch = useAppDispatch();
  const language = useAppSelector(selectLanguage);
  const paymentMethods = useAppSelector(selectPaymentMethods);

  const handleCardPayment = () => {
    dispatch(
      requestPayment({
        method: paymentMethodTypes.ALLO_PAY,
      })
    );
  };

  const handleCashPayment = () => {
    dispatch(
      requestPayment({
        method: paymentMethodTypes.CASH,
      })
    );
  };

  return (
    <Box
      sx={{
        height: '100%',
        overflow: 'hidden',
        display: 'flex',
        maxWidth: '1080px',
        margin: '0 auto',
        backgroundColor: (theme) => theme.palette.background,
      }}
    >
      <SidebarGeneric backButtonRoute={routes.ORDER_SUMMARY} />
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          flex: 2,
        }}
      >
        <TopBarGeneric title={getI18n(language, 'how-do-you-want-to-pay')} />
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            flex: 2,
            gap: '32px',
            padding: '48px',
          }}
        >
          {paymentMethods.includes('CARD') && (
            <Button
              variant={'none'}
              onClick={handleCardPayment}
              sx={{
                fontFamily: 'inherit',
                flex: 1,
                width: '100%',
                position: 'relative',
                borderRadius: '16px',
                maxHeight: '480px',
              }}
            >
              <Image
                style={{
                  borderRadius: 'inherit',
                  objectFit: 'cover',
                }}
                fill={true}
                sizes="100vw"
                src={'/credit-card-payment-method.png'}
                alt={'credit card'}
              />
              <Typography
                sx={{
                  fontSize: '35px',
                  fontWeight: '400',
                  lineHeight: 'unset',
                  position: 'absolute',
                  top: '32px',
                  left: '32px',
                }}
              >
                {getI18n(language, 'pay-by-card')}
              </Typography>
            </Button>
          )}
          {paymentMethods.includes('CASH') && (
            <Button
              variant={'none'}
              onClick={handleCashPayment}
              sx={{
                fontFamily: 'inherit',
                flex: 1,
                width: '100%',
                position: 'relative',
                borderRadius: '16px',
                maxHeight: '480px',
              }}
            >
              <Image
                style={{
                  borderRadius: 'inherit',
                  objectFit: 'cover',
                }}
                fill={true}
                sizes="100vw"
                src={'/cash-payment-method.png'}
                alt={'cash payment'}
              />
              <Typography
                sx={{
                  fontSize: '35px',
                  fontWeight: '400',
                  lineHeight: 'unset',
                  position: 'absolute',
                  top: '32px',
                  left: '32px',
                }}
              >
                {getI18n(language, 'pay-at-the-cashier')}
              </Typography>
            </Button>
          )}
        </Box>
      </Box>
    </Box>
  );
};
