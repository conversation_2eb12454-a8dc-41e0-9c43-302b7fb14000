import { Box } from '@allo/ui-lib';
import { GLOBAL_LOADING_MODAL_INDEX } from '~/lib/constants';
import { useAppSelector } from '~/store/hooks';
import { selectGlobalLoading } from '~/store/slices/device/deviceSlice';

export const GlobalLoading = () => {
  const globalLoading = useAppSelector(selectGlobalLoading);

  if (!globalLoading) return null;

  return (
    <Box
      sx={{
        zIndex: GLOBAL_LOADING_MODAL_INDEX,
        height: '100vh',
        width: '100%',
        top: 0,
        left: 0,
        background: '#FAFAFA',
        position: 'fixed',
        opacity: '50%',
      }}
    />
  );
};
