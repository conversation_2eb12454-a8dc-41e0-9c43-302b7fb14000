import PropTypes from 'prop-types';

const IconPlus = ({ width = '16', height = '16' }) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7.39997 12.2857V12.8857H8.59997V12.2857V8.59997H12.2857H12.8857V7.39997H12.2857H8.59997V3.71426V3.11426H7.39997V3.71426V7.39997H3.71426H3.11426V8.59997H3.71426H7.39997V12.2857Z"
      fill="#1F0000"
    />
  </svg>
);

IconPlus.propTypes = {
  width: PropTypes.string,
  height: PropTypes.string,
};

export default IconPlus;
