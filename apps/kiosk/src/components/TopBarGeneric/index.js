import { Box, Typography } from '@allo/ui-lib';
import PropTypes from 'prop-types';

export const TopBarGeneric = ({ title }) => {
  return (
    <Box
      sx={{
        height: '160px',
        minHeight: '160px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'start',
        borderBottom: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
      }}
    >
      <Typography sx={{ fontSize: '35px', fontWeight: 500, paddingLeft: '48px' }}>
        {title}
      </Typography>
    </Box>
  );
};

TopBarGeneric.propTypes = {
  title: PropTypes.string.isRequired,
};
