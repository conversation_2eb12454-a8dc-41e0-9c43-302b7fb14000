import { env } from 'next-runtime-env';
import posthog from 'posthog-js';
import { PostHogProvider } from 'posthog-js/react';
import PropTypes from 'prop-types';
import { useEffect } from 'react';
import { POSTHOG_ENABLED } from '~/lib/constants';
import { useAppSelector } from '~/store/hooks';
import { selectCartId } from '~/store/slices/cart/cartSlice';
import {
  selectDeviceId,
  selectDeviceName,
  selectRestaurant,
} from '~/store/slices/device/deviceSlice';

const PostHog = ({ children }) => {
  const restaurant = useAppSelector(selectRestaurant);
  const {
    id: restaurantId,
    name: restaurantName,
    mode: restaurantMode,
  } = restaurant || {};

  const deviceId = useAppSelector(selectDeviceId);
  const deviceName = useAppSelector(selectDeviceName);

  const cartId = useAppSelector(selectCartId);
  const loadPostHog = POSTHOG_ENABLED === 'true';

  useEffect(() => {
    if (!loadPostHog) return;

    if (typeof window !== 'undefined') {
      const posthogkey = env('NEXT_PUBLIC_POSTHOG_KEY');

      posthog.init(posthogkey, {
        api_host: 'https://eu.i.posthog.com',
        person_profiles: 'identified_only',
        autocapture: false,
        capture_pageview: false,
        capture_pageleave: false,
        capture_dead_clicks: false,
        // disable_session_recording: true
      });
    }
  }, [loadPostHog]);

  useEffect(() => {
    if (!loadPostHog) return;

    if (!restaurantId) {
      posthog.reset();
    } else {
      posthog.group('restaurant', restaurantId, {
        restaurantId,
        name: restaurantName,
        mode: restaurantMode,
      });
    }
  }, [loadPostHog, restaurantId, restaurantMode, restaurantName]);

  useEffect(() => {
    if (!loadPostHog) return;
    if (!deviceId) {
      posthog.reset();
    } else {
      posthog.identify(deviceId, { name: deviceName, deviceId });
    }
  }, [deviceId, deviceName, loadPostHog]);

  useEffect(() => {
    if (!loadPostHog) return;
    if (cartId) {
      posthog.capture('cart', {
        cartId,
      });
    }
  }, [cartId, loadPostHog]);

  if (!loadPostHog) {
    return children;
  }

  return <PostHogProvider client={posthog}>{children}</PostHogProvider>;
};

PostHog.propTypes = {
  children: PropTypes.node.isRequired,
};

export default PostHog;
