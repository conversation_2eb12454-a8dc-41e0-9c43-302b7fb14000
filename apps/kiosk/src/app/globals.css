* {
  -webkit-tap-highlight-color: transparent;
  -webkit-user-select: none;
}

html {
  user-select: none;
}

body {
  height: 100vh;
  font-size: 30px;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

:root {
  --background: #ffffff;
  --foreground: #171717;
}

/*@media (prefers-color-scheme: dark) {*/
/*  :root {*/
/*    --background: #0a0a0a;*/
/*    --foreground: #ededed;*/
/*  }*/
/*}*/

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

/* HTML: <div class="loader"></div> */
.loader {
  width: 50px;
  aspect-ratio: 1;
  border-radius: 50%;
  background:
    radial-gradient(farthest-side, #830d0d 93%, #0000) top / 1px 1px no-repeat,
    conic-gradient(#0000 30%, #ffffff);
  -webkit-mask: radial-gradient(farthest-side, #0000 calc(100% - 3px), #000 0);
  animation: l13 1s infinite linear;
}
@keyframes l13 {
  100% {
    transform: rotate(1turn);
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  color: inherit;
  text-decoration: none;
}

@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
}

::-webkit-scrollbar {
  width: 0;
  background: transparent; /* make scrollbar transparent */
  display: none;
}

button:focus {
  outline: none;
  box-shadow: none;
}

input,
select {
  -webkit-user-select: auto !important;
  /*outline: none !important;*/
  /*border-color: inherit;*/
  /*-webkit-box-shadow: none;*/
  /*box-shadow: none;*/
}

textarea:focus,
input:focus {
  -webkit-box-shadow: none;
  box-shadow: none;
  outline: none;
}

/* TWICPICs */
/* required when using the padding-trick */
.isolation {
  overflow: hidden;
  border-radius: inherit;
}
.media,
.placeholder,
.fallback {
  /* reset border, margin and padding */
  border: none;
  margin: 0;
  padding: 0;
  /* preview and final image must stack and fill their container */
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
  border-radius: inherit;
}
.media {
  object-fit: var(--twic-mode, cover);
}
.fallback {
  opacity: 100; /* hides placeholder once image is loaded */
  background: no-repeat;
  background-position: center;
  background-size: contain;
}
.placeholder {
  background: no-repeat;
  background-position: center;
  background-size: var(--twic-mode, cover);
  opacity: 1;
  transition-property: opacity; /* makes transition smooth */
  transition-duration: var(--twic-duration, 400ms); /* makes transition smooth */
  will-change: opacity; /* makes transition smooth */
  border-radius: inherit;
}
.media.twic-done + .placeholder {
  opacity: 0; /* hides placeholder once image is loaded */
}
.media.twic-error + .placeholder {
  opacity: 0; /* hides placeholder once image is loaded */
}

.cls-optimization {
  overflow: hidden; /* allows border-radius for example */
  position: relative;
  padding-top: calc(
    100% / var(--twic-ratio, 1)
  ); /* padding trick : reserves the display size */
  width: 100%;
  border-radius: inherit;
}
.cls-optimization img:not([src]) {
  /* avoid broken images */
  visibility: hidden;
}

/* YOUR OWN CSS */
.a-custom-class {
  --twic-duration: calc(500ms);
  --twic-mode: cover;
  --twic-ratio: calc(4 / 3);
}

video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.menu-card {
}

.menu-card-img-container {
  opacity: 1;
}

.menu-card:active .menu-card-img-container {
  opacity: 0.8;
}

.menu-card:active img {
  transform: scale(1.1);
  transition: all 0.05s;
}

.menu-card:active {
  transform: translateY(4px);
  transition: all 0.05s;
}

.menu-card-inner:active {
  box-shadow: none;
}

.button:active {
  transform: translateY(4px) !important;
  box-shadow: none !important;
  transition: all 0.05s;
  background: #f6f5f4 !important;
}

.button-disabled {
  opacity: 0.5;
}
.button-disabled:active {
  transform: none !important;
  box-shadow: none !important;
}

.button-primary:active {
  transform: translateY(4px) !important;
  box-shadow: none !important;
  transition: all 0.05s;
  background: #e76444 !important;
}

/*.menu-card-image:active {*/
/*    transform: scale(1.3);*/
/*}*/
