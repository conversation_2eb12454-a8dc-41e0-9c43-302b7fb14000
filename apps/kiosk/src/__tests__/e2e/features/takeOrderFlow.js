import { expect } from '@playwright/test';
import { i18n } from '../../../lib/i18n/i18n';
import {
  handlePairDevice,
  selectEnglishLanguage,
  sleep,
  sleepSetTimeout,
} from '../utils';

export const PRODUCT_NAME =
  'Double BBQ Burger EN Double BBQ Burger EN Double BBQ Burger EN Double BBQ Burger EN';

export const handleChooseProduct = async (page, productName) => {
  await page.click('button:has-text("Burgers & BBQ")');
  await expect(page.locator('text=Burgers & BBQ')).toBeVisible();
  await page.click(`p:has-text("${productName}")`);
};

export const selectRequiredOptionsAndAddToOrder = async (page, productName) => {
  await expect(page.locator(`text=${productName}`)).toHaveCount(2);
  await expect(page.getByText('in 1 pot2Required')).toBeVisible();

  await page.click('button:has-text("Sichuan spicy broth🌶️🌶️🌶️")');
  await page.click('button:has-text("Chinese spicy brew🌶️🌶️")');

  await expect(page.locator('button[disabled]')).toHaveCount(6);

  await page.click('text="Add to order"');
};

export const checkOrder = async (page) => {
  await expect(page.locator(`text=${i18n.en['your-order-to-eat-here']}`)).toBeVisible();

  await expect(page.locator('text=Double BBQ Burger')).toBeVisible();

  await expect(page.locator('text=Fries - €1.00')).toBeVisible();
  await expect(page.locator('text=opt 1 - €0.00')).toBeVisible();
  await expect(page.locator('text=Fritz Kola 0,33l - €3.90')).toBeVisible();
  await expect(page.locator('text=Sichuan spicy broth🌶️🌶️🌶️ - €2.00')).toBeVisible();
  await expect(page.locator('text=Chinese spicy brew🌶️🌶️ - €1.00')).toBeVisible();
  await expect(page.locator('text=Chicken - €4.00')).toBeVisible();
};

export const handleAddDiscount = async (page) => {
  await page.click(`button:has-text("${i18n.en['add-discount-code']}")`);

  await page.click(`button:has-text("W")`);
  await sleep(page, 300);
  await page.click(`button:has-text("W")`);
  await sleep(page, 300);
  await page.click(`button:has-text("W")`);
  await sleep(page, 300);

  await page.click(`button:has-text("${i18n.en['add-discount']}")`);
  await sleep(page, 3000);
  await expect(page.getByText('Discount', { exact: true })).toBeVisible();
};

export const handlePOSCheckOrder = async (page, orderNumber) => {
  page.setDefaultTimeout(10000);
  await page.goto('https://app-dev.allo.restaurant/');
  await page.getByText('Deutsch').click();
  await page.getByText('English').click();
  await page.getByText('I have a password').click();
  await page.getByLabel('Username').fill('<EMAIL>');
  await page.getByLabel('Password').fill('Leviee2021!@');
  await page.getByRole('button', { name: 'Continue', exact: true }).click();
  await sleepSetTimeout(5000);
  await page.click('button:has-text("Express")', { timeout: 30000 });
  await sleepSetTimeout(5000);

  const onderLocator = `text=#${orderNumber} PIPELINE-PLAYWRIGHT-DONT-DELETE`;
  await page.click(onderLocator, { timeout: 60000 });
  await expect(page.getByText(PRODUCT_NAME)).toBeVisible();
  await page.click('button:has-text("Mark as ready")', { timeout: 60000 });
  await expect(page.getByText('Mark as ready')).not.toBeVisible();

  await sleepSetTimeout(5000);
  await page.click(onderLocator, { timeout: 60000 });
  await expect(page.getByText(PRODUCT_NAME)).toBeVisible();
  await page.click('button:has-text("Finish")');

  await expect(page.locator(onderLocator)).not.toBeVisible();
};

export const handleTakeOrderFlowByType = async (page, { type }) => {
  await handlePairDevice(page);
  await selectEnglishLanguage(page);

  await page.click(`button:has-text("${i18n.en[type]}")`);

  await expect(page.locator(`text=${i18n.en['explore-our-menu']}`)).toBeVisible({
    timeout: 10000,
  });

  await handleChooseProduct(page, PRODUCT_NAME);

  await sleep(page, 2000);

  await selectRequiredOptionsAndAddToOrder(page, PRODUCT_NAME);

  await sleep(page);

  await page.click(`text=${i18n.en['checkout']}`);

  await sleep(page);

  await checkOrder(page);

  // await handleAddDiscount(page);

  await page.click(`button:has-text("${i18n.en['pay-now']}")`);

  await expect(
    page.locator(`text=${i18n.en['want-to-add-something-else']}`)
  ).toBeVisible();
  await page.click(`button:has-text("${i18n.en['no-thanks']}")`);

  await page.click(`img[alt="credit card"]`);

  await expect(page.locator(`text=${i18n.en['your-order-number-is']}`)).toBeVisible({
    timeout: 20000,
  });

  await page.click(`button:has-text("${i18n.en['finish']}")`);
  const orderNumber = await page.getByTestId('order-number').textContent();

  await handlePOSCheckOrder(page, orderNumber);
};
