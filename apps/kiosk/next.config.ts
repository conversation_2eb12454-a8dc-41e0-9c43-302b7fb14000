import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  output: 'standalone', // important for packageing in docker
  reactStrictMode: false,
  images: {
    dangerouslyAllowSVG: true,
    unoptimized: true,
    remotePatterns: [
      {
        hostname: 'storage.googleapis.com',
      },
      {
        hostname: 'cdn.dev.allo.restaurant',
      },
      {
        hostname: 'cdn.allo.restaurant',
      },
    ],
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
};

export default nextConfig;
