{"name": "kiosk", "version": "0.1.3", "private": true, "type": "module", "scripts": {"dev": "NEXT_PUBLIC_BASE_URL=https://app-dev.allo.restaurant NEXT_PUBLIC_ALLO_CDN_URL=https://cdn.dev.allo.restaurant next dev --turbo --port=3001", "build": "next build", "start": "HOSTNAME=0.0.0.0 NEXT_PUBLIC_BASE_URL=https://app-dev.allo.restaurant NEXT_PUBLIC_ALLO_CDN_URL=https://cdn.dev.allo.restaurant node .next/standalone/apps/kiosk/server.js --port=3000", "start:next": "HOSTNAME=0.0.0.0 NEXT_PUBLIC_BASE_URL=https://app-dev.allo.restaurant NEXT_PUBLIC_ALLO_CDN_URL=https://cdn.dev.allo.restaurant next start --port=3000", "test": "jest", "test:e2e": "DEBUG=pw:webserver NEXT_PUBLIC_ALLO_CDN_URL=https://cdn.dev.allo.restaurant NEXT_PUBLIC_BASE_URL=https://app-dev.allo.restaurant npx playwright@1.50.1 test", "test:e2e:ui": "NEXT_PUBLIC_ALLO_CDN_URL=https://cdn.dev.allo.restaurant NEXT_PUBLIC_BASE_URL=https://app-dev.allo.restaurant npx playwright@1.50.1 test --ui", "test:e2e:debug": "npx playwright@1.50.1 test --debug", "test:watch": "jest --watch", "lint": "next lint", "install-ui-lib": "npx google-artifactregistry-auth ./.npmrc && yarn add @allo/ui-lib@latest", "lint:fix": "eslint --fix \"src/**/*.+(js|jsx)\"", "format": "prettier --write \"src/**/*.+(js|jsx)\""}, "engines": {"node": ">=20 <24", "npm": ">=10"}, "packageManager": "npm@10.8.2", "nodeLinker": "node-modules", "dependencies": {"@allo/ui": "^0.0.29", "@allo/ui-lib": "^1.0.276", "@floating-ui/react": "^0.26.27", "@reduxjs/toolkit": "^2.3.0", "axios": "^1.7.7", "core-js": "3.39.0", "next": "^15.1.0", "next-runtime-env": "^3.2.2", "posthog-js": "^1.207.0", "prop-types": "^15.8.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-redux": "^9.1.2", "sharp": "^0.33.5"}, "devDependencies": {"@monorepo/eslint-prettier-config": "*", "@monorepo/jest-playwright-config": "*", "@monorepo/typescript-config": "*", "@monorepo/utils": "*", "@next/eslint-plugin-next": "^15.2.0", "@playwright/test": "1.50.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/node": "^22.13.1", "axios-mock-adapter": "^2.1.0", "babel-eslint": "^10.1.0", "corepack": "^0.29.4", "eslint": "^9.21.0", "globals": "^15.14.0", "google-artifactregistry-auth": "^3.1.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-fixed-jsdom": "^0.0.9", "jsdom": "^26.0.0", "npm-run-all": "^4.1.5", "prettier": "^3.4.2"}}