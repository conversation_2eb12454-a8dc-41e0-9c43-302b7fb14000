{"name": "webshop", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "NEXT_PUBLIC_BASE_URL=https://app-dev.allo.restaurant NEXT_PUBLIC_ALLO_CDN_URL=https://cdn.dev.allo.restaurant next dev --turbo --port=3000", "build": "next build", "start": "next start", "format": "prettier --write ./src", "lint": "next lint", "types:api": "openapi-typescript https://app-dev.allo.restaurant/webshop-service/v3/api-docs -o src/lib/api/api.d.ts"}, "engines": {"node": ">=20 <24", "npm": ">=10"}, "packageManager": "npm@10.8.2", "nodeLinker": "node-modules", "dependencies": {"@allo/ui": "^0.0.44", "@date-fns/tz": "^1.2.0", "@fontsource-variable/bricolage-grotesque": "^5.2.6", "@fontsource-variable/inter": "^5.2.5", "@vis.gl/react-google-maps": "^1.5.2", "date-fns": "^4.1.0", "date-fns-timezone": "^0.1.4", "date-fns-tz": "^3.2.0", "lucide-react": "^0.476.0", "motion": "^12.16.0", "next": "15.3.1", "next-intl": "^4.1.0", "next-runtime-env": "^3.3.0", "openapi-fetch": "^0.14.0", "posthog-js": "^1.236.5", "react": "^19.0.0", "react-dom": "^19.0.0", "swr": "^2.3.3", "zustand": "^5.0.3"}, "devDependencies": {"@monorepo/eslint-prettier-config": "*", "@monorepo/typescript-config": "*", "@monorepo/utils": "*", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4", "typescript": "^5"}}