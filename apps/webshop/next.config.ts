import type { NextConfig } from 'next';
import createNextIntlPlugin from 'next-intl/plugin';
import { restaurants } from './src/lib/mock-data/restaurants';

const nextConfig: NextConfig = {
  output: 'standalone', // important for packageing in docker
  reactStrictMode: false,
  devIndicators: false,
  images: {
    dangerouslyAllowSVG: true,
    unoptimized: true,
    remotePatterns: [
      {
        hostname: 'storage.googleapis.com',
      },
      {
        hostname: 'cdn.dev.allo.restaurant',
      },
      {
        hostname: 'cdn.allo.restaurant',
      },
      {
        hostname: 'unsplash.com',
      },
      {
        hostname: 'images.unsplash.com',
      },
    ],
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  // TEMP: redirect old route structure to new one
  // Can be removed when integrating with backend
  redirects: async () => {
    return [
      {
        source: '/en/chicken-house',
        destination: '/en/brand/chicken-house',
        permanent: true,
      },
      {
        source: '/en/house-of-food',
        destination: '/en/locations/house-of-food',
        permanent: true,
      },
      ...Object.values(restaurants).flatMap((restaurant) => [
        {
          source: `/en/${restaurant.slug}/:path*`,
          destination: `/en/restaurant/${restaurant.slug}/:path*`,
          permanent: true,
        },
      ]),
    ];
  },
  async rewrites() {
    return [
      // posthog reverse proxy
      {
        source: '/ingest/static/:path*',
        destination: 'https://eu-assets.i.posthog.com/static/:path*',
      },
      {
        source: '/ingest/:path*',
        destination: 'https://eu.i.posthog.com/:path*',
      },
      {
        source: '/ingest/decide',
        destination: 'https://eu.i.posthog.com/decide',
      },
    ];
  },
  // This is required to support PostHog trailing slash API requests
  skipTrailingSlashRedirect: true,
};

const withNextIntl = createNextIntlPlugin();

export default withNextIntl(nextConfig);
