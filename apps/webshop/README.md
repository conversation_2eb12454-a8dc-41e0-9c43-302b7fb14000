# allO Webshop

## Setup Private Registry

This project uses a private npm registry. To set up, follow the `allO GCP access (registry)` note in 1Password, then run:

```bash
npm run setup:registry
```

This will create a local .npmrc file with the necessary authentication token.

Note: If you get authentication errors in the future, just run the setup command again to refresh the token.

## Getting Started

First, run the development server:

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Pre-integration Route Structure

| Route                                     | Description                                                       |
| ----------------------------------------- | ----------------------------------------------------------------- |
| `/[locale]/brand/[slug]`                  | Brand page showing menu of a brand                                |
| `/[locale]/locations/[slug]`              | Multi-brand or brand locations page showing available restaurants |
| `/[locale]/restaurant/[slug]`             | Individual restaurant page                                        |
| `/[locale]/restaurant/[slug]/menu`        | Readonly menu with reservation link                               |
| `/[locale]/restaurant/[slug]/checkout`    | Restaurant checkout page                                          |
| `/[locale]/restaurant/[slug]/orders`      | Restaurant orders                                                 |
| `/[locale]/restaurant/[slug]/orders/[id]` | Restaurant single order                                           |

Note: All routes support localization through the `[locale]` parameter. The project uses `next-intl` for internationalization.
