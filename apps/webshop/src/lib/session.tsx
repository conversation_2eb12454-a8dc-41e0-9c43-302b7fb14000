'use client';

import posthog from 'posthog-js';
import { createContext, use, useCallback, useState } from 'react';
import { User } from './mock-data/user';

type SessionContextType = {
  user: User | null;
  login: (user: User) => void;
  logout: () => void;
};

const SessionContext = createContext<SessionContextType | null>(null);

export const SessionProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);

  const login = useCallback((user: User) => {
    setUser(user);

    posthog.identify(user.id, { user });
  }, []);

  const logout = useCallback(() => {
    setUser(null);

    posthog.reset();
  }, []);

  return <SessionContext value={{ user, login, logout }}>{children}</SessionContext>;
};

export const useSession = () => {
  const session = use(SessionContext);

  if (!session) {
    throw new Error('useSession must be used within a SessionProvider');
  }

  return session;
};
