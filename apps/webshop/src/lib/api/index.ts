import createClient from 'openapi-fetch';
import {
  WebShopCartNewOrderCart,
  WebShopOrderCartAddItemRequest,
} from '~/lib/api/types.ts';
import type { paths } from './api';

export const base = `https://app-dev.allo.restaurant/webshop-service`;

export const baseUrl = `${base}`;

export const client = createClient<paths>({
  baseUrl: baseUrl,
});

export const getRestaurant = async (slug: string) => {
  return client.GET('/v1/restaurants/slug/{slug}', {
    params: {
      path: { slug },
    },
  });
};

export const getRestaurantMenus = async (
  restaurantId: string,
  orderType: 'PICKUP' | 'DELIVERY' | 'DINE_IN' | 'EXPRESS' = 'PICKUP'
) => {
  return client.GET('/v1/restaurants/{restaurantId}/menus', {
    params: {
      path: { restaurantId },
      query: { orderType },
    },
  });
};

export const postCreateCart = async (
  restaurantId: string,
  orderCartRequest: WebShopCartNewOrderCart
) => {
  return client.POST('/v1/restaurants/{restaurantId}/cart', {
    params: {
      path: { restaurantId },
    },
    body: orderCartRequest,
  });
};

export const getCart = async (restaurantId: string, cartId: string) => {
  return client.GET('/v1/restaurants/{restaurantId}/cart', {
    params: {
      path: { restaurantId },
    },
    headers: {
      'X-allO-Cart': cartId,
    },
  });
};

export const postAddItemToCart = async (
  restaurantId: string,
  addItemRequest: WebShopOrderCartAddItemRequest,
  cartId: string
) => {
  return client.POST('/v1/restaurants/{restaurantId}/cart/items', {
    params: {
      path: { restaurantId },
    },
    body: addItemRequest,
    headers: {
      'X-allO-Cart': cartId,
    },
  });
};

export const patchUpdateCartItem = (
  restaurantId: string,
  itemId: string,
  addItemRequest: WebShopOrderCartAddItemRequest,
  cartId: string
) => {
  return client.PATCH(`/v1/restaurants/{restaurantId}/cart/items/{itemId}`, {
    params: {
      path: { restaurantId, itemId },
    },
    body: addItemRequest,
    headers: {
      'X-allO-Cart': cartId,
    },
  });
};

export const deleteCartItem = (restaurantId: string, itemId: string, cartId: string) => {
  return client.DELETE(`/v1/restaurants/{restaurantId}/cart/items/{itemId}`, {
    params: {
      path: { restaurantId, itemId },
    },
    headers: {
      'X-allO-Cart': cartId,
    },
  });
};

export const postAddPromotionToCart = (
  restaurantId: string,
  promoCode: string,
  cartId: string
) => {
  return client.POST(`/v1/restaurants/{restaurantId}/cart/promotion`, {
    params: {
      path: { restaurantId },
    },
    body: { promoCode },
    headers: {
      'X-allO-Cart': cartId,
    },
  });
};

export const postRequestCartPayment = (
  restaurantId: string,
  cartId: string,
  paymentChannel: 'CASH' | 'ALLO_PAY_ONLINE' = 'CASH'
) => {
  return client.POST(`/v1/restaurants/{restaurantId}/cart/_request-payment`, {
    params: {
      path: { restaurantId },
    },
    body: { paymentChannel: paymentChannel },
    headers: {
      'X-allO-Cart': cartId,
    },
  });
};

export const getRestaurantFromSlug = (slug: string) => {
  return client.GET(`/v1/restaurants/slug/{slug}`, {
    params: {
      path: { slug },
    },
  });
};

export const getOrderFromOrderId = (restaurantId: string, orderId: string) => {
  return client.GET(`/v1/restaurants/{restaurantId}/orders/{orderId}`, {
    params: {
      path: { restaurantId, orderId },
    },
  });
};

export const getRestaurantOrderStatus = (restaurantId: string, orderId: string) => {
  return client.GET(`/v1/restaurants/{restaurantId}/orders/{orderId}/status`, {
    params: {
      path: { restaurantId, orderId },
    },
  });
};
