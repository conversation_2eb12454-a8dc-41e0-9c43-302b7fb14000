/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
  '/v1/restaurants/{restaurantId}/cart': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get the current cart
     * @description Returns the details of the current webshop cart
     */
    get: operations['getCart'];
    put?: never;
    /**
     * Create a new cart
     * @description Creates a new webshop cart, replacing the old one if any
     */
    post: operations['generateNewCart'];
    delete?: never;
    options?: never;
    head?: never;
    /**
     * Update existing cart
     * @description Updates some details of an existing webshop cart
     */
    patch: operations['updateCart'];
    trace?: never;
  };
  '/v1/restaurants/{restaurantId}/cart/promotion': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Add promotion code to cart
     * @description Adds a promotion code to the current webshop cart
     */
    post: operations['addPromotionToCart'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/restaurants/{restaurantId}/cart/items': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Add item to cart
     * @description Adds an item to the current webshop cart
     */
    post: operations['addItemQtd'];
    /**
     * Reset cart items
     * @description Removes all items from the current webshop cart
     */
    delete: operations['resetItems'];
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/restaurants/{restaurantId}/cart/_request-payment': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Request cart payment
     * @description Requests for the payment of the current webshop cart
     */
    post: operations['requestCartPayment'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/restaurants/{restaurantId}/cart/items/{itemId}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post?: never;
    /**
     * Remove item from cart
     * @description Removes a single item from the current webshop cart
     */
    delete: operations['removeItem'];
    options?: never;
    head?: never;
    /**
     * Update item from cart
     * @description Update a single item from the current webshop cart
     */
    patch: operations['updateItem'];
    trace?: never;
  };
  '/v1/restaurants/{restaurantId}/schedule-times': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Returns schedule times for webshop
     * @description Returns the available schedule times for webshop on a restaurant
     */
    get: operations['getRestaurantScheduleTimes'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/restaurants/{restaurantId}/orders/{orderId}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get an order by ID
     * @description Returns the details of a specific webshop order for the summary page after the checkout
     */
    get: operations['getOrder'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/restaurants/{restaurantId}/orders/{orderId}/status': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get an order status by ID
     * @description Returns the status of a specific webshop order for the summary page after the checkout
     */
    get: operations['getOrderStatus'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/restaurants/{restaurantId}/menus': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Returns the restaurant webshop menus
     * @description Returns the webshop menus of a restaurant
     */
    get: operations['getRestaurantMenus'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/restaurants/{restaurantId}/cart/recommendations': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * List recommendations for cart
     * @description List recommended menu items for the current webshop cart
     */
    get: operations['listRecommendationsForCart'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/restaurants/slug/{slug}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Returns the restaurant
     * @description Returns the details of a restaurant that are needed by the webshop
     */
    get: operations['getRestaurantBySlug'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/restaurants/slug/{slug}/enabled': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Returns the if webshop is enabled
     * @description Returns if the new webshop if enabled for a specific restaurant
     */
    get: operations['getRestaurantEnabledBySlug'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/actuator': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** Actuator root web endpoint */
    get: operations['links'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/actuator/health': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** Actuator web endpoint 'health' */
    get: operations['health'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
}
export type webhooks = Record<string, never>;
export interface components {
  schemas: {
    Address: {
      street?: string;
      number?: string;
      area?: string;
      city?: string;
      /** @enum {string} */
      country?:
        | 'DE'
        | 'BR'
        | 'GR'
        | 'AT'
        | 'DK'
        | 'FR'
        | 'IT'
        | 'ES'
        | 'PT'
        | 'NL'
        | 'CH'
        | 'FI'
        | 'PL'
        | 'CZ'
        | 'HU'
        | 'HR'
        | 'BG'
        | 'RO'
        | 'EE'
        | 'GB'
        | 'US'
        | 'CA'
        | 'BE';
      additionalInfo?: string;
      zipCode?: string;
      /** Format: double */
      lat?: number;
      /** Format: double */
      lng?: number;
    };
    Customer: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      /** @enum {string} */
      title?: 'other' | 'mr' | 'ms';
      firstName?: string;
      lastName?: string;
      address?: components['schemas']['Address'];
      email?: string;
      phone?: string;
      userId?: string;
      billingId?: string;
      company?: string;
      avatar?: string;
      vatId?: string;
      externalCustomerId?: string;
      restaurantIds?: string[];
      tags?: string[];
    };
    NewOrderCartRequest: {
      customer: components['schemas']['Customer'];
      /** @enum {string} */
      orderType?: 'PICKUP' | 'DELIVERY' | 'DINE_IN' | 'EXPRESS';
      notes?: string;
    };
    Account: {
      id?: string;
      userIds?: string[];
      /** Format: date-time */
      createdAt?: string;
      /** Format: date-time */
      modifiedAt?: string;
      createdBy?: string;
      modifiedBy?: string;
      firstName?: string;
      lastName?: string;
      email?: string;
      emailConfirmed?: boolean;
      mobile?: string;
      mobileConfirmed?: boolean;
      /** @enum {string} */
      mode?: 'LIVE' | 'EXPLORATION';
      restaurantIds?: string[];
      organizationIds?: string[];
      permissions?: components['schemas']['Permission'][];
      bypassAllowed?: boolean;
      isManaged?: boolean;
    };
    CategoryConsumption: {
      categoryId?: string;
      name?: string;
      /** @enum {string} */
      unit?: 'G' | 'KG' | 'L' | 'ML' | 'PC' | 'BT';
      amount?: number;
    };
    Course: {
      /** Format: int32 */
      number?: number;
      nameI18n?: {
        [key: string]: string;
      };
    };
    CourseConfig: {
      enabled?: boolean;
      courses?: components['schemas']['Course'][];
    };
    CrmSettings: {
      companyId?: string;
    };
    DailyReportConfig: {
      emails?: string[];
    };
    DatevConfiguration: {
      kasseKontonummer?: string;
      configurationRecords?: components['schemas']['Record'][];
    };
    DeliveryArea: {
      zipCode?: string;
      deliveryFee?: number;
      minOrderValue?: number;
    };
    DeliveryRadius: {
      /** Format: double */
      radiusInKm?: number;
      deliveryFee?: number;
      minOrderValue?: number;
    };
    Discount: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      waiterId?: string;
      orderId?: string;
      orderItemId?: string;
      menuId?: string;
      menuItemCode?: string;
      promotionId?: string;
      code?: string;
      percentage?: number;
      amount?: number;
      restaurantId?: string;
      discountedItem?: components['schemas']['DiscountItem'];
      orderTotal?: number;
    };
    DiscountItem: {
      menuId?: string;
      menuItem?: components['schemas']['DiscountMenuItem'];
      /** Format: int32 */
      qtd?: number;
      unitPrice?: number;
      itemTotal?: number;
    };
    DiscountMenuItem: {
      code?: string;
      name?: string;
    };
    Driver: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      externalId?: string;
      firstName?: string;
      lastName?: string;
      phone?: string;
      phoneCode?: string;
      email?: string;
      vehicle?: components['schemas']['Vehicle'];
      pictureUrl?: string;
    };
    DriverAssignmentLog: {
      /** Format: date-time */
      timestamp?: string;
      action?: string;
      performedBy?: components['schemas']['Account'];
      drivers?: components['schemas']['Driver'][];
    };
    DslCallSettings: {
      callMonitoringEnabled?: boolean;
    };
    Extra: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      restaurantId?: string;
      name?: string;
      nameI18n?: {
        [key: string]: string;
      };
      internalName?: string;
      internalNameI18n?: {
        [key: string]: string;
      };
      description?: string;
      descriptionI18n?: {
        [key: string]: string;
      };
      restricted?: boolean;
      disabled?: boolean;
      hidden?: boolean;
      deleted?: boolean;
      /** Format: int32 */
      order?: number;
      total?: number;
      /** Format: int32 */
      max?: number;
      /** Format: int32 */
      rounds?: number;
      printerIds?: string[];
      items?: components['schemas']['ExtraItem'][];
      custom?: boolean;
    };
    ExtraItem: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      restaurantId?: string;
      extraId?: string;
      name?: string;
      nameI18n?: {
        [key: string]: string;
      };
      internalName?: string;
      internalNameI18n?: {
        [key: string]: string;
      };
      description?: string;
      descriptionI18n?: {
        [key: string]: string;
      };
      thumbnailUrl?: string;
      restricted?: boolean;
      disabled?: boolean;
      hidden?: boolean;
      deleted?: boolean;
      /** Format: int32 */
      order?: number;
      numeration?: string;
      unitPrice?: number;
      /** Format: int32 */
      qtd?: number;
      /** @enum {string} */
      category?: 'DISH' | 'BEVERAGE' | 'DISCOUNT' | 'FEE';
      /** @enum {string} */
      dineTaxCategory?: 'REDUCED' | 'NORMAL';
      /** @enum {string} */
      takeawayTaxCategory?: 'REDUCED' | 'NORMAL';
      taxCategory?: string;
      taxRate?: number;
      /** Format: int32 */
      max?: number;
      remarks?: components['schemas']['Remark'][];
      remarkAnnotations?: string[];
      total?: number;
      partnerPrices?: {
        [key: string]: number;
      };
      alcoholPercentage?: number;
      metadata?: components['schemas']['ItemMetadata'];
      custom?: boolean;
    };
    Gallery: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      name?: string;
      restaurantId?: string;
      menuItemId?: string;
      items?: components['schemas']['GalleryItem'][];
    };
    GalleryItem: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      restaurantId?: string;
      galleryId?: string;
      itemUrl?: string;
      thumbnailUrl?: string;
      /** @enum {string} */
      type?: 'IMAGE' | 'VIDEO';
    };
    GiftCardAmountSuggestion: {
      /** @enum {string} */
      type?: 'FIXED' | 'CUSTOM';
      value?: number;
    };
    GiftCardSettings: {
      suggestedAmounts?: components['schemas']['GiftCardAmountSuggestion'][];
    };
    Image: {
      src?: string;
      alt?: string;
    };
    InventoryConfig: {
      enableConsumption?: boolean;
      invoiceEmail?: string;
      /** @enum {string} */
      actionOnItemCancellation?: 'NEVER_RETURN' | 'ALWAYS_RETURN';
    };
    ItemMetadata: {
      consumptions?: components['schemas']['CategoryConsumption'][];
      /** @enum {string} */
      inventoryStatus?: 'OUT_OF_STOCK' | 'LOW_STOCK' | 'IN_STOCK';
    };
    KioskConfig: {
      languages?: (
        | 'en'
        | 'de'
        | 'zh'
        | 'bg'
        | 'cs'
        | 'da'
        | 'el'
        | 'es'
        | 'et'
        | 'fi'
        | 'fr'
        | 'hr'
        | 'hu'
        | 'it'
        | 'ja'
        | 'ko'
        | 'nl'
        | 'pl'
        | 'ro'
        | 'ru'
        | 'sq'
        | 'th'
        | 'tr'
        | 'uk'
        | 'vi'
        | 'zh_CN'
        | 'zh_HK'
        | 'pt_BR'
        | 'pt_PT'
        | 'en_US'
        | 'en_GB'
        | 'de_DE'
        | 'de_AT'
        | 'de_CH'
        | 'de_LU'
        | 'el_GR'
        | 'ko_KR'
        | 'bg_BG'
        | 'cs_CZ'
        | 'da_DK'
        | 'es_ES'
        | 'et_EE'
        | 'fi_FI'
        | 'fr_FR'
        | 'hr_HR'
        | 'hu_HU'
        | 'it_IT'
        | 'ja_JP'
        | 'nl_NL'
        | 'pl_PL'
        | 'ro_RO'
        | 'ru_RU'
        | 'sq_AL'
        | 'th_TH'
        | 'tr_TR'
        | 'uk_UA'
        | 'vi_VN'
        | 'pt'
        | 'hi'
        | 'hi_IN'
        | 'zt'
        | 'de_BE'
        | 'en_CA'
      )[];
      paymentMethods?: (
        | 'APP'
        | 'CASH'
        | 'CARD'
        | 'LIEFERANDO'
        | 'LIEFERANDO_CASH'
        | 'WOLT'
        | 'FOODPANDA'
        | 'UBEREATS'
        | 'FOOD2GO'
        | 'MCHOICE'
        | 'LEBEN'
        | 'PAYPAL'
        | 'UEBERWEISUNG'
        | 'EC'
        | 'VISA'
        | 'MASTERCARD'
        | 'AMERICAN_EXPRESS'
        | 'JCB'
        | 'STRIPE'
        | 'GUTSCHEIN'
        | 'KAIYUAN'
        | 'SONSTIGE'
        | 'ORDER_SMART'
        | 'ALLO_PAY'
        | 'ALLO_PAY_LINK'
        | 'ALLO_PAY_ONLINE'
        | 'OFFENE_RECHNUNG'
        | 'KAUF_AUF_RECHNUNG'
        | 'ANZAHLUNG'
        | 'SODEXO'
        | 'BAYERNETS'
        | 'EDENRED'
      )[];
      /** Format: url */
      heroImageUrl?: string;
      /** Format: url */
      topImageUrl?: string;
      allowPromotions?: boolean;
      allowGiftCards?: boolean;
      enableBypassSinglePaymentMethod?: boolean;
      tipEnabled?: boolean;
      diningOptions?: ('IN_HOUSE' | 'TO_GO')[];
    };
    Konto: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      /** Format: int32 */
      validity?: number;
      /** @enum {string} */
      standard?: 'SKR03' | 'SKR04';
      code?: string;
      label?: string;
      description?: string;
    };
    LieferandoConfig: {
      useV2Integration?: boolean;
    };
    LocalizedString: {
      en_value?: string;
      de_value?: string;
      zh_value?: string;
    };
    Menu: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      restaurantId?: string;
      restricted?: boolean;
      disabled?: boolean;
      recommended?: boolean;
      hidden?: boolean;
      excluded?: boolean;
      deleted?: boolean;
      oneTimeMenu?: boolean;
      lieferando?: boolean;
      lieferandoId?: string;
      imageUrl?: string;
      title?: string;
      titleI18n?: {
        [key: string]: string;
      };
      internalTitle?: string;
      internalTitleI18n?: {
        [key: string]: string;
      };
      description?: string;
      descriptionI18n?: {
        [key: string]: string;
      };
      /** @enum {string} */
      displayType?: 'ITEM' | 'CARD';
      /** Format: int32 */
      order?: number;
      items?: components['schemas']['MenuItem'][];
      sharedItemIds?: string[];
      schedule?: {
        [key: string]: components['schemas']['Period'][];
      };
      printerId?: string;
      printerIds?: string[];
      orderTypes?: ('PICKUP' | 'DELIVERY' | 'DINE_IN' | 'EXPRESS')[];
      /** @enum {string} */
      color?:
        | 'SALMON_500'
        | 'GREY_600'
        | 'GREY_800'
        | 'PURPLE_700'
        | 'YELLOW_700'
        | 'BLUE_700'
        | 'GREEN_600';
      partnerIds?: ('UBER_EATS' | 'WOLT' | 'LIEFERANDO' | 'OTHER')[];
      restaurantTags?: components['schemas']['RestaurantTag'][];
    };
    MenuItem: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      restaurantId?: string;
      menuId?: string;
      sharedMenuId?: string;
      code?: string;
      name?: string;
      nameI18n?: {
        [key: string]: string;
      };
      internalName?: string;
      internalNameI18n?: {
        [key: string]: string;
      };
      description?: string;
      descriptionI18n?: {
        [key: string]: string;
      };
      restricted?: boolean;
      disabled?: boolean;
      ongoing?: boolean;
      hidden?: boolean;
      deleted?: boolean;
      lieferando?: boolean;
      lieferandoId?: string;
      unitPrice?: number;
      /** @enum {string} */
      category?: 'DISH' | 'BEVERAGE' | 'DISCOUNT' | 'FEE';
      volume?: string;
      thumbnailUrl?: string;
      gallery?: components['schemas']['Gallery'];
      numeration?: string;
      remarks?: components['schemas']['Remark'][];
      /** @enum {string} */
      printerCategory?: 'KIOSK' | 'MAIN' | 'KITCHEN' | 'BAR' | 'MONITOR' | 'NONE';
      printerIds?: string[];
      remarkAnnotations?: string[];
      /** Format: int32 */
      order?: number;
      options?: components['schemas']['Option'][];
      extras?: components['schemas']['Extra'][];
      /** @enum {string} */
      dineTaxCategory?: 'REDUCED' | 'NORMAL';
      /** @enum {string} */
      takeawayTaxCategory?: 'REDUCED' | 'NORMAL';
      tags?: components['schemas']['Tag'][];
      tagIds?: string[];
      extraItems?: components['schemas']['ExtraItem'][];
      optionItems?: components['schemas']['OptionItem'][];
      /** @enum {string} */
      color?:
        | 'SALMON_500'
        | 'GREY_600'
        | 'GREY_800'
        | 'PURPLE_700'
        | 'YELLOW_700'
        | 'BLUE_700'
        | 'GREEN_600';
      partnerPrices?: {
        [key: string]: number;
      };
      alcoholPercentage?: number;
      partnerIds?: ('UBER_EATS' | 'WOLT' | 'LIEFERANDO' | 'OTHER')[];
      /** Format: int32 */
      ongoingExtrasQuantityMax?: number;
      hasHighQuantitySelector?: boolean;
      restaurantTags?: components['schemas']['RestaurantTag'][];
      includedInPrice?: boolean;
      metadata?: components['schemas']['ItemMetadata'];
      /** Format: int32 */
      preparationTime?: number;
      templateSetMenuId?: string;
      /** Format: int32 */
      defaultCourseNumber?: number;
      /** Format: int32 */
      smartschankId?: number;
      /** @enum {string} */
      unitOfMeasure?: 'G' | 'KG' | 'L' | 'ML' | 'PC' | 'BT';
    };
    MenuItemQuantityLimit: {
      menuItemCode?: string;
      /** Format: int32 */
      maxQtdPerPersonPerOrdering?: number;
    };
    MunicipalRegistry: {
      im?: string;
      cnae?: string;
    };
    Note: {
      id?: string;
      deleted?: boolean;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      restaurantId?: string;
      name?: string;
      nameI18n?: {
        [key: string]: string;
      };
      /** @enum {string} */
      type?: 'ORDER' | 'CANCELLATION';
    };
    OnUnseenConfig: {
      /** @enum {string} */
      action?: 'ACCEPT' | 'REJECT';
      /** Format: date-time */
      checkIfAcceptedAt?: string;
    };
    OncePerOrderMenuItemQuantityLimit: {
      menuItemCode?: string;
      /** Format: int32 */
      maxQtdPerPersonPerOrder?: number;
    };
    OpenInvoiceBankDetails: {
      enabled?: boolean;
      beneficiary?: string;
      iban?: string;
      bic?: string;
      bank?: string;
    };
    OpenInvoiceSettings: {
      /** Format: int32 */
      daysUntilDue?: number;
      additionalMemo?: string;
      paymentMethods?: ('CARD' | 'SEPA_DEBIT')[];
      bankDetails?: components['schemas']['OpenInvoiceBankDetails'];
    };
    Option: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      restaurantId?: string;
      name?: string;
      nameI18n?: {
        [key: string]: string;
      };
      internalName?: string;
      internalNameI18n?: {
        [key: string]: string;
      };
      description?: string;
      descriptionI18n?: {
        [key: string]: string;
      };
      restricted?: boolean;
      disabled?: boolean;
      hidden?: boolean;
      deleted?: boolean;
      lieferando?: boolean;
      lieferandoId?: string;
      /** Format: int32 */
      order?: number;
      total?: number;
      /** Format: int32 */
      qtd?: number;
      /** Format: int32 */
      rounds?: number;
      items?: components['schemas']['OptionItem'][];
      printerIds?: string[];
      sourceId?: string;
      collapsed?: boolean;
    };
    OptionItem: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      restaurantId?: string;
      optionId?: string;
      name?: string;
      nameI18n?: {
        [key: string]: string;
      };
      internalName?: string;
      internalNameI18n?: {
        [key: string]: string;
      };
      description?: string;
      descriptionI18n?: {
        [key: string]: string;
      };
      restricted?: boolean;
      disabled?: boolean;
      hidden?: boolean;
      deleted?: boolean;
      /** Format: int32 */
      order?: number;
      numeration?: string;
      thumbnailUrl?: string;
      unitPrice?: number;
      /** Format: int32 */
      qtd?: number;
      /** @enum {string} */
      category?: 'DISH' | 'BEVERAGE' | 'DISCOUNT' | 'FEE';
      /** @enum {string} */
      dineTaxCategory?: 'REDUCED' | 'NORMAL';
      /** @enum {string} */
      takeawayTaxCategory?: 'REDUCED' | 'NORMAL';
      taxCategory?: string;
      taxRate?: number;
      /** Format: int32 */
      min?: number;
      /** Format: int32 */
      max?: number;
      remarks?: components['schemas']['Remark'][];
      remarkAnnotations?: string[];
      total?: number;
      extraItems?: components['schemas']['ExtraItem'][];
      sourceId?: string;
      tags?: components['schemas']['Tag'][];
      tagIds?: string[];
      partnerPrices?: {
        [key: string]: number;
      };
      alcoholPercentage?: number;
      metadata?: components['schemas']['ItemMetadata'];
    };
    Order: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      /** Format: date-time */
      closeTime?: string;
      closedBy?: string;
      autoAccept?: boolean;
      unseenConfig?: components['schemas']['OnUnseenConfig'];
      /** Format: int32 */
      number?: number;
      restaurantId?: string;
      restaurant?:
        | components['schemas']['Restaurant']
        | components['schemas']['RestaurantAustria']
        | components['schemas']['RestaurantBrazil'];
      customerId?: string;
      userId?: string;
      accountId?: string;
      isWaiter?: boolean;
      isNew?: boolean;
      user?: components['schemas']['Account'];
      tableId?: string;
      tableCode?: string;
      /** @enum {string} */
      partnerId?: 'UBER_EATS' | 'WOLT' | 'LIEFERANDO' | 'OTHER';
      items?: components['schemas']['OrderItem'][];
      offlineItems?: components['schemas']['OrderItem'][];
      /** @enum {string} */
      paymentOption?: 'ALL' | 'OWN' | 'SPLIT' | 'CUSTOM';
      /** @enum {string} */
      status?:
        | 'OPEN'
        | 'PAYMENT_REQUESTED'
        | 'PENDING'
        | 'PREPARING'
        | 'READY'
        | 'DELIVERING'
        | 'CLOSED'
        | 'CANCELLED';
      /** @enum {string} */
      type?: 'PICKUP' | 'DELIVERY' | 'DINE_IN' | 'EXPRESS';
      /** @enum {string} */
      origin?: 'POS' | 'KIOSK' | 'TABLET' | 'WEBSHOP' | 'PARTNER' | 'SCAN_TO_ORDER';
      activity?: components['schemas']['OrderActivity'];
      invalidState?: boolean;
      restaurantSlug?: string;
      total?: number;
      itemsTotal?: number;
      totalDiscounts?: number;
      taxesTotal?: number;
      totalWithoutTaxes?: number;
      participantIds?: string[];
      participants?: components['schemas']['OrderParticipant'][];
      guestIds?: string[];
      orderApprovalId?: string;
      orderRejectId?: string;
      refundedOrderId?: string;
      refundOrderId?: string;
      recoveredOrderId?: string;
      orderApprovalRequestId?: string;
      orderApprovalRequest?: components['schemas']['OrderApprovalRequest'];
      orderServingId?: string;
      checkoutId?: string;
      payments?: components['schemas']['Payment'][];
      identifier?: string;
      receiptTxId?: string;
      /** Format: int32 */
      receiptTxRevision?: number;
      orderTxId?: string;
      /** Format: int32 */
      orderTxRevision?: number;
      pickupTime?: string;
      /** Format: date-time */
      takeawayDate?: string;
      isAsap?: boolean;
      /** Format: date-time */
      estimatedPickupTime?: string;
      /** Format: date-time */
      requestedPickupTime?: string;
      /** Format: date-time */
      requestedDeliveryTime?: string;
      /** Format: date-time */
      estimatedDeliveryTime?: string;
      /** Format: date-time */
      actualDeliveryTime?: string;
      notes?: string;
      customer?: components['schemas']['Customer'];
      discountId?: string;
      discount?: components['schemas']['Discount'];
      deliveryFee?: number;
      receiptStripeUrl?: string;
      mergedInto?: string;
      mergedWith?: string;
      notificationSent?: boolean;
      verificationRequestSent?: boolean;
      reservationId?: string;
      /** Format: int32 */
      dishCount?: number;
      /** Format: int32 */
      beverageCount?: number;
      /** @enum {string} */
      mode?: 'LIVE' | 'EXPLORATION';
      /** @enum {string} */
      paymentStatus?: 'NOT_PAID' | 'PAYING' | 'PAID';
      partnerOrderInfo?: components['schemas']['PartnerOrderInfo'];
      drivers?: components['schemas']['Driver'][];
      /** Format: int32 */
      duration?: number;
      /** Format: int32 */
      remainingDuration?: number;
      /** Format: int32 */
      estimatedDiningTime?: number;
      /** Format: int32 */
      estimatedPreparationTime?: number;
      /** Format: date-time */
      acceptedAt?: string;
      timeZone?: string;
      guestMonitorId?: string;
      sendToGuestMonitor?: boolean;
      /** Format: int32 */
      partySize?: number;
      orderingMode?: components['schemas']['OrderingMode'];
      orderActions?: components['schemas']['OrderAction'][];
      table?: components['schemas']['Table'];
      toGo?: boolean;
      pagerIdentifier?: string;
      cartId?: string;
      orderTagLimit?: components['schemas']['OrderTagLimit'];
    };
    OrderAction: {
      title?: string;
      content?: string;
      titleI18n?: {
        [key: string]: string;
      };
      contentI18n?: {
        [key: string]: string;
      };
    };
    OrderActivity: {
      /** Format: date-time */
      partnerCreationTime?: string;
      createdBy?: components['schemas']['Account'];
      acceptedBy?: components['schemas']['Account'];
      /** Format: date-time */
      acceptedAt?: string;
      autoAccepted?: boolean;
      preparingBy?: components['schemas']['Account'];
      /** Format: date-time */
      preparingAt?: string;
      autoPreparing?: boolean;
      readyBy?: components['schemas']['Account'];
      /** Format: date-time */
      readyAt?: string;
      deliveringBy?: components['schemas']['Account'];
      /** Format: date-time */
      deliveringAt?: string;
      deliveringExternally?: boolean;
      recoveredBy?: components['schemas']['Account'];
      /** Format: date-time */
      recoveredAt?: string;
      refundedBy?: components['schemas']['Account'];
      /** Format: date-time */
      refundedAt?: string;
      closedBy?: components['schemas']['Account'];
      /** Format: date-time */
      closedAt?: string;
      closedExternally?: boolean;
      autoClosed?: boolean;
      closingReason?: string;
      closingReasonI18n?: {
        [key: string]: string;
      };
      closingReasonCustomerFriendly?: string;
      closingReasonCustomerFriendlyI18n?: {
        [key: string]: string;
      };
      cancellationRequestedBy?: components['schemas']['Account'];
      /** Format: date-time */
      cancellationRequestedAt?: string;
      cancellationRequestedExternally?: boolean;
      cancellationFailureReason?: string;
      cancellationFailureReasonI18n?: {
        [key: string]: string;
      };
      driverAssignmentLogs?: components['schemas']['DriverAssignmentLog'][];
    };
    OrderApprovalRequest: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      userId?: string;
      lastName?: string;
      email?: string;
      orderId?: string;
    };
    OrderCart: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      /** @enum {string} */
      status?: 'OPEN' | 'PAYMENT_REQUESTED' | 'PAYMENT_FAILED' | 'COMPLETED';
      restaurantId?: string;
      items?: components['schemas']['OrderItem'][];
      total?: number;
      itemsTotal?: number;
      totalDiscounts?: number;
      taxesTotal?: number;
      totalWithoutTaxes?: number;
      /** @enum {string} */
      orderType?: 'PICKUP' | 'DELIVERY' | 'DINE_IN' | 'EXPRESS';
      /** @enum {string} */
      orderOrigin?: 'POS' | 'KIOSK' | 'TABLET' | 'WEBSHOP' | 'PARTNER' | 'SCAN_TO_ORDER';
      /** @enum {string} */
      paymentChannel?:
        | 'APP'
        | 'CASH'
        | 'CARD'
        | 'LIEFERANDO'
        | 'LIEFERANDO_CASH'
        | 'WOLT'
        | 'FOODPANDA'
        | 'UBEREATS'
        | 'FOOD2GO'
        | 'MCHOICE'
        | 'LEBEN'
        | 'PAYPAL'
        | 'UEBERWEISUNG'
        | 'EC'
        | 'VISA'
        | 'MASTERCARD'
        | 'AMERICAN_EXPRESS'
        | 'JCB'
        | 'STRIPE'
        | 'GUTSCHEIN'
        | 'KAIYUAN'
        | 'SONSTIGE'
        | 'ORDER_SMART'
        | 'ALLO_PAY'
        | 'ALLO_PAY_LINK'
        | 'ALLO_PAY_ONLINE'
        | 'OFFENE_RECHNUNG'
        | 'KAUF_AUF_RECHNUNG'
        | 'ANZAHLUNG'
        | 'SODEXO'
        | 'BAYERNETS'
        | 'EDENRED';
      /** Format: date-time */
      paymentRequestedAt?: string;
      paymentId?: string;
      paymentUrl?: string;
      notes?: string;
      customer?: components['schemas']['Customer'];
      orderId?: string;
      /** Format: int32 */
      orderNumber?: number;
      toGo?: boolean;
      /** Format: int64 */
      version?: number;
    };
    OrderItem: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      name?: string;
      nameI18n?: {
        [key: string]: string;
      };
      description?: string;
      descriptionI18n?: {
        [key: string]: string;
      };
      internalName?: string;
      internalNameI18n?: {
        [key: string]: string;
      };
      numeration?: string;
      remarks?: components['schemas']['Remark'][];
      unitPrice?: number;
      total?: number;
      baseTotal?: number;
      /** @enum {string} */
      category?: 'DISH' | 'BEVERAGE' | 'DISCOUNT' | 'FEE';
      type?: string;
      volume?: string;
      menuId?: string;
      code?: string;
      customerId?: string;
      userId?: string;
      createdByAccount?: components['schemas']['Account'];
      /** @enum {string} */
      printerCategory?: 'KIOSK' | 'MAIN' | 'KITCHEN' | 'BAR' | 'MONITOR' | 'NONE';
      offline?: boolean;
      orderId?: string;
      /** Format: int32 */
      qtd?: number;
      /** @enum {string} */
      status?:
        | 'UNCONFIRMED'
        | 'CONFIRMED'
        | 'COOKING'
        | 'SERVING'
        | 'SERVED'
        | 'PAID'
        | 'CANCELLED'
        | 'REMOVED';
      preparation?: components['schemas']['Preparation'];
      notes?: string;
      orderItemCancellationId?: string;
      cancellationNotes?: string;
      cancellationPredefinedNotes?: components['schemas']['Note'][];
      taxCategory?: string;
      taxRate?: number;
      options?: components['schemas']['Option'][];
      extras?: components['schemas']['Extra'][];
      promotionType?: string;
      isDiscount?: boolean;
      isRefund?: boolean;
      isFee?: boolean;
      ongoing?: boolean;
      nested?: boolean;
      invalidState?: boolean;
      nestedOrderItemIds?: string[];
      predefinedNotes?: components['schemas']['Note'][];
      discountId?: string;
      discountedItemId?: string;
      discount?: components['schemas']['Discount'];
      thumbnailUrl?: string;
      aggregatedTotal?: number;
      /** Format: int32 */
      aggregatedQtd?: number;
      extraItems?: components['schemas']['ExtraItem'][];
      optionItems?: components['schemas']['OptionItem'][];
      /** @enum {string} */
      partnerId?: 'UBER_EATS' | 'WOLT' | 'LIEFERANDO' | 'OTHER';
      sourceOrderId?: string;
      sourceTableId?: string;
      movedByWaiterId?: string;
      /** Format: date-time */
      movedAt?: string;
      hasHighQuantitySelector?: boolean;
      metadata?: components['schemas']['ItemMetadata'];
      /** Format: int32 */
      preparationTime?: number;
      printerIds?: string[];
      templateSetMenuId?: string;
      leadingTemplateSetMenuItem?: boolean;
      /** Format: int32 */
      smartschankId?: number;
      /** @enum {string} */
      unitOfMeasure?: 'G' | 'KG' | 'L' | 'ML' | 'PC' | 'BT';
      measurementValue?: number;
      restaurantTags?: components['schemas']['RestaurantTag'][];
    };
    OrderParticipant: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      orderId?: string;
      restaurantId?: string;
      customerId?: string;
      customer?: components['schemas']['Customer'];
      userId?: string;
      approvalId?: string;
      approvedByCustomerId?: string;
      approvedByCustomer?: components['schemas']['Customer'];
      approvedByWaiterId?: string;
      canKickout?: boolean;
      canReject?: boolean;
      canApprove?: boolean;
      canLeave?: boolean;
      checkoutSessionId?: string;
      hasApproval?: boolean;
    };
    OrderTagLimit: {
      id?: string;
      limitedTags?: components['schemas']['RestaurantTag'][];
      orderId?: string;
      /** Format: date-time */
      expiresAt?: string;
      /** Format: int32 */
      periodInMinutes?: number;
      /** Format: int32 */
      quantityOrderedInPeriod?: number;
      itemQuantityLimits?: components['schemas']['MenuItemQuantityLimit'][];
      /** Format: int32 */
      maxQuantityInPeriod?: number;
      /** Format: int32 */
      expirationInSeconds?: number;
      /** Format: int32 */
      includingUnconfirmedQuantityOrderedInPeriod?: number;
      hasUnconfirmedUnlimited?: boolean;
      hasUnconfirmedLimited?: boolean;
      /** Format: int32 */
      numberOfOrdersInPeriod?: number;
      oncePerOrderQuantityLimits?: components['schemas']['OncePerOrderMenuItemQuantityLimit'][];
      oncePerOrderLimitedTags?: components['schemas']['RestaurantTag'][];
      /** Format: int32 */
      oncePerOrderQuantity?: number;
      /** Format: int32 */
      oncePerOrderMaxQuantity?: number;
      /** Format: int32 */
      oncePerOrderIncludingUnconfirmedQuantity?: number;
    };
    OrderingMode: {
      id?: string;
      enabled?: boolean;
      /** Format: int32 */
      position?: number;
      nameI18n?: {
        [key: string]: string;
      };
      descriptionI18n?: {
        [key: string]: string;
      };
      steps?: components['schemas']['OrderingModeStep'][];
      tags?: components['schemas']['RestaurantTag'][];
      limitedTags?: components['schemas']['RestaurantTag'][];
      itemQuantityLimits?: components['schemas']['MenuItemQuantityLimit'][];
      /** Format: int32 */
      limitedTagPeriodInMinutes?: number;
      singleOrderInPeriod?: boolean;
      oncePerOrderLimitedTags?: components['schemas']['RestaurantTag'][];
      oncePerOrderQuantityLimits?: components['schemas']['OncePerOrderMenuItemQuantityLimit'][];
    };
    OrderingModeStep: {
      nameI18n?: {
        [key: string]: string;
      };
      mandatory?: boolean;
      /** @enum {string} */
      type?: 'NUMBER_OF_PARTICIPANTS' | 'MENU_ITEM_SELECTION' | 'ALLERGENS';
      /** Format: int32 */
      minItemsQtd?: number;
      /** Format: int32 */
      maxItemsQtd?: number;
      tags?: components['schemas']['RestaurantTag'][];
      name?: string;
      menuItems?: components['schemas']['MenuItem'][];
    };
    PartnerConfig: {
      /** @enum {string} */
      partnerId?: 'UBER_EATS' | 'WOLT' | 'LIEFERANDO' | 'OTHER';
      offerCutlery?: boolean;
      offerVytalBowl?: boolean;
      skipKitchenPrintout?: boolean;
      unseenOrder?: components['schemas']['UnseenOrder'];
    };
    PartnerOrderInfo: {
      /** @enum {string} */
      partnerId?: 'UBER_EATS' | 'WOLT' | 'LIEFERANDO' | 'OTHER';
      partnerRestaurantId?: string;
      storeId?: string;
      venueId?: string;
      orderId?: string;
      displayOrderId?: string;
      orderType?: string;
      createdByRestaurant?: boolean;
      orderNumber?: string;
      isPreOrder?: boolean;
    };
    Payment: {
      id?: string;
      deleted?: boolean;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      orderId?: string;
      restaurantId?: string;
      customerId?: string;
      /** @enum {string} */
      paymentChannel?:
        | 'APP'
        | 'CASH'
        | 'CARD'
        | 'LIEFERANDO'
        | 'LIEFERANDO_CASH'
        | 'WOLT'
        | 'FOODPANDA'
        | 'UBEREATS'
        | 'FOOD2GO'
        | 'MCHOICE'
        | 'LEBEN'
        | 'PAYPAL'
        | 'UEBERWEISUNG'
        | 'EC'
        | 'VISA'
        | 'MASTERCARD'
        | 'AMERICAN_EXPRESS'
        | 'JCB'
        | 'STRIPE'
        | 'GUTSCHEIN'
        | 'KAIYUAN'
        | 'SONSTIGE'
        | 'ORDER_SMART'
        | 'ALLO_PAY'
        | 'ALLO_PAY_LINK'
        | 'ALLO_PAY_ONLINE'
        | 'OFFENE_RECHNUNG'
        | 'KAUF_AUF_RECHNUNG'
        | 'ANZAHLUNG'
        | 'SODEXO'
        | 'BAYERNETS'
        | 'EDENRED';
      /** @enum {string} */
      paymentOption?: 'ALL' | 'OWN' | 'SPLIT' | 'CUSTOM';
      amount?: number;
      /** @enum {string} */
      tipOption?: 'CUSTOM' | 'TIP5' | 'TIP10' | 'TIP15' | 'TIP20';
      tipAmount?: number;
      waiterId?: string;
      orderItemIds?: string[];
      cardId?: string;
      cardCode?: string;
      cardTransactionId?: string;
      linkingPaymentId?: string;
      isLinked?: boolean;
      linkedPaymentId?: string;
      transactionAmount?: number;
      transactionChange?: number;
      linkedAmount?: number;
    };
    Period: {
      opens?: string;
      closes?: string;
    };
    Permission: {
      /** @enum {string} */
      permissionIdentifier?:
        | 'ALL'
        | 'CAN_CANCEL_ORDER'
        | 'CAN_RECOVER_ORDER'
        | 'CAN_REFUND_ORDER'
        | 'CAN_VIEW_HISTORICAL_ORDERS'
        | 'CAN_VIEW_OWN_HISTORICAL_ORDERS'
        | 'CAN_VIEW_MENU_EDITOR'
        | 'CAN_VIEW_FLOOR_PLAN_EDITOR'
        | 'CAN_VIEW_CUSTOMER_EDITOR'
        | 'CAN_UPDATE_GENERAL_SETTINGS'
        | 'CAN_VIEW_REPORTS'
        | 'CAN_VIEW_OWN_REPORTS'
        | 'CAN_UPDATE_CASH_REGISTER'
        | 'CAN_VIEW_MARKETING'
        | 'CAN_ROTATE_TABLE'
        | 'CAN_DISCOUNT_ORDER'
        | 'CAN_MANAGE_APPS'
        | 'CAN_VIEW_CASH_REGISTER'
        | 'CAN_ORDER'
        | 'CAN_MANAGE_TAKEAWAYS'
        | 'CAN_MANAGE_RESERVATIONS'
        | 'CAN_VIEW_HISTORICAL_ORDERS_ANALYTICS'
        | 'CAN_MOVE_ORDER_ITEM'
        | 'SUPER_ADMIN'
        | 'CAN_USE_TIMESHEET'
        | 'CAN_MANUALLY_PROCESS_ALLO_PAY_TERMINAL_CHECKOUT'
        | 'CAN_USE_ALLO_SHOP'
        | 'CAN_VIEW_RESERVATIONS'
        | 'CAN_DELETE_GIFT_CARD'
        | 'CAN_COLLABORATE_ON_ORDER'
        | 'CAN_CLOSE_DAILY_REPORT'
        | 'CAN_WORK_AS_DRIVER';
      restaurantIds?: string[];
      organizationIds?: string[];
      includes?: (
        | 'ALL'
        | 'CAN_CANCEL_ORDER'
        | 'CAN_RECOVER_ORDER'
        | 'CAN_REFUND_ORDER'
        | 'CAN_VIEW_HISTORICAL_ORDERS'
        | 'CAN_VIEW_OWN_HISTORICAL_ORDERS'
        | 'CAN_VIEW_MENU_EDITOR'
        | 'CAN_VIEW_FLOOR_PLAN_EDITOR'
        | 'CAN_VIEW_CUSTOMER_EDITOR'
        | 'CAN_UPDATE_GENERAL_SETTINGS'
        | 'CAN_VIEW_REPORTS'
        | 'CAN_VIEW_OWN_REPORTS'
        | 'CAN_UPDATE_CASH_REGISTER'
        | 'CAN_VIEW_MARKETING'
        | 'CAN_ROTATE_TABLE'
        | 'CAN_DISCOUNT_ORDER'
        | 'CAN_MANAGE_APPS'
        | 'CAN_VIEW_CASH_REGISTER'
        | 'CAN_ORDER'
        | 'CAN_MANAGE_TAKEAWAYS'
        | 'CAN_MANAGE_RESERVATIONS'
        | 'CAN_VIEW_HISTORICAL_ORDERS_ANALYTICS'
        | 'CAN_MOVE_ORDER_ITEM'
        | 'SUPER_ADMIN'
        | 'CAN_USE_TIMESHEET'
        | 'CAN_MANUALLY_PROCESS_ALLO_PAY_TERMINAL_CHECKOUT'
        | 'CAN_USE_ALLO_SHOP'
        | 'CAN_VIEW_RESERVATIONS'
        | 'CAN_DELETE_GIFT_CARD'
        | 'CAN_COLLABORATE_ON_ORDER'
        | 'CAN_CLOSE_DAILY_REPORT'
        | 'CAN_WORK_AS_DRIVER'
      )[];
    };
    Preparation: {
      description?: string;
    };
    Printer: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      restaurantId?: string;
      sn?: string;
      key?: string;
      label?: string;
      ip?: string;
      osName?: string;
      /** @enum {string} */
      connectionType?: 'IP_ADDRESS' | 'USB_PORT' | 'HTTP_API';
      /** @enum {string} */
      printerCategory?: 'KIOSK' | 'MAIN' | 'KITCHEN' | 'BAR' | 'MONITOR' | 'NONE';
      /** @enum {string} */
      printerFormat?: 'PAPER' | 'LABEL';
      /** @enum {string} */
      brand?: 'FEIE' | 'EPSON' | 'STAR' | 'SUNMI';
      /** @enum {string} */
      model?:
        | 'FEIE_N20W'
        | 'FEIE_N21W'
        | 'FEIE_N80WC'
        | 'EPSON_TM_T20II'
        | 'EPSON_TM_M30'
        | 'STAR_MC_Print2'
        | 'STAR_MC_Print3'
        | 'SUNMI_Kiosk_K2';
      /** @enum {string} */
      paperSize?: 'LABEL_SMALL' | 'LABEL_BIG' | 'CUSTOM_50_30';
      /** @enum {string} */
      language?:
        | 'en'
        | 'de'
        | 'zh'
        | 'bg'
        | 'cs'
        | 'da'
        | 'el'
        | 'es'
        | 'et'
        | 'fi'
        | 'fr'
        | 'hr'
        | 'hu'
        | 'it'
        | 'ja'
        | 'ko'
        | 'nl'
        | 'pl'
        | 'ro'
        | 'ru'
        | 'sq'
        | 'th'
        | 'tr'
        | 'uk'
        | 'vi'
        | 'zh_CN'
        | 'zh_HK'
        | 'pt_BR'
        | 'pt_PT'
        | 'en_US'
        | 'en_GB'
        | 'de_DE'
        | 'de_AT'
        | 'de_CH'
        | 'de_LU'
        | 'el_GR'
        | 'ko_KR'
        | 'bg_BG'
        | 'cs_CZ'
        | 'da_DK'
        | 'es_ES'
        | 'et_EE'
        | 'fi_FI'
        | 'fr_FR'
        | 'hr_HR'
        | 'hu_HU'
        | 'it_IT'
        | 'ja_JP'
        | 'nl_NL'
        | 'pl_PL'
        | 'ro_RO'
        | 'ru_RU'
        | 'sq_AL'
        | 'th_TH'
        | 'tr_TR'
        | 'uk_UA'
        | 'vi_VN'
        | 'pt'
        | 'hi'
        | 'hi_IN'
        | 'zt'
        | 'de_BE'
        | 'en_CA';
      hasCutter?: boolean;
      cutByCategory?: boolean;
      sortByCategory?: boolean;
      disabled?: boolean;
      multiplyAdditions?: boolean;
      printAccount?: boolean;
      orderTypes?: ('PICKUP' | 'DELIVERY' | 'DINE_IN' | 'EXPRESS')[];
      printLogo?: boolean;
      cashDrawer?: boolean;
      /** Format: int32 */
      topSpacingLines?: number;
      orderingDeviceId?: string;
    };
    ReceiptConfig: {
      hideCancelledOrdersWithoutPayments?: boolean;
      defaultOrderColumns?: (
        | 'IDENTIFIER'
        | 'ORDER_NUMBER'
        | 'ORDER_TYPE'
        | 'TABLE_NUMBER'
        | 'WAITER'
        | 'CLOSED_BY'
        | 'ORDER_STARTED'
        | 'ORDER_ENDED'
        | 'ORDER_TOTAL'
        | 'TABLE_TAX'
        | 'TIP'
        | 'TABLE_TOTAL'
        | 'PAYMENT_METHOD'
        | 'PAYMENT_STATUS'
      )[];
      defaultPaymentColumns?: (
        | 'IDENTIFIER'
        | 'ORDER_NUMBER'
        | 'ORDER_TYPE'
        | 'TABLE_NUMBER'
        | 'CLOSED_BY'
        | 'ORDER_STARTED'
        | 'ORDER_ENDED'
        | 'TIP'
        | 'PAYMENT_METHOD'
        | 'PAYMENT_STATUS'
        | 'PAYMENT'
        | 'PAYMENT_TOTAL'
      )[];
    };
    ReceiptOption: {
      /** @enum {string} */
      type?: 'NONE' | 'STANDARD' | 'BUSINESS' | 'PDF';
      email?: string;
      paymentId?: string;
    };
    Record: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      restaurantId?: string;
      cashRegisterId?: string;
      cashRegisterBalance?: number;
      /** @enum {string} */
      type?: 'CASH_IN' | 'CASH_OUT';
      /** @enum {string} */
      category?:
        | 'NORMAL'
        | 'TIP'
        | 'DAILY_REVENUE'
        | 'CASH_DEPOSIT'
        | 'BANK_DEPOSIT'
        | 'SALARY_PAYMENT'
        | 'MONEY_TRANSIT'
        | 'PRIVATE'
        | 'ADJUSTMENT'
        | 'GUTSCHEIN'
        | 'CANCELLATION'
        | 'MONEY_TRANSIT_LIEFERANDO'
        | 'MONEY_TRANSIT_STRIPE'
        | 'MONEY_TRANSIT_WOLT'
        | 'MONEY_TRANSIT_UBER_EATS'
        | 'MONEY_TRANSIT_ONLINE';
      /** @enum {string} */
      taxRate?: 'NONE' | 'SEVEN' | 'NINETEEN' | 'MIXED';
      amount?: number;
      kontoId?: string;
      konto?: components['schemas']['Konto'];
      gegenKontoId?: string;
      gegenKonto?: components['schemas']['Konto'];
      nestedRecordIds?: string[];
      nested?: boolean;
      supplierId?: string;
      supplier?: components['schemas']['Supplier'];
      documentId?: string;
      documentUrl?: string;
      name?: string;
      description?: string;
      note?: string;
      /** Format: date-time */
      date?: string;
      cardTransactionId?: string;
      cardCode?: string;
      cancelled?: boolean;
      /** Format: date-time */
      cancelledAt?: string;
      cancelledBy?: string;
      cancelledByRecordId?: string;
      createdByUserId?: string;
      createdByAccountId?: string;
    };
    RefundConfig: {
      allOPayAutoRefundEnabled?: boolean;
    };
    Remark: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      deleted?: boolean;
      annotation?: string;
      restaurantId?: string;
      shortText?: string;
      description?: string;
      global?: boolean;
      /** @enum {string} */
      type?: 'ADDITIVE' | 'ALLERGIES' | 'SPICE';
      /** @enum {string} */
      subType?:
        | 'CEREAL_UNSPECIFIED'
        | 'CRUSTACEANS'
        | 'EGGS'
        | 'FISH'
        | 'PEANUTS'
        | 'SOYBEANS'
        | 'MILK'
        | 'NUTS_UNSPECIFIED'
        | 'CELERY'
        | 'MUSTARD'
        | 'SESAME_SEEDS'
        | 'SULPHUR_DIOXIDE_SULPHITES'
        | 'LUPIN'
        | 'MOLLUSCS'
        | 'UNSPECIFIED'
        | 'COLORANT_UNSPECIFIED'
        | 'PRESERVATIVES_UNSPECIFIED'
        | 'ANTIOXIDANT'
        | 'FLAVOURE_ENHANCER'
        | 'SULFITES'
        | 'BLACKENED'
        | 'WAXED'
        | 'PHOSPHATE'
        | 'SWEETENER_UNSPECIFIED'
        | 'CAFFEINE_UNSPECIFIED'
        | 'QUININE'
        | 'GENETICALLY_MODIFIED'
        | 'ACIDIFIERS'
        | 'STABILISERS'
        | 'PROTEIN_UNSPECIFIED';
      /** @enum {string} */
      iconIdentifier?:
        | 'GLUTEN'
        | 'CRUSTACEANS'
        | 'EGGS'
        | 'FISH'
        | 'PEANUTS'
        | 'SOYBEANS'
        | 'MILK'
        | 'NUTS'
        | 'CELERY'
        | 'MUSTARD'
        | 'SESAME'
        | 'SULPHUR'
        | 'LUPINS'
        | 'MOLLUSKS';
      descriptionI18n?: {
        [key: string]: string;
      };
      shortTextI18n?: {
        [key: string]: string;
      };
      category?: string;
      /** Format: int32 */
      level?: number;
      icon?: string;
    };
    Restaurant: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      /** @enum {string} */
      mode?: 'LIVE' | 'EXPLORATION';
      name?: string;
      slug?: string;
      intlName?: components['schemas']['LocalizedString'];
      description?: string;
      intlDescription?: components['schemas']['LocalizedString'];
      address?: components['schemas']['Address'];
      avatarId?: string;
      coverId?: string;
      code?: string;
      tables?: unknown[];
      rating?: number;
      hidden?: boolean;
      taxId?: string;
      vatNumber?: string;
      phone?: string;
      email?: string;
      organizationId?: string;
      nameI18n?: {
        [key: string]: string;
      };
      descriptionI18n?: {
        [key: string]: string;
      };
      timezone?: string;
      logoUrl?: string;
      thumbnailUrl?: string;
      tags?: components['schemas']['Tag'][];
      tagIds?: string[];
      notificationMobile?: string;
      notificationEmail?: string;
      paymentChannels?: (
        | 'APP'
        | 'CASH'
        | 'CARD'
        | 'LIEFERANDO'
        | 'LIEFERANDO_CASH'
        | 'WOLT'
        | 'FOODPANDA'
        | 'UBEREATS'
        | 'FOOD2GO'
        | 'MCHOICE'
        | 'LEBEN'
        | 'PAYPAL'
        | 'UEBERWEISUNG'
        | 'EC'
        | 'VISA'
        | 'MASTERCARD'
        | 'AMERICAN_EXPRESS'
        | 'JCB'
        | 'STRIPE'
        | 'GUTSCHEIN'
        | 'KAIYUAN'
        | 'SONSTIGE'
        | 'ORDER_SMART'
        | 'ALLO_PAY'
        | 'ALLO_PAY_LINK'
        | 'ALLO_PAY_ONLINE'
        | 'OFFENE_RECHNUNG'
        | 'KAUF_AUF_RECHNUNG'
        | 'ANZAHLUNG'
        | 'SODEXO'
        | 'BAYERNETS'
        | 'EDENRED'
      )[];
      enabledPaymentChannels?: {
        [key: string]: (
          | 'APP'
          | 'CASH'
          | 'CARD'
          | 'LIEFERANDO'
          | 'LIEFERANDO_CASH'
          | 'WOLT'
          | 'FOODPANDA'
          | 'UBEREATS'
          | 'FOOD2GO'
          | 'MCHOICE'
          | 'LEBEN'
          | 'PAYPAL'
          | 'UEBERWEISUNG'
          | 'EC'
          | 'VISA'
          | 'MASTERCARD'
          | 'AMERICAN_EXPRESS'
          | 'JCB'
          | 'STRIPE'
          | 'GUTSCHEIN'
          | 'KAIYUAN'
          | 'SONSTIGE'
          | 'ORDER_SMART'
          | 'ALLO_PAY'
          | 'ALLO_PAY_LINK'
          | 'ALLO_PAY_ONLINE'
          | 'OFFENE_RECHNUNG'
          | 'KAUF_AUF_RECHNUNG'
          | 'ANZAHLUNG'
          | 'SODEXO'
          | 'BAYERNETS'
          | 'EDENRED'
        )[];
      };
      hasPickup?: boolean;
      /** Format: int32 */
      pickupRequests?: number;
      hasDelivery?: boolean;
      hasScan?: boolean;
      takeawayThreshold?: number;
      takeawayAreas?: string[];
      expressCheckout?: boolean;
      oneClickCheckout?: boolean;
      deliveryFee?: number;
      website?: string;
      openingHours?: {
        [key: string]: components['schemas']['Period'][];
      };
      menus?: components['schemas']['Menu'][];
      galleries?: components['schemas']['Gallery'][];
      isOpen?: boolean;
      configuration?: components['schemas']['RestaurantConfiguration'];
      googlePlaceId?: string;
      googleReservationEnabled?: boolean;
      webshopUrl?: string;
      reservationUrl?: string;
      giftCardUrl?: string;
      isParent?: boolean;
      parentRestaurantId?: string;
      branchRestaurantIds?: string[];
      serializationType: string;
    };
    RestaurantAustria: {
      serializationType: 'RestaurantAustria';
    } & (Omit<components['schemas']['Restaurant'], 'serializationType'> & {
      branchRestaurants?: {
        id?: string;
        /** Format: date-time */
        creationTime?: string;
        /** Format: date-time */
        modificationTime?: string;
        /** @enum {string} */
        mode?: 'LIVE' | 'EXPLORATION';
        name?: string;
        slug?: string;
        intlName?: components['schemas']['LocalizedString'];
        description?: string;
        intlDescription?: components['schemas']['LocalizedString'];
        address?: components['schemas']['Address'];
        avatarId?: string;
        coverId?: string;
        code?: string;
        tables?: unknown[];
        rating?: number;
        hidden?: boolean;
        taxId?: string;
        vatNumber?: string;
        phone?: string;
        email?: string;
        organizationId?: string;
        nameI18n?: {
          [key: string]: string;
        };
        descriptionI18n?: {
          [key: string]: string;
        };
        timezone?: string;
        logoUrl?: string;
        thumbnailUrl?: string;
        tags?: components['schemas']['Tag'][];
        tagIds?: string[];
        notificationMobile?: string;
        notificationEmail?: string;
        paymentChannels?: (
          | 'APP'
          | 'CASH'
          | 'CARD'
          | 'LIEFERANDO'
          | 'LIEFERANDO_CASH'
          | 'WOLT'
          | 'FOODPANDA'
          | 'UBEREATS'
          | 'FOOD2GO'
          | 'MCHOICE'
          | 'LEBEN'
          | 'PAYPAL'
          | 'UEBERWEISUNG'
          | 'EC'
          | 'VISA'
          | 'MASTERCARD'
          | 'AMERICAN_EXPRESS'
          | 'JCB'
          | 'STRIPE'
          | 'GUTSCHEIN'
          | 'KAIYUAN'
          | 'SONSTIGE'
          | 'ORDER_SMART'
          | 'ALLO_PAY'
          | 'ALLO_PAY_LINK'
          | 'ALLO_PAY_ONLINE'
          | 'OFFENE_RECHNUNG'
          | 'KAUF_AUF_RECHNUNG'
          | 'ANZAHLUNG'
          | 'SODEXO'
          | 'BAYERNETS'
          | 'EDENRED'
        )[];
        enabledPaymentChannels?: {
          [key: string]: (
            | 'APP'
            | 'CASH'
            | 'CARD'
            | 'LIEFERANDO'
            | 'LIEFERANDO_CASH'
            | 'WOLT'
            | 'FOODPANDA'
            | 'UBEREATS'
            | 'FOOD2GO'
            | 'MCHOICE'
            | 'LEBEN'
            | 'PAYPAL'
            | 'UEBERWEISUNG'
            | 'EC'
            | 'VISA'
            | 'MASTERCARD'
            | 'AMERICAN_EXPRESS'
            | 'JCB'
            | 'STRIPE'
            | 'GUTSCHEIN'
            | 'KAIYUAN'
            | 'SONSTIGE'
            | 'ORDER_SMART'
            | 'ALLO_PAY'
            | 'ALLO_PAY_LINK'
            | 'ALLO_PAY_ONLINE'
            | 'OFFENE_RECHNUNG'
            | 'KAUF_AUF_RECHNUNG'
            | 'ANZAHLUNG'
            | 'SODEXO'
            | 'BAYERNETS'
            | 'EDENRED'
          )[];
        };
        hasPickup?: boolean;
        /** Format: int32 */
        pickupRequests?: number;
        hasDelivery?: boolean;
        hasScan?: boolean;
        takeawayThreshold?: number;
        takeawayAreas?: string[];
        expressCheckout?: boolean;
        oneClickCheckout?: boolean;
        deliveryFee?: number;
        website?: string;
        openingHours?: {
          [key: string]: components['schemas']['Period'][];
        };
        menus?: components['schemas']['Menu'][];
        galleries?: components['schemas']['Gallery'][];
        isOpen?: boolean;
        configuration?: components['schemas']['RestaurantConfiguration'];
        googlePlaceId?: string;
        googleReservationEnabled?: boolean;
        webshopUrl?: string;
        reservationUrl?: string;
        giftCardUrl?: string;
        isParent?: boolean;
        parentRestaurantId?: string;
        branchRestaurantIds?: string[];
        branchRestaurants?: unknown[];
      }[];
    });
    RestaurantBrazil: {
      serializationType: 'RestaurantBrazil';
    } & (Omit<components['schemas']['Restaurant'], 'serializationType'> & {
      branchRestaurants?: {
        id?: string;
        /** Format: date-time */
        creationTime?: string;
        /** Format: date-time */
        modificationTime?: string;
        /** @enum {string} */
        mode?: 'LIVE' | 'EXPLORATION';
        name?: string;
        slug?: string;
        intlName?: components['schemas']['LocalizedString'];
        description?: string;
        intlDescription?: components['schemas']['LocalizedString'];
        address?: components['schemas']['Address'];
        avatarId?: string;
        coverId?: string;
        code?: string;
        tables?: unknown[];
        rating?: number;
        hidden?: boolean;
        taxId?: string;
        vatNumber?: string;
        phone?: string;
        email?: string;
        organizationId?: string;
        nameI18n?: {
          [key: string]: string;
        };
        descriptionI18n?: {
          [key: string]: string;
        };
        timezone?: string;
        logoUrl?: string;
        thumbnailUrl?: string;
        tags?: components['schemas']['Tag'][];
        tagIds?: string[];
        notificationMobile?: string;
        notificationEmail?: string;
        paymentChannels?: (
          | 'APP'
          | 'CASH'
          | 'CARD'
          | 'LIEFERANDO'
          | 'LIEFERANDO_CASH'
          | 'WOLT'
          | 'FOODPANDA'
          | 'UBEREATS'
          | 'FOOD2GO'
          | 'MCHOICE'
          | 'LEBEN'
          | 'PAYPAL'
          | 'UEBERWEISUNG'
          | 'EC'
          | 'VISA'
          | 'MASTERCARD'
          | 'AMERICAN_EXPRESS'
          | 'JCB'
          | 'STRIPE'
          | 'GUTSCHEIN'
          | 'KAIYUAN'
          | 'SONSTIGE'
          | 'ORDER_SMART'
          | 'ALLO_PAY'
          | 'ALLO_PAY_LINK'
          | 'ALLO_PAY_ONLINE'
          | 'OFFENE_RECHNUNG'
          | 'KAUF_AUF_RECHNUNG'
          | 'ANZAHLUNG'
          | 'SODEXO'
          | 'BAYERNETS'
          | 'EDENRED'
        )[];
        enabledPaymentChannels?: {
          [key: string]: (
            | 'APP'
            | 'CASH'
            | 'CARD'
            | 'LIEFERANDO'
            | 'LIEFERANDO_CASH'
            | 'WOLT'
            | 'FOODPANDA'
            | 'UBEREATS'
            | 'FOOD2GO'
            | 'MCHOICE'
            | 'LEBEN'
            | 'PAYPAL'
            | 'UEBERWEISUNG'
            | 'EC'
            | 'VISA'
            | 'MASTERCARD'
            | 'AMERICAN_EXPRESS'
            | 'JCB'
            | 'STRIPE'
            | 'GUTSCHEIN'
            | 'KAIYUAN'
            | 'SONSTIGE'
            | 'ORDER_SMART'
            | 'ALLO_PAY'
            | 'ALLO_PAY_LINK'
            | 'ALLO_PAY_ONLINE'
            | 'OFFENE_RECHNUNG'
            | 'KAUF_AUF_RECHNUNG'
            | 'ANZAHLUNG'
            | 'SODEXO'
            | 'BAYERNETS'
            | 'EDENRED'
          )[];
        };
        hasPickup?: boolean;
        /** Format: int32 */
        pickupRequests?: number;
        hasDelivery?: boolean;
        hasScan?: boolean;
        takeawayThreshold?: number;
        takeawayAreas?: string[];
        expressCheckout?: boolean;
        oneClickCheckout?: boolean;
        deliveryFee?: number;
        website?: string;
        openingHours?: {
          [key: string]: components['schemas']['Period'][];
        };
        menus?: components['schemas']['Menu'][];
        galleries?: components['schemas']['Gallery'][];
        isOpen?: boolean;
        configuration?: components['schemas']['RestaurantConfiguration'];
        googlePlaceId?: string;
        googleReservationEnabled?: boolean;
        webshopUrl?: string;
        reservationUrl?: string;
        giftCardUrl?: string;
        isParent?: boolean;
        parentRestaurantId?: string;
        branchRestaurantIds?: string[];
        branchRestaurants?: unknown[];
      }[];
      cnpj?: string;
      ie?: string;
      municipalRegistry?: components['schemas']['MunicipalRegistry'];
    });
    RestaurantConfiguration: {
      restaurantId?: string;
      printByMenu?: boolean;
      printByMenuItem?: boolean;
      printByAddition?: boolean;
      hasReservations?: boolean;
      hasDATEVExport?: boolean;
      hasTakeaway?: boolean;
      hasScanToOrder?: boolean;
      hasOngoingItems?: boolean;
      hasExpress?: boolean;
      partialCheckout?: boolean;
      newCheckout?: boolean;
      disableDailyReportPrintout?: boolean;
      disableDigitalReceiptPrintout?: boolean;
      showInventory?: boolean;
      views?: (
        | 'TERMINAL'
        | 'ACTIVITY'
        | 'PLANNER'
        | 'ORDERS'
        | 'MENU'
        | 'RECEIPTS'
        | 'CUSTOMERS'
        | 'PICKUP'
        | 'PAYMENTS_HISTORY'
        | 'REPORTING'
        | 'MY_REPORTING'
        | 'TAX_REPORTING'
        | 'MONTHLY_REPORTING'
        | 'CASH_REGISTER'
        | 'MENU_EDITOR'
        | 'RESERVATIONS'
        | 'PROCUREMENT'
        | 'ANALYTICS'
        | 'PAYMENTS'
        | 'MARKETING'
        | 'FEEDBACK'
        | 'SETTINGS'
      )[];
      disableCancelledItemPrintout?: boolean;
      pin?: string;
      discountPin?: string;
      /** @enum {string} */
      restaurantServiceMode?: 'TABLE' | 'EXPRESS' | 'HYBRID';
      onboardCompleted?: boolean;
      /** @enum {string} */
      defaultLanguage?:
        | 'en'
        | 'de'
        | 'zh'
        | 'bg'
        | 'cs'
        | 'da'
        | 'el'
        | 'es'
        | 'et'
        | 'fi'
        | 'fr'
        | 'hr'
        | 'hu'
        | 'it'
        | 'ja'
        | 'ko'
        | 'nl'
        | 'pl'
        | 'ro'
        | 'ru'
        | 'sq'
        | 'th'
        | 'tr'
        | 'uk'
        | 'vi'
        | 'zh_CN'
        | 'zh_HK'
        | 'pt_BR'
        | 'pt_PT'
        | 'en_US'
        | 'en_GB'
        | 'de_DE'
        | 'de_AT'
        | 'de_CH'
        | 'de_LU'
        | 'el_GR'
        | 'ko_KR'
        | 'bg_BG'
        | 'cs_CZ'
        | 'da_DK'
        | 'es_ES'
        | 'et_EE'
        | 'fi_FI'
        | 'fr_FR'
        | 'hr_HR'
        | 'hu_HU'
        | 'it_IT'
        | 'ja_JP'
        | 'nl_NL'
        | 'pl_PL'
        | 'ro_RO'
        | 'ru_RU'
        | 'sq_AL'
        | 'th_TH'
        | 'tr_TR'
        | 'uk_UA'
        | 'vi_VN'
        | 'pt'
        | 'hi'
        | 'hi_IN'
        | 'zt'
        | 'de_BE'
        | 'en_CA';
      additionalLanguages?: (
        | 'en'
        | 'de'
        | 'zh'
        | 'bg'
        | 'cs'
        | 'da'
        | 'el'
        | 'es'
        | 'et'
        | 'fi'
        | 'fr'
        | 'hr'
        | 'hu'
        | 'it'
        | 'ja'
        | 'ko'
        | 'nl'
        | 'pl'
        | 'ro'
        | 'ru'
        | 'sq'
        | 'th'
        | 'tr'
        | 'uk'
        | 'vi'
        | 'zh_CN'
        | 'zh_HK'
        | 'pt_BR'
        | 'pt_PT'
        | 'en_US'
        | 'en_GB'
        | 'de_DE'
        | 'de_AT'
        | 'de_CH'
        | 'de_LU'
        | 'el_GR'
        | 'ko_KR'
        | 'bg_BG'
        | 'cs_CZ'
        | 'da_DK'
        | 'es_ES'
        | 'et_EE'
        | 'fi_FI'
        | 'fr_FR'
        | 'hr_HR'
        | 'hu_HU'
        | 'it_IT'
        | 'ja_JP'
        | 'nl_NL'
        | 'pl_PL'
        | 'ro_RO'
        | 'ru_RU'
        | 'sq_AL'
        | 'th_TH'
        | 'tr_TR'
        | 'uk_UA'
        | 'vi_VN'
        | 'pt'
        | 'hi'
        | 'hi_IN'
        | 'zt'
        | 'de_BE'
        | 'en_CA'
      )[];
      templateConfig?: components['schemas']['TemplateConfig'];
      /** Format: int32 */
      estimatedDiningTime?: number;
      /** Format: int32 */
      estimatedPreparationTime?: number;
      checkoutAutoClose?: boolean;
      taxReportConfig?: components['schemas']['TaxReportConfig'];
      pollPrinterAccountIds?: string[];
      datevConfiguration?: components['schemas']['DatevConfiguration'];
      generateTipRecordInCashRegisterForDailyClosing?: boolean;
      createMenuSnapshot?: boolean;
      autoSyncWoltMenu?: boolean;
      partnerConfigs?: components['schemas']['PartnerConfig'][];
      dailyReportConfig?: components['schemas']['DailyReportConfig'];
      receiptOption?: components['schemas']['ReceiptOption'];
      useUpdatedMenuEditor?: boolean;
      useUpdatedTerminal?: boolean;
      useUpdatedMobileTerminal?: boolean;
      useUpdatedCheckout?: boolean;
      useUpdatedMobileCheckout?: boolean;
      takeawayConfig?: components['schemas']['TakeawayConfig'];
      webshopConfig?: components['schemas']['WebshopConfig'];
      terminalConfig?: components['schemas']['TerminalConfig'];
      preparationTimeInMinutes?: {
        [key: string]: number;
      };
      hasFlashTakeaway?: boolean;
      allowTableSelectionInExpress?: boolean;
      allowPagerInputInExpress?: boolean;
      createPayoutOnDailyClosing?: boolean;
      giftCardCheckoutEnabled?: boolean;
      giftCardSettings?: components['schemas']['GiftCardSettings'];
      courseConfig?: components['schemas']['CourseConfig'];
      scanToOrderConfig?: components['schemas']['ScanToOrderConfig'];
      tabletOrderingConfig?: components['schemas']['TabletOrderingConfig'];
      kioskConfig?: components['schemas']['KioskConfig'];
      disableCancellationReceipt?: boolean;
      enableTerminalPaymentReceipt?: boolean;
      hideItemsWithPriceZero?: boolean;
      hideMenuItemNotesFromCustomers?: boolean;
      orderActions?: components['schemas']['OrderAction'][];
      inventoryConfig?: components['schemas']['InventoryConfig'];
      enableTakeawayInDineIn?: boolean;
      smartschankConfig?: components['schemas']['SmartschankConfig'];
      fiskalyAutoReportingEnabled?: boolean;
      generatePassKitCard?: boolean;
      /** Format: int32 */
      minHoursReportCloseInterval?: number;
      /** Format: int32 */
      warningThresholdReportCloseInHours?: number;
      disableSendToKitchenConfirmation?: boolean;
      receiptConfig?: components['schemas']['ReceiptConfig'];
      enableKeyboardOrderingTerminal?: boolean;
      refundConfig?: components['schemas']['RefundConfig'];
      frontEndSettings?: {
        [key: string]: unknown;
      };
      displayOrderedItemsTotalsInMenu?: boolean;
      disableFinalReceiptForSplitPayments?: boolean;
      crmSettings?: components['schemas']['CrmSettings'];
      dslSettings?: components['schemas']['DslCallSettings'];
      enabledSingleKitchenPrintoutForTakeawayOrders?: boolean;
      targetKitchenPrinterId?: string;
      deepCopyReorderingEnabled?: boolean;
      openInvoiceSettings?: components['schemas']['OpenInvoiceSettings'];
      customExtrasEnabled?: boolean;
      useFirstOrderingInMonthlyReports?: boolean;
    };
    RestaurantTag: {
      id?: string;
      label?: string;
      /** Format: int32 */
      maxQtdPerPerson?: number;
      /** Format: int32 */
      maxQtdPerPersonPerOrdering?: number;
      /** Format: int32 */
      minQtdPerPersonPerOrderingThatTriggersBlocking?: number;
      /** Format: int32 */
      blockingTimeInMinIfMinQtdPerPersonPerOrderingReached?: number;
    };
    ScanToOrderConfig: {
      guestPinEnabled?: boolean;
      guestApprovalPin?: string;
      /** Format: int32 */
      defaultPartySize?: number;
      /** Format: int32 */
      maxQtdPerPerson?: number;
      /** Format: int32 */
      maxQtdPerPersonPerOrdering?: number;
      /** Format: int32 */
      minQtdPerPersonPerOrderingThatTriggersBlocking?: number;
      /** Format: int32 */
      blockingTimeInMinIfMinQtdPerPersonPerOrderingReached?: number;
    };
    SmartschankConfig: {
      ip?: string;
      enabled?: boolean;
    };
    Supplier: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      name?: string;
      legalName?: string;
      address?: components['schemas']['Address'];
    };
    Table: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      deleted?: boolean;
      restaurantId?: string;
      floorId?: string;
      code?: string;
      label?: string;
      labelI18n?: {
        [key: string]: string;
      };
      description?: string;
      descriptionI18n?: {
        [key: string]: string;
      };
      /** @enum {string} */
      status?: 'FREE' | 'TAKEN' | 'PAYMENT_REQUESTED';
      layout?: components['schemas']['TableLayout'];
      customerId?: string;
      orderId?: string;
      orderingDeviceId?: string;
      orderingDeviceIds?: string[];
      /** @enum {string} */
      category?: 'INSIDE' | 'GARDEN' | 'TERRACE' | 'OUTSIDE';
      /** Format: int32 */
      minimumCapacity?: number;
      /** Format: int32 */
      capacity?: number;
      /** Format: int32 */
      order?: number;
      printers?: components['schemas']['Printer'][];
      printerIds?: string[];
      reservable?: boolean;
      zoneId?: string;
      /** @enum {string} */
      language?:
        | 'en'
        | 'de'
        | 'zh'
        | 'bg'
        | 'cs'
        | 'da'
        | 'el'
        | 'es'
        | 'et'
        | 'fi'
        | 'fr'
        | 'hr'
        | 'hu'
        | 'it'
        | 'ja'
        | 'ko'
        | 'nl'
        | 'pl'
        | 'ro'
        | 'ru'
        | 'sq'
        | 'th'
        | 'tr'
        | 'uk'
        | 'vi'
        | 'zh_CN'
        | 'zh_HK'
        | 'pt_BR'
        | 'pt_PT'
        | 'en_US'
        | 'en_GB'
        | 'de_DE'
        | 'de_AT'
        | 'de_CH'
        | 'de_LU'
        | 'el_GR'
        | 'ko_KR'
        | 'bg_BG'
        | 'cs_CZ'
        | 'da_DK'
        | 'es_ES'
        | 'et_EE'
        | 'fi_FI'
        | 'fr_FR'
        | 'hr_HR'
        | 'hu_HU'
        | 'it_IT'
        | 'ja_JP'
        | 'nl_NL'
        | 'pl_PL'
        | 'ro_RO'
        | 'ru_RU'
        | 'sq_AL'
        | 'th_TH'
        | 'tr_TR'
        | 'uk_UA'
        | 'vi_VN'
        | 'pt'
        | 'hi'
        | 'hi_IN'
        | 'zt'
        | 'de_BE'
        | 'en_CA';
    };
    TableLayout: {
      /** Format: int32 */
      x?: number;
      /** Format: int32 */
      y?: number;
      /** Format: int32 */
      w?: number;
      /** Format: int32 */
      h?: number;
      /** @enum {string} */
      shape?: 'SQUARE' | 'CIRCLE';
      /** Format: int32 */
      angle?: number;
    };
    TabletOrderingConfig: {
      waiterPinEnabled?: boolean;
      waiterPin?: string;
    };
    Tag: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      /** @enum {string} */
      category?: 'TYPE' | 'CUISINE' | 'LIFESTYLE' | 'ALLERGIES' | 'INGREDIENT';
      identifier?: string;
      label?: string;
      labelI18n?: {
        [key: string]: string;
      };
    };
    TakeawayConfig: {
      /** Format: int32 */
      bufferTime?: number;
      wolt?: components['schemas']['WoltConfig'];
      lieferando?: components['schemas']['LieferandoConfig'];
      allowDriverAssignment?: boolean;
      printDriverOnReceipt?: boolean;
      orderReceiptPrinterId?: string;
      orderReceiptPrintStatus?: (
        | 'OPEN'
        | 'PAYMENT_REQUESTED'
        | 'PENDING'
        | 'PREPARING'
        | 'READY'
        | 'DELIVERING'
        | 'CLOSED'
        | 'CANCELLED'
      )[];
    };
    TaxReportConfig: {
      autoClosure?: boolean;
      closureTime?: string;
    };
    TemplateConfig: {
      capabilities?: ('DINE_IN' | 'TAKEAWAY' | 'RESERVATION' | 'EXPRESS')[];
    };
    TerminalConfig: {
      tipEnabled?: boolean;
    };
    UnseenOrder: {
      disabled?: boolean;
      /** @enum {string} */
      action?: 'ACCEPT' | 'REJECT';
      /** Format: int32 */
      waitingTimeInSeconds?: number;
    };
    Vehicle: {
      brand?: string;
      model?: string;
      color?: string;
      licensePlate?: string;
      type?: string;
      isAutonomous?: boolean;
    };
    WebshopConfig: {
      printWebshopUrlQRCodeOnFinalReceipt?: boolean;
      printAddressQrCodeForDriver?: boolean;
      deliveryAreas?: components['schemas']['DeliveryArea'][];
      deliveryRadius?: components['schemas']['DeliveryRadius'][];
      /** @enum {string} */
      deliveryFeeMode?: 'ZIP_CODES' | 'KM_RADIUS';
      /** Format: int32 */
      incomingOrderSound?: number;
      pollSoundNotificationAccountIds?: string[];
      unseenOrder?: components['schemas']['UnseenOrder'];
      pickupDiscountPercentage?: number;
      promotionText?: string;
      disableTakeawayEmailCopy?: boolean;
      disableNewOrderReceipt?: boolean;
      printUnpaidNewOrderReceipt?: boolean;
      pickupPaymentChannels?: (
        | 'APP'
        | 'CASH'
        | 'CARD'
        | 'LIEFERANDO'
        | 'LIEFERANDO_CASH'
        | 'WOLT'
        | 'FOODPANDA'
        | 'UBEREATS'
        | 'FOOD2GO'
        | 'MCHOICE'
        | 'LEBEN'
        | 'PAYPAL'
        | 'UEBERWEISUNG'
        | 'EC'
        | 'VISA'
        | 'MASTERCARD'
        | 'AMERICAN_EXPRESS'
        | 'JCB'
        | 'STRIPE'
        | 'GUTSCHEIN'
        | 'KAIYUAN'
        | 'SONSTIGE'
        | 'ORDER_SMART'
        | 'ALLO_PAY'
        | 'ALLO_PAY_LINK'
        | 'ALLO_PAY_ONLINE'
        | 'OFFENE_RECHNUNG'
        | 'KAUF_AUF_RECHNUNG'
        | 'ANZAHLUNG'
        | 'SODEXO'
        | 'BAYERNETS'
        | 'EDENRED'
      )[];
      deliveryPaymentChannels?: (
        | 'APP'
        | 'CASH'
        | 'CARD'
        | 'LIEFERANDO'
        | 'LIEFERANDO_CASH'
        | 'WOLT'
        | 'FOODPANDA'
        | 'UBEREATS'
        | 'FOOD2GO'
        | 'MCHOICE'
        | 'LEBEN'
        | 'PAYPAL'
        | 'UEBERWEISUNG'
        | 'EC'
        | 'VISA'
        | 'MASTERCARD'
        | 'AMERICAN_EXPRESS'
        | 'JCB'
        | 'STRIPE'
        | 'GUTSCHEIN'
        | 'KAIYUAN'
        | 'SONSTIGE'
        | 'ORDER_SMART'
        | 'ALLO_PAY'
        | 'ALLO_PAY_LINK'
        | 'ALLO_PAY_ONLINE'
        | 'OFFENE_RECHNUNG'
        | 'KAUF_AUF_RECHNUNG'
        | 'ANZAHLUNG'
        | 'SODEXO'
        | 'BAYERNETS'
        | 'EDENRED'
      )[];
      orderWithGoogleEnabled?: boolean;
      webshopHours?: {
        [key: string]: components['schemas']['Period'][];
      };
      hasDifferentWebshopHours?: boolean;
      allowGiftCards?: boolean;
      allowPromoCodes?: boolean;
      /** @enum {string} */
      status?: 'ONLINE' | 'OFFLINE';
      useNewWebshop?: boolean;
      covers?: components['schemas']['Image'][];
    };
    WoltConfig: {
      useV2Integration?: boolean;
    };
    AddPromotionRequest: {
      promoCode: string;
    };
    AddItemRequest: {
      code: string;
      /** Format: int32 */
      qtd: number;
      measurementValue?: number;
      notes?: string;
      selectedOptions?: {
        [key: string]: {
          [key: string]: components['schemas']['OptionDTO'];
        };
      };
      selectedExtras?: {
        [key: string]: {
          [key: string]: components['schemas']['ExtraDTO'];
        };
      };
      selectedNotes?: components['schemas']['Note'][];
    };
    ExtraDTO: {
      id: string;
      /** Format: int32 */
      qtd: number;
    };
    OptionDTO: {
      id: string;
      /** Format: int32 */
      qtd: number;
    };
    CartPaymentRequest: {
      /** @enum {string} */
      paymentChannel:
        | 'APP'
        | 'CASH'
        | 'CARD'
        | 'LIEFERANDO'
        | 'LIEFERANDO_CASH'
        | 'WOLT'
        | 'FOODPANDA'
        | 'UBEREATS'
        | 'FOOD2GO'
        | 'MCHOICE'
        | 'LEBEN'
        | 'PAYPAL'
        | 'UEBERWEISUNG'
        | 'EC'
        | 'VISA'
        | 'MASTERCARD'
        | 'AMERICAN_EXPRESS'
        | 'JCB'
        | 'STRIPE'
        | 'GUTSCHEIN'
        | 'KAIYUAN'
        | 'SONSTIGE'
        | 'ORDER_SMART'
        | 'ALLO_PAY'
        | 'ALLO_PAY_LINK'
        | 'ALLO_PAY_ONLINE'
        | 'OFFENE_RECHNUNG'
        | 'KAUF_AUF_RECHNUNG'
        | 'ANZAHLUNG'
        | 'SODEXO'
        | 'BAYERNETS'
        | 'EDENRED';
    };
    UpdateOrderCartRequest: {
      /** @enum {string} */
      orderType?: 'PICKUP' | 'DELIVERY' | 'DINE_IN' | 'EXPRESS';
      notes?: string;
    };
    WebshopSchedulingTimes: {
      isOpen?: boolean;
      items?: components['schemas']['WebshopSchedulingTimesDay'][];
    };
    WebshopSchedulingTimesDay: {
      /** Format: date */
      day?: string;
      times?: string[];
    };
    Coordinates: {
      /** Format: double */
      lat?: number;
      /** Format: double */
      lng?: number;
    };
    WebshopOrder: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      /** Format: date-time */
      closeTime?: string;
      closedBy?: string;
      autoAccept?: boolean;
      /** Format: int32 */
      number?: number;
      restaurantId?: string;
      restaurantSlug?: string;
      restaurant?: components['schemas']['WebshopOrderRestaurant'];
      customerId?: string;
      customer?: components['schemas']['Customer'];
      userId?: string;
      accountId?: string;
      isWaiter?: boolean;
      isNew?: boolean;
      /** @enum {string} */
      partnerId?: 'UBER_EATS' | 'WOLT' | 'LIEFERANDO' | 'OTHER';
      items?: components['schemas']['OrderItem'][];
      /** @enum {string} */
      paymentOption?: 'ALL' | 'OWN' | 'SPLIT' | 'CUSTOM';
      /** @enum {string} */
      status?:
        | 'OPEN'
        | 'PAYMENT_REQUESTED'
        | 'PENDING'
        | 'PREPARING'
        | 'READY'
        | 'DELIVERING'
        | 'CLOSED'
        | 'CANCELLED';
      /** @enum {string} */
      type?: 'PICKUP' | 'DELIVERY' | 'DINE_IN' | 'EXPRESS';
      /** @enum {string} */
      origin?: 'POS' | 'KIOSK' | 'TABLET' | 'WEBSHOP' | 'PARTNER' | 'SCAN_TO_ORDER';
      activity?: components['schemas']['OrderActivity'];
      invalidState?: boolean;
      total?: number;
      itemsTotal?: number;
      totalDiscounts?: number;
      taxesTotal?: number;
      totalWithoutTaxes?: number;
      orderApprovalId?: string;
      orderRejectId?: string;
      refundedOrderId?: string;
      refundOrderId?: string;
      recoveredOrderId?: string;
      orderApprovalRequestId?: string;
      orderApprovalRequest?: components['schemas']['OrderApprovalRequest'];
      orderServingId?: string;
      checkoutId?: string;
      payments?: components['schemas']['Payment'][];
      identifier?: string;
      pickupTime?: string;
      /** Format: date-time */
      takeawayDate?: string;
      isAsap?: boolean;
      /** Format: date-time */
      estimatedPickupTime?: string;
      /** Format: date-time */
      requestedPickupTime?: string;
      /** Format: date-time */
      requestedDeliveryTime?: string;
      /** Format: date-time */
      estimatedDeliveryTime?: string;
      /** Format: date-time */
      actualDeliveryTime?: string;
      notes?: string;
      deliveryFee?: number;
      receiptStripeUrl?: string;
      notificationSent?: boolean;
      verificationRequestSent?: boolean;
      reservationId?: string;
      /** Format: int32 */
      dishCount?: number;
      /** Format: int32 */
      beverageCount?: number;
      /** @enum {string} */
      mode?: 'LIVE' | 'EXPLORATION';
      /** @enum {string} */
      paymentStatus?: 'NOT_PAID' | 'PAYING' | 'PAID';
      partnerOrderInfo?: components['schemas']['PartnerOrderInfo'];
      /** Format: int32 */
      duration?: number;
      /** Format: int32 */
      remainingDuration?: number;
      /** Format: int32 */
      estimatedDiningTime?: number;
      /** Format: int32 */
      estimatedPreparationTime?: number;
      formattedDeliveryAddress?: string;
      /** Format: date-time */
      acceptedAt?: string;
      timeZone?: string;
      guestMonitorId?: string;
      sendToGuestMonitor?: boolean;
      /** Format: int32 */
      partySize?: number;
      orderingMode?: components['schemas']['OrderingMode'];
      orderActions?: components['schemas']['OrderAction'][];
      toGo?: boolean;
      pagerIdentifier?: string;
      cartId?: string;
      orderTagLimit?: components['schemas']['OrderTagLimit'];
    };
    WebshopOrderRestaurant: {
      name?: string;
      coordinates?: components['schemas']['Coordinates'];
      formattedAddress?: string;
    };
    OrderStatusResponseContract: {
      /** @enum {string} */
      status?:
        | 'OPEN'
        | 'PAYMENT_REQUESTED'
        | 'PENDING'
        | 'PREPARING'
        | 'READY'
        | 'DELIVERING'
        | 'CLOSED'
        | 'CANCELLED';
      /** Format: date-time */
      estimatedDeliveryTime?: string;
    };
    WebshopBranchRestaurant: {
      slug?: string;
      name?: string;
      logo?: string;
      hasPickup?: boolean;
      hasDelivery?: boolean;
      address?: string;
      openingHours?: {
        [key: string]: components['schemas']['Period'][];
      };
      timezone?: string;
      deliveryRadius?: components['schemas']['DeliveryRadius'][];
      /** @enum {string} */
      status?: 'ONLINE' | 'OFFLINE';
      covers?: components['schemas']['Image'][];
    };
    WebshopRestaurant: {
      id?: string;
      name?: string;
      logo?: string;
      description?: string;
      tags?: string[];
      services?: ('DINE_IN' | 'TAKEAWAY' | 'RESERVATION' | 'EXPRESS')[];
      hasPickup?: boolean;
      hasDelivery?: boolean;
      phone?: string;
      email?: string;
      website?: string;
      address?: string;
      mapUrl?: string;
      mapCoordinates?: components['schemas']['Coordinates'];
      galleries?: components['schemas']['Gallery'][];
      openingHours?: {
        [key: string]: components['schemas']['Period'][];
      };
      timezone?: string;
      branchRestaurants?: components['schemas']['WebshopBranchRestaurant'][];
      covers?: components['schemas']['Image'][];
      pickupPaymentChannels?: (
        | 'APP'
        | 'CASH'
        | 'CARD'
        | 'LIEFERANDO'
        | 'LIEFERANDO_CASH'
        | 'WOLT'
        | 'FOODPANDA'
        | 'UBEREATS'
        | 'FOOD2GO'
        | 'MCHOICE'
        | 'LEBEN'
        | 'PAYPAL'
        | 'UEBERWEISUNG'
        | 'EC'
        | 'VISA'
        | 'MASTERCARD'
        | 'AMERICAN_EXPRESS'
        | 'JCB'
        | 'STRIPE'
        | 'GUTSCHEIN'
        | 'KAIYUAN'
        | 'SONSTIGE'
        | 'ORDER_SMART'
        | 'ALLO_PAY'
        | 'ALLO_PAY_LINK'
        | 'ALLO_PAY_ONLINE'
        | 'OFFENE_RECHNUNG'
        | 'KAUF_AUF_RECHNUNG'
        | 'ANZAHLUNG'
        | 'SODEXO'
        | 'BAYERNETS'
        | 'EDENRED'
      )[];
      deliveryPaymentChannels?: (
        | 'APP'
        | 'CASH'
        | 'CARD'
        | 'LIEFERANDO'
        | 'LIEFERANDO_CASH'
        | 'WOLT'
        | 'FOODPANDA'
        | 'UBEREATS'
        | 'FOOD2GO'
        | 'MCHOICE'
        | 'LEBEN'
        | 'PAYPAL'
        | 'UEBERWEISUNG'
        | 'EC'
        | 'VISA'
        | 'MASTERCARD'
        | 'AMERICAN_EXPRESS'
        | 'JCB'
        | 'STRIPE'
        | 'GUTSCHEIN'
        | 'KAIYUAN'
        | 'SONSTIGE'
        | 'ORDER_SMART'
        | 'ALLO_PAY'
        | 'ALLO_PAY_LINK'
        | 'ALLO_PAY_ONLINE'
        | 'OFFENE_RECHNUNG'
        | 'KAUF_AUF_RECHNUNG'
        | 'ANZAHLUNG'
        | 'SODEXO'
        | 'BAYERNETS'
        | 'EDENRED'
      )[];
      printWebshopUrlQRCodeOnFinalReceipt?: boolean;
      printAddressQrCodeForDriver?: boolean;
      deliveryAreas?: components['schemas']['DeliveryArea'][];
      deliveryRadius?: components['schemas']['DeliveryRadius'][];
      /** @enum {string} */
      deliveryFeeMode?: 'ZIP_CODES' | 'KM_RADIUS';
      /** Format: int32 */
      incomingOrderSound?: number;
      pollSoundNotificationAccountIds?: string[];
      unseenOrder?: components['schemas']['UnseenOrder'];
      pickupDiscountPercentage?: number;
      promotionText?: string;
      disableTakeawayEmailCopy?: boolean;
      disableNewOrderReceipt?: boolean;
      printUnpaidNewOrderReceipt?: boolean;
      allowGiftCards?: boolean;
      allowPromoCodes?: boolean;
      /** @enum {string} */
      status?: 'ONLINE' | 'OFFLINE';
    };
    Link: {
      href?: string;
      templated?: boolean;
    };
  };
  responses: never;
  parameters: never;
  requestBodies: never;
  headers: never;
  pathItems: never;
}
export type $defs = Record<string, never>;
export interface operations {
  getCart: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['OrderCart'];
        };
      };
    };
  };
  generateNewCart: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': components['schemas']['NewOrderCartRequest'];
      };
    };
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['OrderCart'];
        };
      };
    };
  };
  updateCart: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': components['schemas']['UpdateOrderCartRequest'];
      };
    };
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['OrderCart'];
        };
      };
    };
  };
  addPromotionToCart: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': components['schemas']['AddPromotionRequest'];
      };
    };
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['OrderCart'];
        };
      };
    };
  };
  addItemQtd: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': components['schemas']['AddItemRequest'];
      };
    };
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['OrderCart'];
        };
      };
    };
  };
  resetItems: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['OrderCart'];
        };
      };
    };
  };
  requestCartPayment: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': components['schemas']['CartPaymentRequest'];
      };
    };
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['OrderCart'];
        };
      };
    };
  };
  removeItem: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
        itemId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['OrderCart'];
        };
      };
    };
  };
  updateItem: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
        itemId: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': components['schemas']['AddItemRequest'];
      };
    };
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['OrderCart'];
        };
      };
    };
  };
  getRestaurantScheduleTimes: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['WebshopSchedulingTimes'];
        };
      };
    };
  };
  getOrder: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
        orderId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['WebshopOrder'];
        };
      };
    };
  };
  getOrderStatus: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
        orderId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['OrderStatusResponseContract'];
        };
      };
    };
  };
  getRestaurantMenus: {
    parameters: {
      query: {
        orderType: 'PICKUP' | 'DELIVERY' | 'DINE_IN' | 'EXPRESS';
      };
      header?: never;
      path: {
        restaurantId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['Menu'][];
        };
      };
    };
  };
  listRecommendationsForCart: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['MenuItem'][];
        };
      };
    };
  };
  getRestaurantBySlug: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        slug: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['WebshopRestaurant'];
        };
      };
    };
  };
  getRestaurantEnabledBySlug: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        slug: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': boolean;
        };
      };
    };
  };
  links: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/vnd.spring-boot.actuator.v3+json': {
            [key: string]: {
              [key: string]: components['schemas']['Link'];
            };
          };
          'application/vnd.spring-boot.actuator.v2+json': {
            [key: string]: {
              [key: string]: components['schemas']['Link'];
            };
          };
          'application/json': {
            [key: string]: {
              [key: string]: components['schemas']['Link'];
            };
          };
        };
      };
    };
  };
  health: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/vnd.spring-boot.actuator.v3+json': Record<string, never>;
          'application/vnd.spring-boot.actuator.v2+json': Record<string, never>;
          'application/json': Record<string, never>;
        };
      };
    };
  };
}
