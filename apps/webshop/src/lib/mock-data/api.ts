import { orders } from './orders';
import { places } from './places';
import { products } from './products';
import { brand, multiBrand, restaurants } from './restaurants';
import { user } from './user';

const SLEEP_MIN = 128;
const SLEEP_MAX = 512;

// simulate a response delay in the API
const sleep = (min: number = SLEEP_MIN, max: number = SLEEP_MAX) => {
  return new Promise<void>((resolve) => {
    const delay = Math.random() * (max - min) + min;
    setTimeout(() => resolve(), delay);
  });
};

export const fetchMultiBrand = async (slug: string) => {
  await sleep();

  if (slug === 'house-of-food') {
    return multiBrand;
  }

  return null;
};

export const fetchBrand = async (slug: string) => {
  await sleep();

  if (slug === 'chicken-house') {
    return brand;
  }

  return null;
};

export const fetchRestaurant = async (slug: string) => {
  if (slug in restaurants) {
    return restaurants[slug as keyof typeof restaurants];
  }

  return null;
};

export const fetchRestaurantsLocationSuggestions = async (slug: string) => {
  await sleep();

  const allRestaurants = Object.values(restaurants);
  return allRestaurants.filter((restaurant) => restaurant.slug !== slug);
};

export const fetchProducts = async () => {
  return products;
};

export const fetchCrossSellProducts = async (ids: string[]) => {
  await sleep();

  return [...products]
    .reverse()
    .filter((product) => !ids.includes(product.id))
    .slice(0, 4);
};

export const fetchDiscountCode = async (code: string) => {
  await sleep();

  if (code === 'ALLOFF') {
    return { code, discount: 1 };
  }

  if (code === '15OFF') {
    return { code, discount: 0.15 };
  }

  return null;
};

export const fetchPlacesByQuery = async (query: string) => {
  await sleep();

  if (!query || query.length === 0 || query.length > 7) {
    return [];
  }

  return [...places].splice(0, Math.max(1, places.length * 2 - query.length));
};

export const fetchPlaceFromCoordinates = async (coordinates: {
  lat: number;
  lng: number;
}) => {
  await sleep();

  return (
    places.find(
      (place) =>
        place.coordinates.lat === coordinates.lat &&
        place.coordinates.lng === coordinates.lng
    ) || places[0]
  );
};

/* return window (in minutes) off the earliest and latest possible delivery time */
export const getCurrentDeliveryWindow = async () => {
  await sleep();

  return [20, 50];
};

export const fetchDeliverySlots = async (date: Date) => {
  await sleep();

  // UTC
  const windows: { start: number; end: number }[] = [
    { start: 10, end: 14 },
    { start: 16, end: 21 },
  ];

  const baseDate = new Date(
    Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate())
  );

  return windows.flatMap((window) => {
    const hours = Array.from(
      { length: window.end - window.start },
      (_, i) => window.start + i
    );

    return hours.flatMap((hour) => {
      return [0, 15, 30, 45].map((minute) => {
        const slotTime = new Date(baseDate);
        slotTime.setUTCHours(hour, minute, 0, 0);
        return slotTime;
      });
    });
  });
};

export const fetchOrders = async () => {
  await sleep();
  return orders;
};

export const fetchOrder = async (id: string) => {
  await sleep();

  const order = orders.find((order) => order.id === id);

  if (!order) {
    throw new Error('Order not found');
  }

  return order;
};

export const fetchUser = async (_: string, password: string) => {
  await sleep();

  if (password === 'error') {
    throw new Error('Invalid email or password');
  }

  return user;
};
