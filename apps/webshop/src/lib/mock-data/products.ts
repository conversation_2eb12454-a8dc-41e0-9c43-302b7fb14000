export type Product = {
  id: string;
  images: string[];
  title: string;
  description?: string;
  allergens?: string[];
  price: number;
  promoPrice?: number;
  tags: string[];
  filters: string[];
  configuration: ProductConfiguration[];
};

export type ProductConfiguration = {
  id: string;
  title: string;
  min: number;
  max: number | null;
  options: ProductOption[];
};

export type ProductOption = {
  id: string;
  title: string;
  description?: string;
  image?: string;
  tag?: string;
  min: number;
  max: number | null;
  initialQuantity: number;
  extraUnitPrice: number;
};

const filters = {
  featured: '🔥 Featured',
  burgers: '🍔 Burgers',
  pizzas: '🍕 Pizzas',
  sides: '🍙 Sides',
  drinks: '🥤 Drinks',
};

const doubleBBQBurgerHalal: Product = {
  id: `double-bbq-burger-halal`,
  images: [
    'https://storage.googleapis.com/leviee_public/DE10555SBA-TEST/menu/gallery/01J9DXEW44PR64PF4J5SSPFBD4.png',
  ],
  title: 'Double BBQ Burger Halal',
  description: 'Double beef patty with BBQ sauce',
  price: 8.9,
  promoPrice: 6.9,
  allergens: ['Gluten', 'Soy'],
  tags: [],
  filters: [filters.featured, filters.burgers],
  configuration: [
    {
      id: 'patty',
      title: 'Choose your patty',
      min: 1,
      max: 1,
      options: [
        {
          id: 'beef',
          tag: 'Premium',
          title: 'Beef',
          description: '100% beef patty',
          image:
            'https://storage.googleapis.com/leviee_public/DE10555SBA-TEST/menu/gallery/01HX73W3TW4MB3E7KFBMKQAW2A.jpeg',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
        {
          id: 'chicken',
          title: 'Chicken',
          description: '100% chicken patty',
          image:
            'https://storage.googleapis.com/leviee_public/DE10555SBA-TEST/menu/gallery/01HX73Y213P692R3D5CVJK18JA.jpg',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 2,
        },
      ],
    },
    {
      id: 'comes-with',
      title: 'Comes with',
      min: 0,
      max: null,
      options: [
        {
          id: 'cheese',
          title: 'Cheese',
          description: 'Melted cheddar cheese',
          min: 0,
          max: 15,
          initialQuantity: 1,
          extraUnitPrice: 1.1,
        },
        {
          id: 'ketchup',
          title: 'Ketchup',
          tag: 'Spicy',
          min: 0,
          max: 1,
          initialQuantity: 1,
          extraUnitPrice: 0,
        },
        {
          id: 'mayo',
          title: 'Mayo',
          min: 0,
          max: 1,
          initialQuantity: 1,
          extraUnitPrice: 0,
        },
        {
          id: 'tomato',
          title: 'Tomato',
          min: 0,
          max: 2,
          initialQuantity: 1,
          extraUnitPrice: 1.2,
        },
      ],
    },
    {
      id: 'optionals',
      title: 'You can add',
      min: 0,
      max: null,
      options: [
        {
          id: 'bacon',
          title: 'Bacon',
          min: 0,
          max: 3,
          initialQuantity: 0,
          extraUnitPrice: 1.5,
        },
        {
          id: 'onion-rings',
          title: 'Onion rings',
          min: 0,
          max: 3,
          initialQuantity: 0,
          extraUnitPrice: 1.5,
        },
        {
          id: 'fried-egg',
          title: 'Fried egg',
          min: 0,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 1.5,
        },
      ],
    },
  ],
};

const crispers: Product = {
  id: 'crispers',
  images: [
    'https://storage.googleapis.com/leviee_public/DE10555SBA-TEST/menu/gallery/01J9DXSA8M6NZ206MWFCAZJV9T.png',
  ],
  title: 'Crispers',
  price: 2.5,
  description: 'Crispy, crunchy, and delicious',
  tags: [],
  filters: [filters.sides],
  configuration: [],
};

//https://unsplash.com/photos/5nvt9BrLaAc/download?ixid=M3wxMjA3fDB8MXxhbGx8fHx8fHx8fHwxNzQxNjI1ODY4fA&force=true&w=1920
const spicyChickenBurger: Product = {
  id: 'spicy-chicken-burger',
  images: [
    'https://storage.googleapis.com/leviee_public/DE10555SBA-TEST/menu/gallery/01J9DY1EV0K6TH8W8M59586JMV.png',
  ],
  title: 'Spicy Chicken Burger',
  price: 9.5,
  description: 'Crispy spicy chicken fillet with lettuce and mayo',
  tags: ['Spicy'],
  filters: [filters.featured, filters.burgers],
  configuration: [
    {
      id: 'spice-level',
      title: 'Choose your spice level',
      min: 1,
      max: 1,
      options: [
        {
          id: 'mild',
          title: 'Mild',
          description: 'Mild heat level',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
        {
          id: 'medium',
          title: 'Medium',
          description: 'Medium heat level',
          tag: 'Spicy',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
        {
          id: 'hot',
          title: 'Hot',
          description: 'High heat level',
          tag: 'Very Spicy',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
      ],
    },
    {
      id: 'toppings',
      title: 'Choose your toppings',
      min: 0,
      max: null,
      options: [
        {
          id: 'lettuce',
          title: 'Lettuce',
          min: 0,
          max: 1,
          initialQuantity: 1,
          extraUnitPrice: 0,
        },
        {
          id: 'tomato',
          title: 'Tomato',
          min: 0,
          max: 1,
          initialQuantity: 1,
          extraUnitPrice: 0,
        },
        {
          id: 'pickles',
          title: 'Pickles',
          min: 0,
          max: 1,
          initialQuantity: 1,
          extraUnitPrice: 0,
        },
        {
          id: 'jalapenos',
          title: 'Jalapeños',
          tag: 'Spicy',
          min: 0,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0.5,
        },
      ],
    },
    {
      id: 'sauces',
      title: 'Choose your sauces',
      min: 0,
      max: null,
      options: [
        {
          id: 'spicy-mayo',
          title: 'Spicy Mayo',
          tag: 'Spicy',
          min: 0,
          max: 1,
          initialQuantity: 1,
          extraUnitPrice: 0,
        },
        {
          id: 'ranch',
          title: 'Ranch',
          min: 0,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
        {
          id: 'buffalo',
          title: 'Buffalo Sauce',
          tag: 'Spicy',
          min: 0,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0.5,
        },
      ],
    },
  ],
};

const chickenWings: Product = {
  id: 'chicken-wings',
  images: [
    'https://storage.googleapis.com/leviee_public/DE10555SBA-TEST/menu/gallery/01J9DY3XP7H41F66QZVEDACG08.png',
  ],
  title: 'Chicken Wings',
  price: 6.5,
  tags: [],
  filters: [filters.sides],
  configuration: [
    {
      id: 'quantity',
      title: 'Choose your quantity',
      min: 1,
      max: 1,
      options: [
        {
          id: '6-pieces',
          title: '6 Pieces',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
        {
          id: '12-pieces',
          title: '12 Pieces',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 6.0,
        },
        {
          id: '18-pieces',
          title: '18 Pieces',
          tag: 'Best value',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 11.0,
        },
      ],
    },
    {
      id: 'flavor',
      title: 'Choose your flavor',
      min: 1,
      max: 1,
      options: [
        {
          id: 'buffalo',
          title: 'Buffalo',
          tag: 'Spicy',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
        {
          id: 'bbq',
          title: 'BBQ',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
        {
          id: 'honey-garlic',
          title: 'Honey Garlic',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
        {
          id: 'lemon-pepper',
          title: 'Lemon Pepper',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
      ],
    },
    {
      id: 'extras',
      title: 'Add extras',
      min: 0,
      max: null,
      options: [
        {
          id: 'ranch',
          title: 'Ranch Dip',
          min: 0,
          max: 3,
          initialQuantity: 1,
          extraUnitPrice: 0.5,
        },
        {
          id: 'blue-cheese',
          title: 'Blue Cheese Dip',
          min: 0,
          max: 3,
          initialQuantity: 0,
          extraUnitPrice: 0.5,
        },
        {
          id: 'celery-sticks',
          title: 'Celery Sticks',
          min: 0,
          max: 1,
          initialQuantity: 1,
          extraUnitPrice: 0,
        },
        {
          id: 'carrot-sticks',
          title: 'Carrot Sticks',
          min: 0,
          max: 1,
          initialQuantity: 1,
          extraUnitPrice: 0,
        },
      ],
    },
  ],
};

const cubanSandwich: Product = {
  id: 'cuban-sandwich',
  images: [
    'https://storage.googleapis.com/leviee_public/DE10555SBA-TEST/menu/gallery/01J9DY4NM92K3K5MMXHMY4G478.png',
  ],
  title: 'Cuban Sandwich',
  price: 8.0,
  description: 'A classic Cuban sandwich with ham, pork, and cheese',
  tags: [],
  filters: [filters.featured, filters.burgers],
  configuration: [
    {
      id: 'bread',
      title: 'Choose your bread',
      min: 1,
      max: 1,
      options: [
        {
          id: 'traditional',
          title: 'Traditional Cuban',
          description: 'Crispy pressed Cuban bread',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
        {
          id: 'sourdough',
          title: 'Sourdough',
          description: 'Artisanal sourdough bread',
          tag: 'Premium',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 1.0,
        },
      ],
    },
    {
      id: 'meat-preference',
      title: 'Meat options',
      min: 0,
      max: null,
      options: [
        {
          id: 'roasted-pork',
          title: 'Roasted Pork',
          min: 0,
          max: 2,
          initialQuantity: 1,
          extraUnitPrice: 2.0,
        },
        {
          id: 'ham',
          title: 'Ham',
          min: 0,
          max: 2,
          initialQuantity: 1,
          extraUnitPrice: 1.5,
        },
        {
          id: 'salami',
          title: 'Salami',
          min: 0,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 1.5,
        },
      ],
    },
    {
      id: 'extras',
      title: 'Add extras',
      min: 0,
      max: null,
      options: [
        {
          id: 'swiss-cheese',
          title: 'Swiss Cheese',
          min: 0,
          max: 2,
          initialQuantity: 1,
          extraUnitPrice: 1.0,
        },
        {
          id: 'pickles',
          title: 'Dill Pickles',
          min: 0,
          max: 1,
          initialQuantity: 1,
          extraUnitPrice: 0,
        },
        {
          id: 'mustard',
          title: 'Yellow Mustard',
          min: 0,
          max: 1,
          initialQuantity: 1,
          extraUnitPrice: 0,
        },
        {
          id: 'mojo-sauce',
          title: 'Mojo Sauce',
          description: 'Traditional Cuban garlic-citrus sauce',
          min: 0,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 1.0,
        },
      ],
    },
  ],
};

const pepperoniPizza: Product = {
  id: 'pepperoni-pizza',
  images: [
    'https://storage.googleapis.com/leviee_public/DE10555SBA-TEST/menu/gallery/01J9DY5DH9BP9R9D18REVSEC9N.png',
  ],
  title: 'Pepperoni Pizza',
  price: 10.0,
  description: 'A classic pepperoni pizza with cheese and pepperoni',
  tags: [],
  filters: [filters.pizzas],
  configuration: [
    {
      id: 'size',
      title: 'Choose your size',
      min: 1,
      max: 1,
      options: [
        {
          id: 'medium',
          title: 'Medium (12")',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
        {
          id: 'large',
          title: 'Large (14")',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 3.0,
        },
        {
          id: 'extra-large',
          title: 'Extra Large (16")',
          tag: 'Best value',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 5.0,
        },
      ],
    },
    {
      id: 'crust',
      title: 'Choose your crust',
      min: 1,
      max: 1,
      options: [
        {
          id: 'regular',
          title: 'Regular Crust',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
        {
          id: 'thin',
          title: 'Thin Crust',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
        {
          id: 'stuffed',
          title: 'Stuffed Crust',
          tag: 'Premium',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 2.0,
        },
      ],
    },
    {
      id: 'extras',
      title: 'Add extras',
      min: 0,
      max: null,
      options: [
        {
          id: 'extra-cheese',
          title: 'Extra Cheese',
          min: 0,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 2.0,
        },
        {
          id: 'extra-pepperoni',
          title: 'Extra Pepperoni',
          min: 0,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 2.0,
        },
        {
          id: 'mushrooms',
          title: 'Mushrooms',
          min: 0,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 1.5,
        },
        {
          id: 'onions',
          title: 'Onions',
          min: 0,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 1.0,
        },
        {
          id: 'bell-peppers',
          title: 'Bell Peppers',
          min: 0,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 1.0,
        },
      ],
    },
  ],
};

const cheeseBurger: Product = {
  id: 'cheese-burger',
  images: [
    'https://storage.googleapis.com/leviee_public/DE10555SBA-TEST/menu/gallery/01J9DY60G0ZD926FWJYSF6YYZQ.png',
  ],
  title: 'Classic Cheese Burger',
  price: 7.5,
  description: 'Juicy beef patty with melted cheese and fresh vegetables',
  tags: ['Popular'],
  filters: [filters.featured, filters.burgers],
  configuration: [
    {
      id: 'patty-doneness',
      title: 'How would you like your patty?',
      min: 1,
      max: 1,
      options: [
        {
          id: 'medium-rare',
          title: 'Medium Rare',
          description: 'Pink and juicy inside',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
        {
          id: 'medium',
          title: 'Medium',
          description: 'Slightly pink inside',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
        {
          id: 'well-done',
          title: 'Well Done',
          description: 'No pink inside',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
      ],
    },
    {
      id: 'toppings',
      title: 'Choose your toppings',
      min: 0,
      max: null,
      options: [
        {
          id: 'lettuce',
          title: 'Lettuce',
          min: 0,
          max: 1,
          initialQuantity: 1,
          extraUnitPrice: 0,
        },
        {
          id: 'tomato',
          title: 'Tomato',
          min: 0,
          max: 1,
          initialQuantity: 1,
          extraUnitPrice: 0,
        },
        {
          id: 'onions',
          title: 'Onions',
          min: 0,
          max: 1,
          initialQuantity: 1,
          extraUnitPrice: 0,
        },
        {
          id: 'pickles',
          title: 'Pickles',
          min: 0,
          max: 1,
          initialQuantity: 1,
          extraUnitPrice: 0,
        },
      ],
    },
    {
      id: 'extras',
      title: 'Add extras',
      min: 0,
      max: null,
      options: [
        {
          id: 'extra-cheese',
          title: 'Extra Cheese',
          min: 0,
          max: 2,
          initialQuantity: 0,
          extraUnitPrice: 1.0,
        },
        {
          id: 'bacon',
          title: 'Bacon',
          min: 0,
          max: 2,
          initialQuantity: 0,
          extraUnitPrice: 1.5,
        },
        {
          id: 'fried-egg',
          title: 'Fried Egg',
          min: 0,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 1.0,
        },
      ],
    },
    {
      id: 'sauces',
      title: 'Choose your sauces',
      min: 0,
      max: null,
      options: [
        {
          id: 'mayo',
          title: 'Mayonnaise',
          min: 0,
          max: 1,
          initialQuantity: 1,
          extraUnitPrice: 0,
        },
        {
          id: 'ketchup',
          title: 'Ketchup',
          min: 0,
          max: 1,
          initialQuantity: 1,
          extraUnitPrice: 0,
        },
        {
          id: 'mustard',
          title: 'Mustard',
          min: 0,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
        {
          id: 'bbq',
          title: 'BBQ Sauce',
          min: 0,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
      ],
    },
  ],
};

const veggieBurger: Product = {
  id: 'veggie-burger',
  images: [
    'https://storage.googleapis.com/leviee_public/DE10555SBA-TEST/menu/gallery/01J9DY9E97P5Z9JMC3K0GVJRNZ.png',
  ],
  title: 'Veggie Burger',
  price: 7.0,
  description: 'Plant-based patty with fresh toppings',
  tags: ['Veggie'],
  filters: [filters.featured, filters.burgers],
  configuration: [
    {
      id: 'patty-type',
      title: 'Choose your patty',
      min: 1,
      max: 1,
      options: [
        {
          id: 'beyond-meat',
          title: 'Beyond Meat',
          description: 'Plant-based patty that tastes like beef',
          tag: 'Premium',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 1.0,
        },
        {
          id: 'black-bean',
          title: 'Black Bean',
          description: 'House-made black bean patty',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
      ],
    },
    {
      id: 'toppings',
      title: 'Choose your toppings',
      min: 0,
      max: null,
      options: [
        {
          id: 'lettuce',
          title: 'Lettuce',
          min: 0,
          max: 1,
          initialQuantity: 1,
          extraUnitPrice: 0,
        },
        {
          id: 'tomato',
          title: 'Tomato',
          min: 0,
          max: 1,
          initialQuantity: 1,
          extraUnitPrice: 0,
        },
        {
          id: 'onions',
          title: 'Onions',
          min: 0,
          max: 1,
          initialQuantity: 1,
          extraUnitPrice: 0,
        },
        {
          id: 'avocado',
          title: 'Avocado',
          min: 0,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 1.5,
        },
        {
          id: 'sprouts',
          title: 'Alfalfa Sprouts',
          min: 0,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0.5,
        },
      ],
    },
    {
      id: 'cheese',
      title: 'Add cheese',
      min: 0,
      max: 1,
      options: [
        {
          id: 'vegan-cheese',
          title: 'Vegan Cheese',
          min: 0,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 1.5,
        },
        {
          id: 'regular-cheese',
          title: 'Regular Cheese',
          min: 0,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 1.0,
        },
      ],
    },
    {
      id: 'sauces',
      title: 'Choose your sauces',
      min: 0,
      max: null,
      options: [
        {
          id: 'vegan-mayo',
          title: 'Vegan Mayo',
          min: 0,
          max: 1,
          initialQuantity: 1,
          extraUnitPrice: 0,
        },
        {
          id: 'chipotle',
          title: 'Chipotle Sauce',
          min: 0,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
        {
          id: 'mustard',
          title: 'Mustard',
          min: 0,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
      ],
    },
  ],
};

const frenchFries: Product = {
  id: 'french-fries',
  images: [
    'https://storage.googleapis.com/leviee_public/DE10555SBA-TEST/menu/gallery/01J9DYBB0CHWYW8PB6S18SZK0K.png',
  ],
  title: 'French Fries',
  price: 3.5,
  description: 'Crispy golden fries with a touch of salt',
  tags: [],
  filters: [filters.sides],
  configuration: [
    {
      id: 'size',
      title: 'Choose your size',
      min: 1,
      max: 1,
      options: [
        {
          id: 'regular',
          title: 'Regular',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
        {
          id: 'large',
          title: 'Large',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 1.5,
        },
      ],
    },
    {
      id: 'seasoning',
      title: 'Add seasoning',
      min: 0,
      max: null,
      options: [
        {
          id: 'salt',
          title: 'Salt',
          min: 0,
          max: 1,
          initialQuantity: 1,
          extraUnitPrice: 0,
        },
        {
          id: 'pepper',
          title: 'Black Pepper',
          min: 0,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
        {
          id: 'cajun',
          title: 'Cajun Seasoning',
          tag: 'Spicy',
          min: 0,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0.5,
        },
        {
          id: 'garlic',
          title: 'Garlic Parmesan',
          min: 0,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0.5,
        },
      ],
    },
    {
      id: 'dips',
      title: 'Add dips',
      min: 0,
      max: null,
      options: [
        {
          id: 'ketchup',
          title: 'Ketchup',
          min: 0,
          max: 2,
          initialQuantity: 1,
          extraUnitPrice: 0,
        },
        {
          id: 'mayo',
          title: 'Mayonnaise',
          min: 0,
          max: 2,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
        {
          id: 'chipotle',
          title: 'Chipotle Mayo',
          tag: 'Spicy',
          min: 0,
          max: 2,
          initialQuantity: 0,
          extraUnitPrice: 0.5,
        },
      ],
    },
  ],
};

const onionRings: Product = {
  id: 'onion-rings',
  images: [
    'https://storage.googleapis.com/leviee_public/DE10555SBA-TEST/menu/gallery/01J9DYCB8MGH5VAMCT76JEQF53.png',
  ],
  title: 'Onion Rings',
  price: 4.0,
  description: 'Crispy battered onion rings',
  tags: ['Most ordered'],
  filters: [filters.sides],
  configuration: [
    {
      id: 'size',
      title: 'Choose your size',
      min: 1,
      max: 1,
      options: [
        {
          id: 'regular',
          title: 'Regular',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
        {
          id: 'large',
          title: 'Large',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 2.0,
        },
      ],
    },
    {
      id: 'dips',
      title: 'Add dips',
      min: 0,
      max: null,
      options: [
        {
          id: 'ranch',
          title: 'Ranch',
          min: 0,
          max: 2,
          initialQuantity: 1,
          extraUnitPrice: 0,
        },
        {
          id: 'honey-mustard',
          title: 'Honey Mustard',
          min: 0,
          max: 2,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
        {
          id: 'bbq',
          title: 'BBQ Sauce',
          min: 0,
          max: 2,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
      ],
    },
  ],
};

const cola: Product = {
  id: 'cola',
  images: [
    'https://storage.googleapis.com/leviee_public/DE10555SBA-TEST/menu/gallery/01J9DYD6KQ1SVSAV3NHA3Y2HN8.png',
  ],
  title: 'Cola',
  price: 2.0,
  tags: [],
  filters: [filters.drinks],
  configuration: [
    {
      id: 'size',
      title: 'Choose your size',
      min: 1,
      max: 1,
      options: [
        {
          id: 'regular',
          title: 'Regular (16 oz)',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
        {
          id: 'large',
          title: 'Large (22 oz)',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0.5,
        },
        {
          id: 'extra-large',
          title: 'Extra Large (32 oz)',
          tag: 'Best value',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 1.0,
        },
      ],
    },
    {
      id: 'ice',
      title: 'Ice preference',
      min: 1,
      max: 1,
      options: [
        {
          id: 'normal',
          title: 'Normal Ice',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
        {
          id: 'light',
          title: 'Light Ice',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
        {
          id: 'no-ice',
          title: 'No Ice',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
      ],
    },
  ],
};

const lemonade: Product = {
  id: 'lemonade',
  images: [
    'https://storage.googleapis.com/leviee_public/DE10555SBA-TEST/menu/gallery/01J9DXEW44PR64PF4J5SSPFBD4.png',
  ],
  title: 'Fresh Lemonade',
  price: 3.0,
  description: 'Freshly squeezed lemons with a hint of sweetness',
  tags: [],
  filters: [filters.drinks],
  configuration: [
    {
      id: 'size',
      title: 'Choose your size',
      min: 1,
      max: 1,
      options: [
        {
          id: 'regular',
          title: 'Regular (16 oz)',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
        {
          id: 'large',
          title: 'Large (22 oz)',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0.5,
        },
      ],
    },
    {
      id: 'sweetness',
      title: 'Sweetness level',
      min: 1,
      max: 1,
      options: [
        {
          id: 'normal',
          title: 'Normal Sweet',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
        {
          id: 'less-sweet',
          title: 'Less Sweet',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
        {
          id: 'sugar-free',
          title: 'Sugar Free',
          min: 1,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0,
        },
      ],
    },
    {
      id: 'extras',
      title: 'Add extras',
      min: 0,
      max: null,
      options: [
        {
          id: 'mint',
          title: 'Fresh Mint',
          min: 0,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 0.5,
        },
        {
          id: 'strawberry',
          title: 'Strawberry Puree',
          min: 0,
          max: 1,
          initialQuantity: 0,
          extraUnitPrice: 1.0,
        },
      ],
    },
  ],
};

export const products = [
  doubleBBQBurgerHalal,
  spicyChickenBurger,
  cubanSandwich,
  pepperoniPizza,
  cheeseBurger,
  veggieBurger,
  frenchFries,
  chickenWings,
  crispers,
  onionRings,
  cola,
  lemonade,
];
