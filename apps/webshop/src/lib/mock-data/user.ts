import { OrderAddress } from '../order/types';

export type User = {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  addresses: OrderAddress[];
};

export const user: User = {
  id: 'j-doe',
  email: 'johndo<PERSON>@example.com',
  name: '<PERSON>',
  avatar: 'https://i.pravatar.cc/150?img=1',
  addresses: [
    {
      formattedAddress: 'Rua do Almada, 123, 2700-123 Porto',
      additionalAddressInfo: '6ºC',
      apartment: '123',
      instructions: 'Please call me when you arrive, the doorbell is broken. Thanks!',
      coordinates: { lat: 41.151491, lng: -8.613789 },
    },
    {
      formattedAddress: '456 Main St, Anytown, USA',
      additionalAddressInfo: '2.1',
      apartment: 'c',
      instructions: 'Leave at the door',
      coordinates: { lat: 123, lng: 456 },
    },
  ],
};
