import { Restaurant } from '~/lib/mock-data/restaurants';
import { Cart, OrderConfiguration } from '~/lib/order/types';
import { restaurants } from './restaurants';

export type Order = OrderConfiguration &
  Cart & {
    id: string;
    createdAt: Date;
    totalPrice: number;
    billing: {
      name: string;
      phone: string;
      card: {
        last4: string;
      };
    };
    restaurant: Restaurant;
  } & (
    | { status: 'in-progress' }
    | { status: 'completed' | 'cancelled'; completedAt: Date }
  );

const BILLING = {
  name: '<PERSON>',
  phone: '+00 000 000 000',
  card: {
    last4: '2356',
  },
};

const ITEMS = [
  {
    id: '088f599c-5596-485a-aab0-73a94771eefb',
    productId: 'double-bbq-burger-halal',
    title: 'Double BBQ Burger Halal',
    image:
      'https://unsplash.com/photos/Ii002MVqbnA/download?ixid=M3wxMjA3fDB8MXxzZWFyY2h8MzF8fGJicSUyMGJ1cmdlcnxlbnwwfHx8fDE3NDI2NDMxNzh8MA&force=true&w=1920',
    promoUnitPrice: 6.9,
    quantity: 1,
    baseUnitPrice: 8.9,
    options: [
      {
        groupId: 'comes-with',
        id: 'cheese',
        title: 'Cheese',
        min: 0,
        max: 15,
        extraUnitPrice: 1.1,
        initialQuantity: 1,
        quantity: 1,
      },
      {
        groupId: 'comes-with',
        id: 'ketchup',
        title: 'Ketchup',
        min: 0,
        max: 1,
        extraUnitPrice: 0,
        initialQuantity: 1,
        quantity: 1,
      },
      {
        groupId: 'comes-with',
        id: 'mayo',
        title: 'Mayo',
        min: 0,
        max: 1,
        extraUnitPrice: 0,
        initialQuantity: 1,
        quantity: 1,
      },
      {
        groupId: 'comes-with',
        id: 'tomato',
        title: 'Tomato',
        min: 0,
        max: 2,
        extraUnitPrice: 1.2,
        initialQuantity: 1,
        quantity: 1,
      },
      {
        groupId: 'patty',
        id: 'beef',
        title: 'Beef',
        min: 1,
        max: 1,
        extraUnitPrice: 0,
        initialQuantity: 0,
        quantity: 1,
      },
    ],
  },
  {
    id: '920f74d2-e954-4c0e-9a3c-69b82babbad5',
    productId: 'cheese-burger',
    title: 'Classic Cheese Burger',
    image:
      'https://unsplash.com/photos/sc5sTPMrVfk/download?ixid=M3wxMjA3fDB8MXxhbGx8fHx8fHx8fHwxNzQxMzY1NDIzfA&force=true&w=640',
    quantity: 1,
    baseUnitPrice: 7.5,
    options: [
      {
        groupId: 'toppings',
        id: 'lettuce',
        title: 'Lettuce',
        min: 0,
        max: 1,
        extraUnitPrice: 0,
        initialQuantity: 1,
        quantity: 1,
      },
      {
        groupId: 'toppings',
        id: 'tomato',
        title: 'Tomato',
        min: 0,
        max: 1,
        extraUnitPrice: 0,
        initialQuantity: 1,
        quantity: 1,
      },
      {
        groupId: 'toppings',
        id: 'onions',
        title: 'Onions',
        min: 0,
        max: 1,
        extraUnitPrice: 0,
        initialQuantity: 1,
        quantity: 1,
      },
      {
        groupId: 'toppings',
        id: 'pickles',
        title: 'Pickles',
        min: 0,
        max: 1,
        extraUnitPrice: 0,
        initialQuantity: 1,
        quantity: 1,
      },
      {
        groupId: 'sauces',
        id: 'mayo',
        title: 'Mayonnaise',
        min: 0,
        max: 1,
        extraUnitPrice: 0,
        initialQuantity: 1,
        quantity: 1,
      },
      {
        groupId: 'sauces',
        id: 'ketchup',
        title: 'Ketchup',
        min: 0,
        max: 1,
        extraUnitPrice: 0,
        initialQuantity: 1,
        quantity: 1,
      },
      {
        groupId: 'patty-doneness',
        id: 'well-done',
        title: 'Well Done',
        min: 1,
        max: 1,
        extraUnitPrice: 0,
        initialQuantity: 0,
        quantity: 1,
      },
      {
        groupId: 'extras',
        id: 'extra-cheese',
        title: 'Extra Cheese',
        min: 0,
        max: 2,
        extraUnitPrice: 1,
        initialQuantity: 0,
        quantity: 1,
      },
      {
        groupId: 'extras',
        id: 'fried-egg',
        title: 'Fried Egg',
        min: 0,
        max: 1,
        extraUnitPrice: 1,
        initialQuantity: 0,
        quantity: 1,
      },
      {
        groupId: 'sauces',
        id: 'mustard',
        title: 'Mustard',
        min: 0,
        max: 1,
        extraUnitPrice: 0,
        initialQuantity: 0,
        quantity: 1,
      },
    ],
  },
  {
    id: '02c78094-4712-4fb5-9ce5-e456278292d0',
    productId: 'chicken-wings',
    title: 'Chicken Wings',
    image:
      'https://unsplash.com/photos/NbXjZomyNEM/download?ixid=M3wxMjA3fDB8MXxzZWFyY2h8OHx8ZmFzdCUyMGZvb2R8ZW58MHx8fHwxNzQxMzQ0NTEzfDI&force=true&w=640',
    quantity: 1,
    baseUnitPrice: 6.5,
    options: [
      {
        groupId: 'extras',
        id: 'ranch',
        title: 'Ranch Dip',
        min: 0,
        max: 3,
        extraUnitPrice: 0.5,
        initialQuantity: 1,
        quantity: 1,
      },
      {
        groupId: 'extras',
        id: 'celery-sticks',
        title: 'Celery Sticks',
        min: 0,
        max: 1,
        extraUnitPrice: 0,
        initialQuantity: 1,
        quantity: 1,
      },
      {
        groupId: 'extras',
        id: 'carrot-sticks',
        title: 'Carrot Sticks',
        min: 0,
        max: 1,
        extraUnitPrice: 0,
        initialQuantity: 1,
        quantity: 1,
      },
      {
        groupId: 'quantity',
        id: '6-pieces',
        title: '6 Pieces',
        min: 1,
        max: 1,
        extraUnitPrice: 0,
        initialQuantity: 0,
        quantity: 1,
      },
      {
        groupId: 'flavor',
        id: 'buffalo',
        title: 'Buffalo',
        min: 1,
        max: 1,
        extraUnitPrice: 0,
        initialQuantity: 0,
        quantity: 1,
      },
    ],
  },
  {
    id: '76572bec-e0b2-4c14-bcaf-e443df750564',
    productId: 'cola',
    title: 'Cola',
    image:
      'https://unsplash.com/photos/Qvnohn4GyJA/download?ixid=M3wxMjA3fDB8MXxhbGx8fHx8fHx8fHwxNzQxNjI1ODE0fA&force=true&w=1920',
    quantity: 1,
    baseUnitPrice: 2,
    options: [
      {
        groupId: 'size',
        id: 'regular',
        title: 'Regular (16 oz)',
        min: 1,
        max: 1,
        extraUnitPrice: 0,
        initialQuantity: 0,
        quantity: 1,
      },
      {
        groupId: 'ice',
        id: 'normal',
        title: 'Normal Ice',
        min: 1,
        max: 1,
        extraUnitPrice: 0,
        initialQuantity: 0,
        quantity: 1,
      },
    ],
  },
  {
    id: '613acd13-4a69-45cf-81b3-adf577c4c597',
    productId: 'lemonade',
    title: 'Fresh Lemonade',
    image: 'https://unsplash.com/photos/p5EiqkBYIEE/download?force=true&w=1920',
    quantity: 1,
    baseUnitPrice: 3,
    options: [
      {
        groupId: 'size',
        id: 'large',
        title: 'Large (22 oz)',
        min: 1,
        max: 1,
        extraUnitPrice: 0.5,
        initialQuantity: 0,
        quantity: 1,
      },
      {
        groupId: 'sweetness',
        id: 'less-sweet',
        title: 'Less Sweet',
        min: 1,
        max: 1,
        extraUnitPrice: 0,
        initialQuantity: 0,
        quantity: 1,
      },
    ],
  },
  {
    id: 'ef6ed6e4-d81c-4c25-9f00-85605ea9aec0',
    productId: 'french-fries',
    title: 'French Fries',
    image:
      'https://unsplash.com/photos/vi0kZuoe0-8/download?ixid=M3wxMjA3fDB8MXxhbGx8fHx8fHx8fHwxNzQxNjIzMTU3fA&force=true&w=1920',
    quantity: 1,
    baseUnitPrice: 3.5,
    options: [
      {
        groupId: 'seasoning',
        id: 'salt',
        title: 'Salt',
        min: 0,
        max: 1,
        extraUnitPrice: 0,
        initialQuantity: 1,
        quantity: 1,
      },
      {
        groupId: 'dips',
        id: 'ketchup',
        title: 'Ketchup',
        min: 0,
        max: 2,
        extraUnitPrice: 0,
        initialQuantity: 1,
        quantity: 1,
      },
      {
        groupId: 'size',
        id: 'large',
        title: 'Large',
        min: 1,
        max: 1,
        extraUnitPrice: 1.5,
        initialQuantity: 0,
        quantity: 1,
      },
    ],
  },
];

export const orders: Order[] = [
  {
    id: '23123',
    status: 'in-progress',
    createdAt: new Date('2021-01-01'),
    type: 'pickup',
    time: { type: 'standard' },
    discount: null,
    items: ITEMS,
    billing: BILLING,
    totalPrice: 10.4,
    restaurant: restaurants['king-loui'],
  },
  {
    id: '6863f1fa731217533d2a5027',
    status: 'in-progress',
    createdAt: new Date('2021-01-01'),
    type: 'delivery',
    time: { type: 'standard' },
    address: {
      formattedAddress: 'Rua da Torrinha 254, Porto',
      additionalAddressInfo: 'Apt 1',
      instructions: 'Leave at the front door',
      coordinates: {
        lat: 41.151491,
        lng: -8.614553,
      },
    },
    discount: null,
    items: ITEMS,
    billing: BILLING,
    totalPrice: 4.99,
    restaurant: restaurants['king-loui'],
  },
  {
    id: '8312',
    status: 'completed',
    createdAt: new Date('2021-01-01'),
    completedAt: new Date(Date.now()),
    type: 'delivery',
    time: { type: 'standard' },
    address: {
      formattedAddress: 'Rua do Breiner 254, Porto',
      additionalAddressInfo: 'Apt 1',
      instructions: 'Leave at the front door',
      coordinates: {
        lat: 37.774929,
        lng: -122.419416,
      },
    },
    discount: null,
    items: ITEMS,
    billing: BILLING,
    totalPrice: 23.2,
    restaurant: restaurants['king-loui-breiner'],
  },
  {
    id: '412',
    status: 'cancelled',
    createdAt: new Date('2021-01-01'),
    completedAt: new Date('2021-01-01:10:00'),
    type: 'pickup',
    time: { type: 'standard' },
    discount: null,
    items: ITEMS,
    billing: BILLING,
    totalPrice: 53.2,
    restaurant: restaurants['king-loui-ribeira'],
  },
];
