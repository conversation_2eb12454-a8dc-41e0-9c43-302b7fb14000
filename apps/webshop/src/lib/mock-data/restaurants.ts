import { GeoCoordinates } from '~/lib/types';

export type MultiBrand = {
  type: 'multi-brand';
  slug: string;
  name: string;
  description: string;
  tags: string[];
  hero: string;
  brands: Brand[];
};

export type Brand = {
  type: 'brand';
  slug: string;
  name: string;
  title: string;
  description: string;
  tags: string[];
  hero: string;
  menuImages: string[];
  restaurants: Restaurant[];
};

export interface Restaurant {
  type: 'restaurant';
  slug: string;
  name: string;
  images: [string, string?, string?];
  address: {
    formattedAddress: string;
    coordinates: GeoCoordinates;
  };
  contact: {
    phone: string;
    email: string;
  };
  openingHours: Record<
    'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday',
    [string, string][]
  >;
  deliveryRadiusKm: number;
  timezone: string;
  hasDelivery: boolean;
  hasPickup: boolean;
  isOpen: boolean;
  openStatus: string;
  tags: string[];
  brand: {
    slug: string;
    name: string;
  } | null;
  multiBrand: {
    slug: string;
    name: string;
  } | null;
}

const kingLoui: Restaurant = {
  type: 'restaurant',
  slug: 'king-loui',
  name: 'King <PERSON><PERSON>',
  images: [
    'https://storage.googleapis.com/leviee_public/DE10555SBA-TEST/menu/gallery/01J9DY1EV0K6TH8W8M59586JMV.png',
  ],
  timezone: 'Europe/Lisbon',
  tags: ['Hotpot', 'Asian Food', 'Average Price €60'],
  contact: {
    phone: '+49 893 791 855 91',
    email: '<EMAIL>',
  },
  address: {
    formattedAddress: 'Rua do Breiner 72, 4050-172 Porto',
    coordinates: {
      lat: 41.15267,
      lng: -8.620182,
    },
  },
  hasDelivery: true,
  deliveryRadiusKm: 10,
  isOpen: true,
  openStatus: 'Open until 10:00 PM',
  openingHours: {
    monday: [['09:00', '11:00']],
    tuesday: [['09:00', '11:00']],
    wednesday: [['09:00', '12:00']],
    thursday: [['09:00', '12:00']],
    friday: [
      ['09:00', '11:00'],
      ['13:00', '20:00'],
    ],
    saturday: [
      ['09:00', '11:00'],
      ['13:00', '20:00'],
    ],
    sunday: [
      ['09:00', '11:00'],
      ['13:00', '20:00'],
    ],
  },
  hasPickup: true,
  brand: {
    slug: 'chicken-house',
    name: 'Chicken House',
  },
  multiBrand: {
    slug: 'house-of-food',
    name: 'House of Food',
  },
};

const kingLouiBreiner: Restaurant = {
  ...kingLoui,
  images: [
    'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    'https://images.unsplash.com/photo-1537047902294-62a40c20a6ae?q=80&w=1935&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    'https://images.unsplash.com/photo-1540420773420-3366772f4999?q=80&w=1884&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  ],
  slug: 'king-loui-breiner',
  name: 'King Loui Breiner',
  address: {
    formattedAddress: 'Rua do Breiner 72, 4050-172 Porto',
    coordinates: {
      lat: 41.154006,
      lng: -8.625491,
    },
  },
  isOpen: false,
  openStatus: 'Opens at 10:00 AM',
  hasDelivery: false,
};

const kingLouiRibeira: Restaurant = {
  ...kingLoui,
  slug: 'king-loui-ribeira',
  name: 'King Loui Downtown',
  images: [
    'https://images.unsplash.com/photo-1577958194277-7b3bc213b03c?q=80&w=1930&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  ],
  openStatus: 'Closes soon',
  address: {
    formattedAddress: 'Rua dos Mercadores 12, Porto',
    coordinates: {
      lat: 41.159882,
      lng: -8.624708,
    },
  },
  hasPickup: false,
};

const kingLouiVegan: Restaurant = {
  ...kingLoui,
  slug: 'king-loui-vegan',
  name: 'King Loui Vegan',
  images: [
    'https://images.unsplash.com/photo-1540914124281-342587941389?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  ],
  openStatus: 'Open until 09:00 PM',
  address: {
    formattedAddress: 'Travessa de Cedofeita 72, 4050-172 Porto',
    coordinates: {
      lat: 41.158449,
      lng: -8.619862,
    },
  },
};

export const restaurants = {
  'king-loui': kingLoui,
  'king-loui-breiner': kingLouiBreiner,
  'king-loui-ribeira': kingLouiRibeira,
  'king-loui-vegan': kingLouiVegan,
};

export const brand: Brand = {
  type: 'brand',
  name: 'Chicken house',
  title: 'Welcome to Chicken House',
  slug: 'chicken-house',
  description:
    'Combine a variety of ingredients to create your own individual and incomparable taste experience. Various meat dishes, seafood, tofu and vegetable dishes are freshly prepared for you and served at your table. Cook the ingredients in the broth of your choice - the Chinese way to eat fresh, healthy and delicious.',
  tags: ['Hotpot', 'Asian Food', 'Vegetarian', 'Chicken'],
  hero: 'https://images.unsplash.com/photo-**********-d89a9ad46330?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  menuImages: [
    'https://unsplash.com/photos/Ii002MVqbnA/download?ixid=**************************************************************************&force=true&w=1920',
    'https://unsplash.com/photos/pYQSM-p_0_c/download?ixid=**********************************************&force=true&w=640',
    'https://unsplash.com/photos/5nvt9BrLaAc/download?ixid=**********************************************&force=true&w=1920',
  ],
  restaurants: [kingLoui, kingLouiBreiner, kingLouiRibeira, kingLouiVegan],
};

export const multiBrand: MultiBrand = {
  type: 'multi-brand',
  slug: 'house-of-food',
  name: 'House of Food',
  description:
    'House of Food is a company that serves food. There is something for everyone.',
  hero: 'https://images.unsplash.com/photo-**********-d89a9ad46330?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  tags: ['food', 'fast food'],
  brands: [brand],
};

const allData: Record<string, Restaurant | Brand | MultiBrand> = {
  'house-of-food': multiBrand,
  'chicken-house': brand,
  ...restaurants,
};

export const getData = (slug: string) => {
  if (!allData[slug]) {
    throw new Error(`Restaurant with slug ${slug} not found`);
  }

  return allData[slug];
};
