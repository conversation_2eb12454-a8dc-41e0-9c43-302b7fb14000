import { format, isToday, isTomorrow } from 'date-fns';
import { toZonedTime } from 'date-fns-tz';

export const getLocalizedHour = (date: Date, locale: string) => {
  const shortTime = new Intl.DateTimeFormat(locale, {
    timeStyle: 'short',
  });

  return shortTime.format(date);
};

export const formatScheduledTime = (date: Date) => {
  if (isToday(date)) {
    return `Today, ${format(date, 'HH:mm')}`;
  }

  if (isTomorrow(date)) {
    return `Tomorrow, ${format(date, 'HH:mm')}`;
  }

  return format(date, 'cccc, HH:mm');
};

// Helper function to format time to HH:MM format
export const formatTime = (timeString: string | undefined): string => {
  if (!timeString) return 'Closed';

  // If it contains seconds (HH:MM:SS format)
  if (timeString.split(':').length === 3) {
    // Extract hours and minutes only
    const [hours, minutes] = timeString.split(':');
    return `${hours}:${minutes}`;
  }

  return timeString; // Already in HH:MM format
};

/**
 * Checks if a restaurant is currently open based on its opening hours
 * @param openingHours The restaurant's opening hours
 * @param timezone The restaurant's timezone (defaults to 'Europe/Berlin')
 * @returns boolean indicating if the restaurant is currently open
 */
export const isRestaurantOpen = (
  openingHours: Record<string, { opens?: string; closes?: string }[]> | undefined,
  timezone: string = 'Europe/Berlin'
): boolean => {
  if (!openingHours) return false;

  // Get current date/time in the restaurant's timezone
  const now = toZonedTime(new Date(), timezone);

  // Get day of week in lowercase (monday, tuesday, etc.)
  const currentDay = format(now, 'EEEE').toLowerCase();

  // Get current time in 24-hour format (HH:MM)
  const currentTime = format(now, 'HH:mm');

  // Check if the restaurant has opening hours for the current day
  const todayHours = openingHours[currentDay.toUpperCase()];
  if (!todayHours || !Array.isArray(todayHours) || todayHours.length === 0) {
    return false; // Closed if no hours defined for today
  }

  // Check if current time falls within any of the opening periods
  return todayHours.some((period) => {
    if (!period.opens || !period.closes) return false;

    // Extract HH:MM from time strings if they contain seconds
    const openTime = formatTime(period.opens);
    const closeTime = formatTime(period.closes);

    // Compare current time with opening and closing times
    return currentTime >= openTime && currentTime < closeTime;
  });
};

/**
 * Gets a human-readable status of a restaurant's opening hours
 * @param openingHours The restaurant's opening hours
 * @param timezone The restaurant's timezone (defaults to 'Europe/Berlin')
 * @returns String indicating if the restaurant is open or closed, with closing time if open
 */
export const getRestaurantOpenStatus = (
  openingHours: Record<string, { opens?: string; closes?: string }[]> | undefined,
  timezone: string = 'Europe/Berlin'
): string => {
  if (!openingHours) return 'Closed';

  // Get current date/time in the restaurant's timezone
  const now = toZonedTime(new Date(), timezone);

  // Get day of week in lowercase (monday, tuesday, etc.)
  const currentDay = format(now, 'EEEE').toLowerCase();

  // Get current time in 24-hour format (HH:MM)
  const currentTime = format(now, 'HH:mm');

  // Check if the restaurant has opening hours for the current day
  const todayHours = openingHours[currentDay.toUpperCase()];
  if (!todayHours || !Array.isArray(todayHours) || todayHours.length === 0) {
    return 'Closed'; // Closed if no hours defined for today
  }

  // Find the current open period, if any
  for (const period of todayHours) {
    if (!period.opens || !period.closes) continue;

    // Extract HH:MM from time strings if they contain seconds
    const openTime = formatTime(period.opens);
    const closeTime = formatTime(period.closes);

    // Check if current time falls within this period
    if (currentTime >= openTime && currentTime < closeTime) {
      return `Open until ${closeTime}`;
    }
  }

  // If we get here, the restaurant is currently closed
  return 'Closed';
};
