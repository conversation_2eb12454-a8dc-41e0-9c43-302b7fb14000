import { env } from 'next-runtime-env';

export const baseUrl = `${env('NEXT_PUBLIC_BASE_URL') || process.env.NEXT_PUBLIC_BASE_URL || ''}`;
export const cdnUrl =
  env('NEXT_PUBLIC_ALLO_CDN_URL') || process.env.NEXT_PUBLIC_ALLO_CDN_URL || '';

export const cdnScriptUrl = `${cdnUrl}/twicpics.js`;
export const mediaBaseUrl = `${cdnUrl}/c/p/twicpics/`;

export const transformMediaUrl = (url) => {
  if (!url) {
    return null;
  }
  return `${mediaBaseUrl}${url?.replace(
    'https://storage.googleapis.com/leviee_public/',
    ''
  )}`;
};
