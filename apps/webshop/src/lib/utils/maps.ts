import { GeoCoordinates } from '~/lib/types';

export const calculateContainerBounds = (coordinates: GeoCoordinates[]) => {
  let north = coordinates[0]?.lat || 0;
  let south = coordinates[0]?.lat || 0;
  let east = coordinates[0]?.lng || 0;
  let west = coordinates[0]?.lng || 0;

  coordinates.forEach((coord) => {
    north = Math.max(north, coord.lat);
    south = Math.min(south, coord.lat);
    east = Math.max(east, coord.lng);
    west = Math.min(west, coord.lng);
  });

  return { east, west, north, south };
};

export const getGoogleMapsPinUrl = (coordinates: GeoCoordinates) => {
  return `https://www.google.com/maps/search/?api=1&query=${coordinates.lat},${coordinates.lng}`;
};
