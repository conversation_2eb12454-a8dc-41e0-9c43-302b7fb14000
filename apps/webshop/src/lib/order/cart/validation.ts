/**
 * Validation
 *
 * Error handling should be done on a group-by-group basis.
 * - No errors are shown to use the user until they click "add to cart" – that triggers the first validation.
 * - Validation exits after the first group error is found and we render it and scroll to it.
 * - From then on, that group validation is "on" and is validated on every change.
 *
 * Repeat until there are no errors.
 * */

import { WebshopMenuItem } from '~/lib/api/types.ts';
import { ProductConfiguration } from '~/lib/mock-data/products';
import { CartItem } from '../types';

export type CartItemGroupError = {
  code:
    | 'TOO_FEW_OPTIONS_SELECTED_IN_GROUP_ERROR'
    | 'TOO_MANY_OPTIONS_SELECTED_IN_GROUP_ERROR';
  message: string;
  groupId: string;
};

export const getCartItemFirstGroupError = (item: CartItem, product: WebshopMenuItem) => {
  // TODO: Error for getCartItemFirstGroupError
  // for (const group of product.configuration) {
  //   const error = getCartItemGroupError(item, group);
  //
  //   if (error) return error;
  // }
  return false;
};

// returns only one error at a time
export const getCartItemGroupError = (
  item: CartItem,
  group: ProductConfiguration
): CartItemGroupError | undefined => {
  const selectedGroupOptions = item.options.filter((option) =>
    group.options.some((groupOption) => groupOption.id === option.id)
  );
  const selectedGroupOptionsCount = selectedGroupOptions.reduce(
    (total, option) => total + option.quantity,
    0
  );

  // Check if the group is required and the user has selected the minimum amount of options
  if (group.min > 0) {
    if (selectedGroupOptionsCount < group.min) {
      return {
        code: 'TOO_FEW_OPTIONS_SELECTED_IN_GROUP_ERROR',
        message: `Select at least ${group.min}`,
        groupId: group.id,
      };
    }
  }

  // Check if the user has selected more options than allowed
  if (group.max && selectedGroupOptionsCount > group.max) {
    return {
      code: 'TOO_MANY_OPTIONS_SELECTED_IN_GROUP_ERROR',
      message: `Select up to ${group.max}`,
      groupId: group.id,
    };
  }
};
