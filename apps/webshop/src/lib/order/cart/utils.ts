import { useMemo } from 'react';
import {
  WebshopMenuItem,
  WebShopMenuItemExtraItem,
  WebShopMenuItemOptionItem,
  WebShopOrderCartAddItemRequest,
  WebShopOrderCartItem,
} from '~/lib/api/types.ts';
import { ProductOption } from '~/lib/mock-data/products';
import { CartItem, CartItemOption } from '~/lib/order/types';

export const productsToCartItems = (products: WebShopOrderCartItem[]): CartItem[] => {
  return products.map((product) => singleProductToCartItem(product));
};

export const singleProductToCartItem = (product: WebShopOrderCartItem): CartItem => {
  const options = product.options?.reduce<CartItemOption[]>((options, group) => {
    const selectedOptions =
      (group.items && group.items.filter((option) => (option?.qtd || 0) > 0)) || [];

    return [
      ...options,
      ...selectedOptions.map((option) =>
        webShopMenuItemOptionItemToCartItemOption(group.id || '', option, option.qtd || 0)
      ),
    ];
  }, []);
  const extras = product.extras?.reduce<CartItemOption[]>((extras, group) => {
    const selectedExtras =
      (group.items && group.items.filter((extra) => (extra?.qtd || 0) > 0)) || [];

    return [
      ...extras,
      ...selectedExtras.map((extra) =>
        webShopMenuItemExtraItemToCartItemOption(group.id || '', extra, extra.qtd || 0)
      ),
    ];
  }, []);
  return {
    id: product.id || '',
    productId: product.code || '',
    title: product.name || '',
    image: product.thumbnailUrl,
    // TODO: When we have promo price
    //promoUnitPrice: product.promoPrice,
    quantity: product.qtd || 0,
    baseUnitPrice: product.unitPrice || 0,
    options: options || [],
    extras: extras || [],
  };
};

export const cartItemToWebShopOrderCartAddItemRequest = (
  product: CartItem
): WebShopOrderCartAddItemRequest => {
  return {
    id: product.id,
    code: product.productId,
    qtd: product.quantity,
    selectedOptions: product.options.reduce((acc, option) => {
      acc[option.groupId] = {
        [option.id]: {
          id: option.id,
          qtd: option.quantity,
        },
      };
      return acc;
    }, {} as any),
    selectedExtras: product.extras.reduce((acc, extra) => {
      acc[extra.groupId] = {
        [extra.id]: {
          id: extra.id,
          qtd: extra.quantity,
        },
      };
      return acc;
    }, {} as any),
  };
};

export const webShopMenuItemOptionItemToCartItemOption = (
  optionGroupId: string,
  productOption: WebShopMenuItemOptionItem,
  quantity: number
): CartItemOption => {
  return {
    groupId: optionGroupId,
    id: productOption.id || '',
    title: productOption.name || '',
    min: productOption.min || 0,
    max: productOption.max || 0,
    extraUnitPrice: productOption.unitPrice || 0,
    initialQuantity: productOption.qtd || 0,
    quantity,
  };
};

export const webShopMenuItemExtraItemToCartItemOption = (
  optionGroupId: string,
  productOption: WebShopMenuItemExtraItem,
  quantity: number
): CartItemOption => {
  return {
    groupId: optionGroupId,
    id: productOption.id || '',
    title: productOption.name || '',
    min: 0,
    max: productOption.max || 0,
    extraUnitPrice: productOption.unitPrice || 0,
    initialQuantity: productOption.qtd || 0,
    quantity,
  };
};

export const productOptionToCartItemOption = (
  optionGroupId: string,
  productOption: ProductOption,
  quantity: number
): CartItemOption => {
  return {
    groupId: optionGroupId,
    id: productOption.id,
    title: productOption.title,
    min: productOption.min,
    max: productOption.max,
    extraUnitPrice: productOption.extraUnitPrice,
    initialQuantity: productOption.initialQuantity,
    quantity,
  };
};

export const productToCartItem = (product: WebshopMenuItem): CartItem => {
  return {
    id: product.id || '',
    productId: product.code || '',
    title: product.name || '',
    image: product.thumbnailUrl,
    // TODO: When we have promo price
    //promoUnitPrice: product.promoPrice,
    quantity: 1,
    baseUnitPrice: product.unitPrice || 0,
    options: [],
    extras: [],
    // options: product.configuration.reduce<CartItemOption[]>((options, group) => {
    //   const selectedOptions = group.options.filter(
    //     (option) => option.initialQuantity > 0
    //   );
    //
    //   return [
    //     ...options,
    //     ...selectedOptions.map((option) =>
    //       productOptionToCartItemOption(group.id, option, option.initialQuantity)
    //     ),
    //   ];
    // }, []),
  };
};

export const calculateProductQuantityInCart = (
  productId: string,
  items: CartItem[]
): number => {
  return items.reduce((acc, item) => {
    if (item.productId !== productId) return acc;
    return acc + item.quantity;
  }, 0);
};

const generateUniqueCartItemKey = (item: CartItem): string => {
  const base = item.productId;
  const options = item.options.map((o) => `${o.groupId}-${o.id}`).join('-');

  return `${base}-${options}`;
};

export const mergeCartItems = (items: CartItem[]): CartItem[] => {
  const mergedItems = new Map<string, CartItem>();

  for (const item of items) {
    const key = generateUniqueCartItemKey(item);
    const existingItem = mergedItems.get(key);

    if (existingItem) {
      mergedItems.set(key, {
        ...existingItem,
        id: item.id, // overwrite the old item id with the new one
        quantity: existingItem.quantity + item.quantity,
      });
    } else {
      mergedItems.set(key, item);
    }
  }

  return Array.from(mergedItems.values());
};

export const calculateCartItemOptionTotalPrice = (option: {
  initialQuantity: number;
  extraUnitPrice: number;
  quantity: number;
}): number => {
  return Math.max(0, option.quantity - option.initialQuantity) * option.extraUnitPrice;
};

export const calculateCartItemTotalPrice = (cartItem: CartItem): number => {
  const optionsPrice = cartItem.options.reduce(
    (total, option) => total + calculateCartItemOptionTotalPrice(option),
    0
  );

  const baseUnitPrice = cartItem.promoUnitPrice ?? cartItem.baseUnitPrice;
  return (baseUnitPrice + optionsPrice) * cartItem.quantity;
};

export const useGetOrCreateCart = () => {
  return useMemo(() => {}, []);
};
