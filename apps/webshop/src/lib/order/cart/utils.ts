import { Product, ProductOption } from '~/lib/mock-data/products';
import { CartItem, CartItemOption } from '~/lib/order/types';
import { uuid } from '~/lib/utils/strings';
import { WebshopMenuItem, WebShopMenuItemExtraItem, WebShopMenuItemOptionItem } from '~/lib/api/types.ts';
import { useMemo } from 'react';



export const webShopMenuItemOptionItemToCartItemOption = (
  optionGroupId: string,
  productOption: WebShopMenuItemOptionItem,
  quantity: number
): CartItemOption => {
  return {
    groupId: optionGroupId,
    id: productOption.id || '',
    title: productOption.name || '',
    min: productOption.min || 0,
    max: productOption.max || 0,
    extraUnitPrice: productOption.unitPrice || 0,
    initialQuantity: productOption.qtd || 0,
    quantity,
  };
};

export const webShopMenuItemExtraItemToCartItemOption = (
  optionGroupId: string,
  productOption: WebShopMenuItemExtraItem,
  quantity: number
): CartItemOption => {
  return {
    groupId: optionGroupId,
    id: productOption.id || '',
    title: productOption.name || '',
    min: 0,
    max: productOption.max || 0,
    extraUnitPrice: productOption.unitPrice || 0,
    initialQuantity: productOption.qtd || 0,
    quantity,
  };
};

export const productOptionToCartItemOption = (
  optionGroupId: string,
  productOption: ProductOption,
  quantity: number
): CartItemOption => {
  return {
    groupId: optionGroupId,
    id: productOption.id,
    title: productOption.title,
    min: productOption.min,
    max: productOption.max,
    extraUnitPrice: productOption.extraUnitPrice,
    initialQuantity: productOption.initialQuantity,
    quantity,
  };
};

export const productToCartItem = (product: WebshopMenuItem): CartItem => {
  return {
    id: uuid(),
    productId: product.id || '',
    title: product.name || '',
    image: product.thumbnailUrl,
    // TODO: When we have promo price
    //promoUnitPrice: product.promoPrice,
    quantity: 1,
    baseUnitPrice: product.unitPrice || 0,
    options: [],
    extras: [],
    // options: product.configuration.reduce<CartItemOption[]>((options, group) => {
    //   const selectedOptions = group.options.filter(
    //     (option) => option.initialQuantity > 0
    //   );
    //
    //   return [
    //     ...options,
    //     ...selectedOptions.map((option) =>
    //       productOptionToCartItemOption(group.id, option, option.initialQuantity)
    //     ),
    //   ];
    // }, []),
  };
};

export const calculateProductQuantityInCart = (
  productId: string,
  items: CartItem[]
): number => {
  return items.reduce((acc, item) => {
    if (item.productId !== productId) return acc;
    return acc + item.quantity;
  }, 0);
};

const generateUniqueCartItemKey = (item: CartItem): string => {
  const base = item.productId;
  const options = item.options.map((o) => `${o.groupId}-${o.id}`).join('-');

  return `${base}-${options}`;
};

export const mergeCartItems = (items: CartItem[]): CartItem[] => {
  const mergedItems = new Map<string, CartItem>();

  for (const item of items) {
    const key = generateUniqueCartItemKey(item);
    const existingItem = mergedItems.get(key);

    if (existingItem) {
      mergedItems.set(key, {
        ...existingItem,
        id: item.id, // overwrite the old item id with the new one
        quantity: existingItem.quantity + item.quantity,
      });
    } else {
      mergedItems.set(key, item);
    }
  }

  return Array.from(mergedItems.values());
};

export const calculateCartItemOptionTotalPrice = (option: {
  initialQuantity: number;
  extraUnitPrice: number;
  quantity: number;
}): number => {
  return Math.max(0, option.quantity - option.initialQuantity) * option.extraUnitPrice;
};

export const calculateCartItemTotalPrice = (cartItem: CartItem): number => {
  const optionsPrice = cartItem.options.reduce(
    (total, option) => total + calculateCartItemOptionTotalPrice(option),
    0
  );

  const baseUnitPrice = cartItem.promoUnitPrice ?? cartItem.baseUnitPrice;
  return (baseUnitPrice + optionsPrice) * cartItem.quantity;
};

export const useGetOrCreateCart = () => {
  return useMemo(() => {

  }, []);
}
