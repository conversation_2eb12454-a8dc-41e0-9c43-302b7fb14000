import { useEffect, useState } from 'react';
import { getCart, postCreateCart } from '~/lib/api';
import { WebShopError, WebShopOrderCart } from '~/lib/api/types.ts';

export type GetOrCreateCartResponse = {
  hasError: boolean;
  isLoading: boolean;
  withResetStore: boolean;
  error?: WebShopError;
  cart?: WebShopOrderCart;
};

export type GetOrCreateCartOptions = {
  restaurantId: string;
  cartId?: string;
};

export type FetchCartResponse = {
  hasError: boolean;
  error?: WebShopError;
  cart?: WebShopOrderCart;
};

export const useGetOrCreateCart = ({
  restaurantId,
  cartId = '',
}: GetOrCreateCartOptions): GetOrCreateCartResponse => {
  const [state, setState] = useState<GetOrCreateCartResponse>({
    hasError: false,
    isLoading: true,
    withResetStore: true,
  });
  /**
   * Fetch an existing orderCart
   */
  const fetchCart = async (): Promise<FetchCartResponse> => {
    const cart = await getCart(restaurantId, cartId);
    if (cart.error) {
      return {
        hasError: true,
      };
    }
    const data = cart.data;
    return {
      hasError: false,
      cart: data,
    };
  };
  const createNewOrderCart = async (): Promise<any> => {
    return await postCreateCart(restaurantId, {
      orderType: 'PICKUP',
      customer: {},
    });
  };

  useEffect(() => {
    const cart = fetchCart();
    cart.then((res) => {
      if (res.hasError || res.cart?.status === 'COMPLETED') {
        createNewOrderCart()
          .then((cartData) => {
            const d = cartData.data;
            if (d.error) {
              setState({
                hasError: true,
                error: d.error,
                isLoading: false,
                withResetStore: true,
              });
            } else {
              setState({
                hasError: false,
                cart: d,
                isLoading: false,
                withResetStore: true,
              });
            }
          })
          .catch((err) => {
            setState({
              hasError: true,
              error: err,
              isLoading: false,
              withResetStore: true,
            });
          });
      } else {
        // TODO: return the cart
        setState({
          hasError: false,
          cart: res.cart,
          isLoading: false,
          withResetStore: true,
        });
      }
    });
  }, []);

  return state;
};
