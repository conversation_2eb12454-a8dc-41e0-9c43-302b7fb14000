import { OrderStore } from './store';
import {
  OrderAddress,
  OrderBilling,
  OrderConfiguration,
  OrderTime,
  OrderType,
} from './types';

export const isValidOrderAddress = (
  address: Partial<OrderAddress>
): address is OrderAddress => {
  if (!address) return false;

  return (
    !!address.coordinates &&
    !!address.formattedAddress?.length &&
    !!address.additionalAddressInfo?.length
  );
};

interface UnknownOrderConfiguration {
  type: OrderType;
  time: OrderTime | null;
  address?: OrderAddress | null;
}

export const isValidOrderConfiguration = (
  configuration: UnknownOrderConfiguration
): configuration is OrderConfiguration => {
  const { type, time, address } = configuration;

  if (type === 'PICKUP' && !!time) return true;
  if (type === 'DELIVERY' && !!address && isValidOrderAddress(address)) return true;

  return false;
};

export const areSameOrderAddress = (a: OrderAddress, b: OrderAddress) => {
  return (
    a.formattedAddress === b.formattedAddress &&
    a.additionalAddressInfo === b.additionalAddressInfo &&
    a.apartment === b.apartment &&
    a.instructions === b.instructions
  );
};

export const isValidOrderBilling = (
  billing: Partial<OrderBilling>
): billing is OrderBilling => {
  return (
    !!billing.firstName &&
    !!billing.lastName &&
    !!billing.phone &&
    !!billing.paymentMethod
  );
};

export const isValidOrder = (order: OrderStore) => {
  return (
    (order.items.length > 0 &&
      isValidOrderConfiguration(order) &&
      order.billing &&
      isValidOrderBilling(order.billing)) ||
    false
  );
};
