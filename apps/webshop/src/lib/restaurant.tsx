'use client';

import { createContext, use, useMemo } from 'react';
import { WebshopRestaurant } from '~/lib/api/types.ts';

type RestaurantContextType = Pick<
  WebshopRestaurant,
  | 'id'
  | 'name'
  | 'address'
  //| 'deliveryRadiusKm'
  //| 'isOpen'
  //| 'brand'
  | 'hasDelivery'
  | 'hasPickup'
  | 'openingHours'
  | 'branchRestaurants'
> & {
  slug: string;
};

export const RestaurantContext = createContext<RestaurantContextType | null>(null);

export const useRestaurant = () => {
  const context = use(RestaurantContext);

  if (!context) {
    throw new Error('useRestaurant must be used within a RestaurantContextProvider.');
  }

  return context;
};

interface RestaurantContextProviderProps {
  value: RestaurantContextType;
  children: React.ReactNode;
}

export const RestaurantContextProvider = ({
  value,
  children,
}: RestaurantContextProviderProps) => {
  const ctx = useMemo(() => value, [value]);
  return <RestaurantContext value={ctx}>{children}</RestaurantContext>;
};
