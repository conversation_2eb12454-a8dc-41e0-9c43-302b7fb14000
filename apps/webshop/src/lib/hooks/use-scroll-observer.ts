import { useEffect } from 'react';
import { useStableCallback } from '~/lib/hooks/use-stable-callback';

export function useScrollObserver(
  ref: React.RefObject<HTMLElement | null>,
  callback: (el: HTMLElement) => void
) {
  const stableCallback = useStableCallback(callback);

  useEffect(() => {
    const el = ref.current;

    if (!el) return;

    const cb = () => stableCallback(el);

    el.addEventListener('scroll', cb);

    const resizeObserver = new ResizeObserver(cb);
    resizeObserver.observe(el);

    const childResizeObservers: ResizeObserver[] = [];
    const mutationObserver = new MutationObserver((mutations) => {
      mutations.forEach(() => {
        // Disconnect old child observers
        childResizeObservers.forEach((o) => o.disconnect());

        // Observe new children
        Array.from(el.children).forEach((child) => {
          const childObserver = new ResizeObserver(cb);
          childObserver.observe(child);
          childResizeObservers.push(childObserver);
        });

        cb();
      });
    });
    mutationObserver.observe(el, { childList: true });

    Array.from(el.children).forEach((child) => {
      const childObserver = new ResizeObserver(cb);

      childObserver.observe(child);
      childResizeObservers.push(childObserver);
    });

    return () => {
      el.removeEventListener('scroll', cb);
      resizeObserver.disconnect();
      mutationObserver.disconnect();
      childResizeObservers.forEach((o) => o.disconnect());
    };
  }, [ref, stableCallback]);
}
