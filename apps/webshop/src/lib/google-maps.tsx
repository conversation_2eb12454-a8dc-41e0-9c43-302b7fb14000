'use client';

import { APIProvider } from '@vis.gl/react-google-maps';
import { env } from 'next-runtime-env';

const key =
  env('NEXT_PUBLIC_GOOGLE_MAPS_API_KEY') || process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;

export const GoogleMapsAPIProvider = ({ children }: { children: React.ReactNode }) => {
  if (!key) {
    console.warn('Google Maps API key is not set');
    return children;
  }

  return <APIProvider apiKey={key}>{children}</APIProvider>;
};
