'use client';

import { Badge, cn, easings } from '@allo/ui';
import { CheckIcon, ShoppingBagIcon } from 'lucide-react';
import { AnimatePresence, motion } from 'motion/react';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useStableCallback } from '~/lib/hooks/use-stable-callback';
import { useOrderCart, useOrderStore } from '~/lib/order/store';
import { CartDrawer } from './cart-drawer';

const BANNER_DURATION = 3000;
const BANNER_ON_DRAWER_DELAY = 256;

type Banner = {
  label: string;
  key: number;
  delay: number;
};

export const CartButton = () => {
  const ref = useRef<HTMLDivElement>(null);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const { items, hasCartId } = useOrderCart();
  const quantity = useMemo(
    () => items.reduce((acc, item) => acc + item.quantity, 0),
    [items]
  );

  const [banner, setBanner] = useState<Banner | null>(null);

  useQuantityObserver((diff) => {
    const element = ref.current;
    if (!element || isDrawerOpen || diff === 0) return;

    const type = diff > 0 ? 'increase' : 'decrease';

    element.animate(
      [
        { transform: 'scale(1)' },
        { transform: `scale(${type === 'increase' ? 1.2 : 0.85})` },
        { transform: 'scale(1)' },
      ],
      { duration: 200, easing: 'ease-out' }
    );

    if (diff > 0) {
      const hasOpenDrawer = document.querySelector('dialog[open]');
      const delay = hasOpenDrawer ? BANNER_ON_DRAWER_DELAY : 0;

      setBanner((prev) => ({
        label: `${diff} item${diff === 1 ? '' : 's'} added to cart`,
        key: prev ? prev.key + 1 : 0,
        delay: delay / 1000,
      }));
    }
  });

  useEffect(() => {
    if (banner) {
      const id = setTimeout(() => setBanner(null), BANNER_DURATION);
      return () => clearTimeout(id);
    }
  }, [banner]);

  return (
    <>
      {hasCartId && (
        <button
          className="relative h-full flex items-center p-3 rounded-lg outline-none focus-visible:ring-4 focus-visible:ring-focus"
          onClick={() => setIsDrawerOpen(true)}
        >
          <div className="relative">
            <ShoppingBagIcon className="size-4" />
            <div
              ref={ref}
              className="absolute -top-1.5 left-full -ml-2 px-0.75 bg-accent text-xs min-w-4 h-4 rounded-full flex items-center justify-center text-background-highlight border border-background-low"
            >
              {quantity}
            </div>
          </div>
        </button>
      )}
      <CartDrawer open={isDrawerOpen} onOpenChange={setIsDrawerOpen} />

      {/* quantity update banners */}
      <div className="fixed w-full h-full left-0 top-(--header-height)" inert>
        <div className="relative max-w-container mx-auto my-2">
          <AnimatePresence>
            {banner && (
              <motion.div
                key={banner.key}
                role="alert"
                aria-live="polite"
                className={cn(
                  'absolute right-0 top-0 origin-top-center',
                  'w-60 px-2.5 py-2 flex items-center gap-2 text-sm whitespace-nowrap truncate',
                  'border border-border-soft bg-background-high shadow-lg rounded-xl'
                )}
                initial={{ y: '100%', opacity: 0 }}
                animate={{
                  opacity: 1,
                  y: 0,
                  transition: {
                    duration: 0.4,
                    delay: banner.delay,
                    ease: easings['emphasized-decelerate'],
                    opacity: { duration: 0.05, delay: banner.delay },
                  },
                }}
                exit={{
                  opacity: 0,
                  scale: 0.6,
                  filter: 'blur(10px)',
                  transition: { duration: 0.25, ease: easings['emphasized-accelerate'] },
                }}
              >
                <Badge
                  variant="positive"
                  size="xs"
                  className="aspect-square px-0 flex items-center justify-center"
                >
                  <CheckIcon className="size-3.5" />
                </Badge>
                {banner.label}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </>
  );
};

const useQuantityObserver = (onChange: (diff: number) => void) => {
  const isCartHydrated = useRef(false);

  const { items } = useOrderCart();
  const quantity = useMemo(
    () => items.reduce((acc, item) => acc + item.quantity, 0),
    [items]
  );

  const previousQuantity = useRef(quantity);
  const stableOnChange = useStableCallback(onChange);

  useEffect(() => {
    // wait for cart store to hydrate before sending events,
    // otherwise we'll always send an event on initial load
    if (!isCartHydrated.current) {
      isCartHydrated.current = useOrderStore.persist.hasHydrated();
      previousQuantity.current = useOrderStore
        .getState()
        .items.reduce((acc, item) => acc + item.quantity, 0);
    } else {
      stableOnChange(quantity - previousQuantity.current);
      previousQuantity.current = quantity;
    }
  }, [quantity, stableOnChange]);
};
