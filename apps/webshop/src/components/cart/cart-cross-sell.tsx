'use client';

import { useMemo } from 'react';
import useSWRImmutable from 'swr/immutable';
import { useMousePan } from '~/lib/hooks/use-mouse-pan';
import { fetchCrossSellProducts } from '~/lib/mock-data/api';
import { useOrderCart } from '~/lib/order/store';

// I'm assuming the cross-sell products are fetched based on the cart items
// Here I'm using an endpoint to fetch them, but they could also be derived from some data in the cart items
const useCrossSellProducts = () => {
  const { items } = useOrderCart();

  const productIds = useMemo(() => {
    const ids = new Set<string>();

    for (const item of items) {
      if (item.productId) {
        ids.add(item.productId);
      }
    }

    return Array.from(ids);
    // we don't want to re-run this hook when the cart changes
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const { data: products = [], isLoading } = useSWRImmutable(
    ['cross-sell-products', productIds.join(',')],
    async () => {
      return await fetchCrossSellProducts(productIds);
    }
  );

  return { products, isLoading };
};

export const CartCrossSell = () => {
  const { ref } = useMousePan<HTMLUListElement>();
  const { products, isLoading } = useCrossSellProducts();
  return null;
  // return (
  //   <div className="pt-6 pb-3 border-t border-border-soft">
  //     <h2 className="text-base px-3 md:px-5">Here&apos;s what might be missing:</h2>
  //     <ul
  //       ref={ref}
  //       inert={isLoading}
  //       className={cn(
  //         'px-3 md:px-5 flex gap-2 overflow-x-auto py-3 snap-x snap-mandatory select-none scroll-px-5',
  //         'cursor-grab active:cursor-grabbing hover:[&_[data-product-card]]:cursor-grab active:[&_[data-product-card]]:cursor-grabbing'
  //       )}
  //     >
  //       {!isLoading &&
  //         products.map((product) => {
  //           return (
  //             <li
  //               key={product.id}
  //               className="w-full md:w-100 shrink-0 snap-center md:snap-start"
  //             >
  //               <ProductCard
  //                 product={product}
  //                 size="sm"
  //                 variant="cross-sell"
  //                 data-product-card
  //               />
  //             </li>
  //           );
  //         })}
  //       {isLoading &&
  //         new Array(3).fill(null).map((_, i) => (
  //           <li
  //             key={i}
  //             className="w-full h-30 md:w-100 shrink-0 snap-center md:snap-start"
  //           >
  //             <Skeleton className="w-full h-full" />
  //           </li>
  //         ))}
  //     </ul>
  //   </div>
  // );
};
