'use client';

import {
  Bad<PERSON>,
  cn,
  Price,
  QuantitySelector,
  QuantitySelectorDecrease,
  QuantitySelectorDelete,
  QuantitySelectorIncrease,
  QuantitySelectorQuantity,
} from '@allo/ui';
import { CheckIcon, ForkKnifeIcon, XIcon } from 'lucide-react';
import { AnimatePresence, motion } from 'motion/react';
import Image from 'next/image';
import { useMemo, useState } from 'react';
import { InlineButton } from '~/components/inline-button';
import { deleteCartItem, patchUpdateCartItem } from '~/lib/api';
import {
  calculateCartItemOptionTotalPrice,
  calculateCartItemTotalPrice,
} from '~/lib/order/cart/utils';
import { OrderStoreActions, useOrderCart } from '~/lib/order/store';
import { CartItem } from '~/lib/order/types';
import { useRestaurant } from '~/lib/restaurant.tsx';

interface CartItemActions {
  add: OrderStoreActions['addItem'];
  remove: OrderStoreActions['removeItem'];
}

interface CartItemCardProps {
  item: CartItem;
  className?: string;
  actions?: CartItemActions;
}

export const CartItemCard = ({ item, className, actions }: CartItemCardProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const totalPrice = useMemo(() => calculateCartItemTotalPrice(item), [item]);
  const { cartId, setCartData } = useOrderCart();
  const { id: restaurantId = '' } = useRestaurant();

  const handleAdd = () => {
    const { quantity, id, productId } = item;
    if (actions) {
      patchUpdateCartItem(
        restaurantId,
        id,
        { qtd: quantity + 1, code: productId },
        cartId
      )
        .then((d) => {
          const data = d.data;
          if (data) {
            setCartData(data, true);
          }
          // actions.add({ ...item, quantity: 1 });
        })
        .catch((err) => {
          // TODO: Error for adding more in cart
        });
    }
  };

  const handleRemove = () => {
    const { quantity, id, productId } = item;
    if (actions) {
      patchUpdateCartItem(
        restaurantId,
        id,
        { qtd: quantity - 1, code: productId },
        cartId
      )
        .then((d) => {
          const data = d.data;
          if (data) {
            setCartData(data, true);
          }
          // actions.add({ ...item, quantity: 1 });
        })
        .catch((err) => {
          // TODO: Error for adding more in cart
        });
    }
  };

  const handleDelete = () => {
    const { id } = item;
    if (actions) {
      deleteCartItem(restaurantId, id, cartId)
        .then((d) => {
          const data = d.data;
          if (data) {
            setCartData(data, true);
          }
        })
        .catch((err) => {
          // TODO: Error for deleting item in cart
        });
    }
  };

  return (
    <div className={cn('p-3 md:p-5 text-sm border-b border-border-soft', className)}>
      <div className="flex gap-2.5 items-center">
        {item.image && (
          <figure className="relative size-10 rounded-xl overflow-hidden shrink-0">
            <Image
              src={item.image}
              alt={item.title}
              fill
              sizes="96px"
              className="object-cover bg-background"
            />
          </figure>
        )}
        <div className="flex-1">
          <p className="line-clamp-1 text-ellipsis">{item.title}</p>
          <div className="flex gap-1 max-md:flex-wrap">
            <span>
              {item.quantity} item{item.quantity > 1 ? 's' : ''}
            </span>
            <span>·</span>
            <Price amount={totalPrice} />
            <span className="max-md:hidden">·</span>
            <InlineButton
              className="max-md:w-full"
              onClick={() => setIsExpanded(!isExpanded)}
              variant="secondary"
            >
              {isExpanded ? 'Hide Details' : 'Show Details'}
            </InlineButton>
          </div>
        </div>
        {actions && (
          <QuantitySelector
            quantity={item.quantity}
            min={0}
            onChange={(qty) => {
              if (qty > item.quantity) handleAdd();
              if (qty < item.quantity) handleRemove();
            }}
          >
            {item.quantity > 0 && (
              <>
                {item.quantity > 1 && <QuantitySelectorDecrease size="sm" />}
                {item.quantity === 1 && (
                  <QuantitySelectorDelete size="sm" onClick={() => handleDelete()} />
                )}
                <QuantitySelectorQuantity />
              </>
            )}
            <QuantitySelectorIncrease size="sm" />
          </QuantitySelector>
        )}
      </div>
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            className="overflow-hidden"
            initial={{ height: 0 }}
            animate={{ height: 'auto' }}
            exit={{ height: 0 }}
            transition={{ duration: 0.3, bounce: 0, type: 'spring' }}
          >
            <PriceDetails item={item} />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

interface PriceDetailsProps {
  item: CartItem;
}

const PriceDetails = ({ item }: PriceDetailsProps) => {
  return (
    <div className="flex flex-col">
      <div className="bg-foreground/2 mt-3 flex items-center justify-between rounded-sm p-1.5">
        <div className="flex items-center gap-1.5">
          <Badge
            variant="default"
            size="sm"
            className="text-foreground-secondary flex size-5 items-center justify-center p-0"
          >
            <ForkKnifeIcon />
          </Badge>
          <span>Item Total</span>
        </div>
        <Price amount={calculateCartItemTotalPrice(item)} />
      </div>
      {item.options.map((option) => (
        <div
          key={option.id}
          className="odd:bg-foreground/2 flex items-center justify-between rounded-sm p-1.5"
        >
          <div className="flex items-center gap-1.5">
            <Badge
              variant={option.quantity > 0 ? 'positive' : 'default'}
              size="sm"
              className="flex size-5 items-center justify-center p-0"
            >
              {option.quantity === 0 ? (
                <XIcon />
              ) : option.quantity === 1 ? (
                <CheckIcon />
              ) : (
                option.quantity
              )}
            </Badge>
            <span>{option.title}</span>
          </div>
          <div className="text-foreground-secondary flex items-center gap-3">
            {option.quantity > 1 && option.extraUnitPrice > 0 && (
              <span>
                <Price
                  amount={calculateCartItemOptionTotalPrice({
                    ...option,
                    quantity: option.initialQuantity + 1,
                  })}
                />{' '}
                ea.
              </span>
            )}
            <Price amount={calculateCartItemOptionTotalPrice(option)} />
          </div>
        </div>
      ))}
      {item.extras.map((extra) => (
        <div
          key={extra.id}
          className="odd:bg-foreground/2 flex items-center justify-between rounded-sm p-1.5"
        >
          <div className="flex items-center gap-1.5">
            <Badge
              variant={extra.quantity > 0 ? 'positive' : 'default'}
              size="sm"
              className="flex size-5 items-center justify-center p-0"
            >
              {extra.quantity === 0 ? (
                <XIcon />
              ) : extra.quantity === 1 ? (
                <CheckIcon />
              ) : (
                extra.quantity
              )}
            </Badge>
            <span>{extra.title}</span>
          </div>
          <div className="text-foreground-secondary flex items-center gap-3">
            {extra.quantity > 1 && extra.extraUnitPrice > 0 && (
              <span>
                <Price
                  amount={calculateCartItemOptionTotalPrice({
                    ...extra,
                    quantity: extra.initialQuantity + 1,
                  })}
                />{' '}
                ea.
              </span>
            )}
            <Price amount={calculateCartItemOptionTotalPrice(extra)} />
          </div>
        </div>
      ))}
    </div>
  );
};
