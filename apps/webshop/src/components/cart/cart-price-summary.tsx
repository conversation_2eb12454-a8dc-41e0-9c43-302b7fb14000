'use client';

import { Price } from '@allo/ui';
import { AnimatePresence } from 'motion/react';
import { useOrderCart } from '~/lib/order/store.ts';
import { CartItem, Discount } from '~/lib/order/types';

interface CartPriceSummaryProps {
  items: CartItem[];
  discount?: Discount | null;
  actions?: {
    removeDiscount: () => void;
  };
}

export const CartPriceSummary = ({ items, discount, actions }: CartPriceSummaryProps) => {
  const { totalPrice, priceWithoutTaxes, taxesTotal } = useOrderCart();

  return (
    <AnimatePresence>
      <ul className="text-sm text-foreground-secondary *:flex *:justify-between *:gap-2 space-y-3">
        <li>
          <span>Subtotal</span>
          <Price amount={priceWithoutTaxes} />
        </li>
        {/*<AnimatePresence initial={false}>*/}
        {/*  {!!discount && (*/}
        {/*    <motion.li*/}
        {/*      initial={{ height: 0, opacity: 0 }}*/}
        {/*      animate={{*/}
        {/*        height: 'auto',*/}
        {/*        opacity: 1,*/}
        {/*        transition: { duration: 0.4, ease: easings['emphasized-decelerate'] },*/}
        {/*      }}*/}
        {/*      exit={{*/}
        {/*        height: 0,*/}
        {/*        opacity: 0,*/}
        {/*        marginTop: 0,*/}
        {/*        marginBottom: 0,*/}
        {/*        transition: { duration: 0.15, ease: easings['emphasized-accelerate'] },*/}
        {/*      }}*/}
        {/*    >*/}
        {/*      <div className="flex items-center gap-1">*/}
        {/*        <span>Discount Code ({discount.code})</span>*/}
        {/*        {actions && (*/}
        {/*          <InlineButton*/}
        {/*            aria-label="Remove discount code"*/}
        {/*            onClick={actions.removeDiscount}*/}
        {/*            variant="secondary"*/}
        {/*          >*/}
        {/*            <XIcon />*/}
        {/*          </InlineButton>*/}
        {/*        )}*/}
        {/*      </div>*/}
        {/*      <Price prefix="-" amount={discountAmount} />*/}
        {/*    </motion.li>*/}
        {/*  )}*/}
        {/*</AnimatePresence>*/}
        {!!taxesTotal && (
          <li>
            <span>Pfand</span>
            <Price amount={taxesTotal} />
          </li>
        )}
        <li className="text-foreground text-base">
          <span>Total</span>
          <Price amount={totalPrice} />
        </li>
      </ul>
    </AnimatePresence>
  );
};
