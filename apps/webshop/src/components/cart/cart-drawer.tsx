'use client';

import {
  But<PERSON>,
  cn,
  Drawer,
  Drawer<PERSON><PERSON>,
  DrawerClose,
  Drawer<PERSON>ontent,
  Drawer<PERSON>eader,
  DrawerTitle,
  Input,
} from '@allo/ui';
import { XIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import posthog from 'posthog-js';
import { useEffect, useState, useTransition } from 'react';
import { IconButton } from '~/components/icon-button';
import { Link } from '~/i18n/navigation';
import { postAddPromotionToCart } from '~/lib/api';
import { useGetOrCreateCart } from '~/lib/order/cart/hooks.ts';
import { useOrderCart, useOrderDiscount } from '~/lib/order/store';
import { useRestaurant } from '~/lib/restaurant';
import { getCaptureId } from '~/lib/utils/posthog';
import { CartCrossSell } from './cart-cross-sell';
import { CartItemCard } from './cart-item-card';
import { CartPriceSummary } from './cart-price-summary';

interface CartDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const tryToGetCartFromLocalStorage = () => {
  try {
    const orderStorage = JSON.parse(
      localStorage.getItem('order-storage') || '{state: {cartId: ""}}'
    );
    if (orderStorage?.state?.cartId) {
      return orderStorage?.state?.cartId;
    }
  } catch (e) {
    return '';
  }
};

export const CartDrawer = ({ open, onOpenChange }: CartDrawerProps) => {
  const { items } = useOrderCart();
  const { discount, setDiscount } = useOrderDiscount();
  const router = useRouter();
  const { addItem, removeItem } = useOrderCart();
  const { slug, id } = useRestaurant();
  const [isSettingDiscount, setIsSettingDiscount] = useState(false);
  const cartId = tryToGetCartFromLocalStorage();
  const { cart, isLoading, withResetStore } = useGetOrCreateCart({
    restaurantId: id || '',
    cartId: cartId,
  });
  const { setCartData } = useOrderCart();
  useEffect(() => {
    if (!isLoading && cart) {
      if (cart.status === 'COMPLETED') {
        // Route to order confirmation page
        router.push(`/en/restaurant/${slug}/orders/${cart.orderId}`);
      } else if (
        cart.status === 'PAYMENT_REQUESTED' ||
        cart.status === 'PAYMENT_FAILED'
      ) {
        router.push(`/en/restaurant/${slug}/checkout`);
      } else {
        setCartData(cart, withResetStore);
      }
    }
  }, [isLoading, cart, setCartData, withResetStore, router, slug]);

  useEffect(() => {
    if (!open) {
      setIsSettingDiscount(false);
    }
  }, [open]);

  return (
    <Drawer
      open={open}
      onOpenChange={(open) => {
        if (open) {
          posthog.capture(getCaptureId('cart_drawer', 'open', 'click'), {
            items,
          });
        }

        onOpenChange(open);
      }}
    >
      <DrawerContent className="bg-background-high">
        <DrawerHeader className="flex items-center justify-between">
          <DrawerTitle>Your Cart</DrawerTitle>
          <DrawerClose asChild>
            <IconButton>
              <XIcon />
            </IconButton>
          </DrawerClose>
        </DrawerHeader>
        <div className="min-h-[50vh]">
          {items.length > 0 ? (
            <ul className="-mb-0.25">
              {items.map((item) => {
                return (
                  <li key={item.id}>
                    <CartItemCard
                      item={item}
                      actions={{ add: addItem, remove: removeItem }}
                    />
                  </li>
                );
              })}
            </ul>
          ) : (
            <div className="p-5 text-foreground-secondary">Your cart is empty</div>
          )}
          <CartCrossSell />
        </div>
        <DrawerActions className="flex flex-col gap-4">
          <CartPriceSummary
            items={items}
            discount={discount}
            actions={{
              removeDiscount: () => {
                setDiscount(null);

                posthog.capture(getCaptureId('cart_drawer', 'discount_code', 'remove'), {
                  discount,
                });
              },
            }}
          />
          <div className="relative w-full">
            <div className="flex gap-2 w-full">
              {isSettingDiscount ? (
                <>
                  <DiscountCodeForm onApply={() => setIsSettingDiscount(false)} />
                  <Button square onClick={() => setIsSettingDiscount(false)}>
                    <XIcon />
                  </Button>
                </>
              ) : (
                <>
                  <div className="flex gap-2 w-full">
                    <Button onClick={() => setIsSettingDiscount(true)}>
                      Discount Code
                    </Button>
                    <Button
                      asChild
                      className="grow"
                      variant="accent"
                      disabled={!items.length}
                    >
                      <Link href={`/restaurant/${slug}/checkout`}>Checkout</Link>
                    </Button>
                  </div>
                </>
              )}
            </div>
          </div>
        </DrawerActions>
      </DrawerContent>
    </Drawer>
  );
};

interface DiscountCodeFormProps {
  onApply: (code: string | null) => void;
}

const DiscountCodeForm = ({ onApply }: DiscountCodeFormProps) => {
  const { discount, setDiscount } = useOrderDiscount();
  const [value, setValue] = useState(discount?.code || '');
  const [isInvalid, setIsInvalid] = useState(false);
  const [isPending, startTransition] = useTransition();
  const { id: restaurantId } = useRestaurant();
  const { cartId } = useOrderCart();

  const handleApplyDiscountCode = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    startTransition(async () => {
      postAddPromotionToCart(restaurantId || '', value, cartId)
        .then((result) => {
          if (result.error) {
            setIsInvalid(true);
          } else {
            // TODO: Add discount (promo)
            // posthog.capture(getCaptureId('cart_drawer', 'discount_code', 'apply'), {
            //   discount,
            // });
          }
        })
        .catch((error) => {
          setIsInvalid(true);
        });
      //const discount = await fetchDiscountCode(value);

      // if (discount) {
      //   setDiscount({
      //     amount: discount.discount,
      //     code: discount.code,
      //   });
      //
      //   onApply(value);
      // } else {
      //   setIsInvalid(true);
      // }
    });
  };

  return (
    <form
      onSubmit={handleApplyDiscountCode}
      className="w-full flex gap-2"
      inert={isPending}
    >
      <Input
        name="discountCode"
        className="grow w-full"
        data-invalid={isInvalid ? 'true' : undefined}
        autoFocus
        placeholder="Discount Code"
        value={value}
        onChange={(e) => {
          setValue(e.target.value);
          setIsInvalid(false);
        }}
      />
      <Button
        type="submit"
        className={cn('grow min-w-1/3', isInvalid && 'animate-shake')}
        variant="accent"
        isLoading={isPending}
        disabled={!value || !value.length || discount?.code === value}
      >
        Apply
      </Button>
    </form>
  );
};
