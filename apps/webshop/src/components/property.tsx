'use client';

import { cn, Slot } from '@allo/ui';

interface PropertyProps extends React.ComponentPropsWithoutRef<'p'> {
  asChild?: boolean;
  variant?: 'primary' | 'secondary';
  size?: 'xs' | 'sm';
}

export const Property = ({
  className,
  children,
  asChild,
  variant = 'primary',
  size = 'sm',
  ...props
}: PropertyProps) => {
  const Component = asChild ? Slot : 'p';
  return (
    <Component
      className={cn(
        'truncate flex items-center gap-1 *:truncate overflow-hidden',
        '[&_svg]:size-em [&_svg]:text-foreground-secondary [&_svg]:shrink-0',
        variant === 'primary' && 'text-foreground',
        variant === 'secondary' && 'text-foreground-secondary',
        size === 'xs' && 'text-xs',
        size === 'sm' && 'text-sm',
        className
      )}
      {...props}
    >
      {children}
    </Component>
  );
};
