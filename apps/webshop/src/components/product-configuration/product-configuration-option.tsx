import {
  Badge,
  <PERSON>ton,
  Price,
  ProductCard,
  ProductCardContent,
  ProductCardCover,
  ProductCardDescription,
  ProductCardFooter,
  ProductCardTitle,
  QuantitySelector,
  QuantitySelectorDecrease,
  QuantitySelectorDelete,
  QuantitySelectorIncrease,
  QuantitySelectorQuantity,
  Radio,
} from '@allo/ui';
import { Plus } from 'lucide-react';
import Image from 'next/image';
import { ProductOption as ProductOptionType } from '~/lib/mock-data/products';
import {
  calculateCartItemOptionTotalPrice,
  productOptionToCartItemOption,
} from '~/lib/order/cart/utils';
import { useProductConfigurationContext } from './product-configuration-context';

interface ProductConfigurationOptionProps {
  type: 'radio' | 'quantity';
  groupId: string;
  option: ProductOptionType;
}

export const ProductConfigurationOption = ({
  type,
  groupId,
  option,
}: ProductConfigurationOptionProps) => {
  const { cartItem, dispatch } = useProductConfigurationContext();
  const optionInCart = cartItem.options.find((o) => option.id === o.id);

  const Wrapper = type === 'radio' ? 'label' : 'div';

  return (
    <ProductCard key={option.id} depth asChild>
      <Wrapper>
        {option.image && (
          <ProductCardCover>
            {option.tag && (
              <Badge size="xs" variant="over-media">
                {option.tag}
              </Badge>
            )}
            <Image src={option.image} alt={option.title} fill sizes="480px" />
          </ProductCardCover>
        )}
        <ProductCardContent>
          <ProductCardTitle>
            <span>{option.title}</span>
            {!option.image && option.tag && <Badge size="xs">{option.tag}</Badge>}
          </ProductCardTitle>
          {option.description && (
            <ProductCardDescription>{option.description}</ProductCardDescription>
          )}
          <ProductCardFooter>
            <Price
              prefix={
                optionInCart &&
                optionInCart.extraUnitPrice &&
                optionInCart.quantity > optionInCart.initialQuantity
                  ? '+'
                  : undefined
              }
              amount={
                optionInCart
                  ? calculateCartItemOptionTotalPrice(optionInCart) ||
                    option.extraUnitPrice
                  : option.extraUnitPrice
              }
            />

            {type === 'radio' ? (
              <Radio
                name={groupId}
                value={option.id}
                checked={!!(optionInCart && optionInCart.quantity === 1)}
                onChange={() => {
                  dispatch({
                    type: 'set-in-group',
                    payload: productOptionToCartItemOption(groupId, option, 1),
                  });
                }}
              />
            ) : (
              <>
                {optionInCart ? (
                  <QuantitySelector
                    size="sm"
                    quantity={optionInCart.quantity}
                    min={option.min}
                    max={option.max || undefined}
                    onChange={(qty) => {
                      dispatch({
                        type: 'set',
                        payload: productOptionToCartItemOption(groupId, option, qty),
                      });
                    }}
                  >
                    {optionInCart.quantity === option.min + 1 ? (
                      <QuantitySelectorDelete
                        onClick={() => {
                          return optionInCart.initialQuantity
                            ? dispatch({
                                type: 'set',
                                payload: productOptionToCartItemOption(
                                  groupId,
                                  option,
                                  option.min
                                ),
                              })
                            : dispatch({ type: 'remove', payload: option.id });
                        }}
                      />
                    ) : (
                      <QuantitySelectorDecrease />
                    )}
                    <QuantitySelectorQuantity />
                    <QuantitySelectorIncrease />
                  </QuantitySelector>
                ) : (
                  <Button
                    size="sm"
                    aria-label="Add"
                    square
                    onClick={() => {
                      dispatch({
                        type: 'set',
                        payload: productOptionToCartItemOption(groupId, option, 1),
                      });
                    }}
                  >
                    <Plus />
                  </Button>
                )}
              </>
            )}
          </ProductCardFooter>
        </ProductCardContent>
      </Wrapper>
    </ProductCard>
  );
};
