import { Badge, cn } from '@allo/ui';
import { Check } from 'lucide-react';
import { ProductConfigurationExtraItem } from '~/components/product-configuration/product-configuration-extra-item.tsx';
import { WebShopMenuItemExtra } from '~/lib/api/types.ts';
import { useProductConfigurationContext } from './product-configuration-context';

interface ProductConfigurationGroupProps {
  group: WebShopMenuItemExtra;
  shouldValidate: boolean;
  className?: string;
}

export const ProductConfigurationGroupExtra = ({
  group,
  shouldValidate,
  className,
}: ProductConfigurationGroupProps) => {
  const { cartItem } = useProductConfigurationContext();

  const isRequired = false; // group.min > 0;
  const isInCart = cartItem.options.some((o) => o.groupId === group.id);

  const error = false; //useMemo(() => getCartItemGroupError(cartItem, group), [cartItem, group]);

  return (
    <div id={group.id} className={cn('border-border-soft not-last:border-b', className)}>
      <div className={cn(shouldValidate && error && 'animate-shake')}>
        <div className="flex items-center gap-2">
          <h2 className="inline">{group.name}</h2>
          {shouldValidate && error ? (
            <div className="flex gap-2">
              <Badge size="sm" variant="negative">
                {/*<X /> {error.message}*/}
              </Badge>
            </div>
          ) : (
            <>
              {isRequired && (
                <Badge size="sm" variant={isInCart && !error ? 'positive' : 'default'}>
                  {isInCart && !error && <Check />} Required
                </Badge>
              )}
            </>
          )}
        </div>
        {(group.max || 0) > 1 && (group.max || 0) < 99 && (
          <p className="text-foreground-secondary text-sm">Select up to {group.max}</p>
        )}
      </div>
      <div className="mt-4 grid auto-rows-fr grid-cols-[repeat(auto-fill,minmax(200px,1fr))] gap-3">
        {group?.items &&
          group?.items.map((item) => (
            <ProductConfigurationExtraItem
              key={`${group.id}-${item.id}`}
              type={group.max === 1 ? 'radio' : 'quantity'}
              groupId={group.id || ''}
              option={item}
            />
          ))}
        {/*{group.options.map((option) => (*/}
        {/*  <ProductConfigurationOption*/}
        {/*    key={option.id}*/}
        {/*    type={group.min === 1 && group.max === 1 ? 'radio' : 'quantity'}*/}
        {/*    groupId={group.id}*/}
        {/*    option={option}*/}
        {/*  />*/}
        {/*))}*/}
      </div>
    </div>
  );
};
