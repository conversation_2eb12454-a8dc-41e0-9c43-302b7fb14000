'use client';

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Drawer,
  <PERSON>er<PERSON><PERSON>,
  <PERSON>er<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  QuantitySelector,
  QuantitySelectorDecrease,
  QuantitySelectorIncrease,
  QuantitySelectorQuantity,
} from '@allo/ui';
import { XIcon } from 'lucide-react';
import Image from 'next/image';
import { useReducer, useState } from 'react';
import { IconButton } from '~/components/icon-button';
import { ProductConfigurationGroupExtra } from '~/components/product-configuration/product-configuration-group-extra.tsx';
import { postAddItemToCart } from '~/lib/api';
import { WebshopMenuItem } from '~/lib/api/types.ts';
import {
  calculateCartItemTotalPrice,
  cartItemToWebShopOrderCartAddItemRequest,
  productsToCartItems,
  productToCartItem,
} from '~/lib/order/cart/utils';
import { getCartItemFirstGroupError } from '~/lib/order/cart/validation';
import { useOrderCart } from '~/lib/order/store';
import { useRestaurant } from '~/lib/restaurant.tsx';
import { getImageSizes } from '~/lib/utils/next';
import {
  cartItemReducer,
  ProductConfigurationContext,
} from './product-configuration-context';
import { ProductConfigurationGroupOption } from './product-configuration-group-option';

interface ProductConfigurationDrawerProps {
  product?: WebshopMenuItem | null;
  onOpenChange?: (open: boolean) => void;
}

export const ProductConfigurationDrawer = ({
  product,
  onOpenChange,
}: ProductConfigurationDrawerProps) => {
  return (
    <Drawer open={!!product} onOpenChange={onOpenChange}>
      <div className="absolute">
        <DrawerContent className="bg-background-high scroll-pt-16 cursor-auto">
          {product && (
            <ProductConfigurationDrawerContent
              product={product}
              requestClose={() => onOpenChange?.(false)}
            />
          )}
        </DrawerContent>
      </div>
    </Drawer>
  );
};

interface ProductConfigurationDrawerContentProps {
  product: WebshopMenuItem;
  requestClose: () => void;
}

export const ProductConfigurationDrawerContent = ({
  product,
  requestClose,
}: ProductConfigurationDrawerContentProps) => {
  const [cartItem, dispatch] = useReducer(cartItemReducer, product, (product) => {
    return productToCartItem(product);
  });
  const { id: restaurantId } = useRestaurant();

  const [liveValidationGroups, setLiveValidationGroups] = useState(
    new Map<string, number>()
  ); // save a timestamp to trigger a re-render (and a shake animation) when the error isn't resolved and the user insists on adding to cart

  const scrollToGroup = (groupId: string) => {
    const group = document.getElementById(groupId);

    if (group) {
      group.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const { setItems, setCartData, cartId } = useOrderCart();

  const handleAddToCart = () => {
    const error = getCartItemFirstGroupError(cartItem, product);
    if (error) {
      // scrollToGroup(error.groupId);
      // return setLiveValidationGroups((prev) => {
      //   prev.set(error.groupId, Date.now());
      //   return new Map(prev);
      // });
    }
    const itemAddRequest = cartItemToWebShopOrderCartAddItemRequest(cartItem);
    const a = postAddItemToCart(restaurantId || '', itemAddRequest, cartId);
    a.then((response) => {
      const d = response.data;
      if (d) {
        const items = productsToCartItems(d.items || []);
        setItems(items);
        setCartData(d, true);
        requestClose();
      } else {
        // TODO: ERROR
      }
    }).catch((err) => {
      // TODO: Error
    });
  };

  return (
    <>
      <DrawerHeader className="flex items-center justify-between rounded-none">
        <DrawerTitle>{product.name}</DrawerTitle>
        <DrawerClose asChild>
          <IconButton>
            <XIcon />
          </IconButton>
        </DrawerClose>
      </DrawerHeader>
      {product?.thumbnailUrl && (
        <figure className="relative w-full aspect-[5/2]">
          <Image
            src={product.thumbnailUrl}
            alt={product.name || ''}
            fill
            className="object-cover"
            sizes={getImageSizes({ default: '100vw', md: '50vw' })}
          />
        </figure>
      )}
      {(product.description || (product.tags && product.tags?.length > 0)) && (
        <div className="border-border-soft flex flex-col gap-5 border-b px-3 py-4 md:px-5 md:py-6">
          {product.description && <p className="text-sm">{product.description}</p>}
          {/* TODO: Allegens? */}
          {product.tags && product.tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {product.tags.map((tag) => (
                <Badge key={tag.id} size="sm">
                  {tag.label}
                </Badge>
              ))}
            </div>
          )}
        </div>
      )}
      <div>
        <ProductConfigurationContext value={{ cartItem, dispatch }}>
          {product.extras?.map((group) => (
            <ProductConfigurationGroupExtra
              key={liveValidationGroups.get(group.id || '') || group.id}
              group={group}
              shouldValidate={liveValidationGroups.has(group.id || '')}
              className="px-3 py-4 md:px-5 md:py-6 last:pb-8"
            />
          ))}
          {product.options?.map((group) => (
            <ProductConfigurationGroupOption
              key={liveValidationGroups.get(group.id || '') || group.id}
              group={group}
              shouldValidate={liveValidationGroups.has(group.id || '')}
              className="px-3 py-4 md:px-5 md:py-6 last:pb-8"
            />
          ))}
          {/*{product.configuration.map((group) => (*/}
          {/*  <ProductConfigurationGroup*/}
          {/*    key={liveValidationGroups.get(group.id) || group.id}*/}
          {/*    group={group}*/}
          {/*    shouldValidate={liveValidationGroups.has(group.id)}*/}
          {/*    className="px-3 py-4 md:px-5 md:py-6 last:pb-8"*/}
          {/*  />*/}
          {/*))}*/}
        </ProductConfigurationContext>
      </div>
      <DrawerActions>
        <QuantitySelector
          className="w-32"
          quantity={cartItem.quantity}
          min={1}
          onChange={(qty) => dispatch({ type: 'quantity', payload: qty })}
        >
          <QuantitySelectorDecrease />
          <QuantitySelectorQuantity />
          <QuantitySelectorIncrease />
        </QuantitySelector>
        <Button onClick={handleAddToCart} variant="accent" className="grow">
          Add to cart · <Price amount={calculateCartItemTotalPrice(cartItem)} />
        </Button>
      </DrawerActions>
    </>
  );
};
