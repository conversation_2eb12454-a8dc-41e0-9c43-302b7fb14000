'use client';

import { Slot, cn } from '@allo/ui';
import { motion } from 'motion/react';
import {
  ComponentProps,
  createContext,
  use,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';

interface AnchorNavigationContextType {
  activeValue: string | null;
  setActiveValue: (newValue: string, timeoutLock?: boolean) => void;
  scrollToAnchorItem: (element: HTMLAnchorElement) => void;
}

const AnchorNavigationContext = createContext<AnchorNavigationContextType>({
  activeValue: null,
  setActiveValue: () => {},
  scrollToAnchorItem: () => {},
});

const useAnchorNavigationContext = () => {
  const ctx = use(AnchorNavigationContext);

  if (!ctx) {
    throw new Error(
      'useAnchorNavigationContext needs to used within an AnchorNavigation'
    );
  }

  return ctx;
};

interface AnchorNavigationProps extends ComponentProps<'nav'> {
  asChild?: boolean;
  defaultValue?: string;
}

export const AnchorNavigation = ({
  asChild,
  children,
  className,
  defaultValue,
  ...props
}: AnchorNavigationProps) => {
  const ref = useRef<HTMLElement>(null);
  const setterLock = useRef<NodeJS.Timeout | null>(null);
  const [activeValue, setActiveValue] = useState<string | null>(defaultValue || null);

  const scrollToAnchorItem = useCallback((child: HTMLAnchorElement) => {
    const element = ref.current;
    if (!element) return;

    const offsetLeft = child.offsetLeft - element.offsetLeft;
    element.scrollTo({ left: offsetLeft, behavior: 'smooth' });
  }, []);

  const handleSetActiveValue = useCallback((newValue: string, timeoutLock?: boolean) => {
    if (!setterLock.current) {
      setActiveValue(newValue);
    }

    if (timeoutLock) {
      if (setterLock.current) {
        clearTimeout(setterLock.current);
        setterLock.current = null;
      }

      // prevents rapid changes to activeValue during smooth scrolling to anchor target
      setterLock.current = setTimeout(() => {
        setterLock.current = null;
      }, 500);
    }
  }, []);

  const Component = asChild ? Slot : 'nav';
  return (
    <AnchorNavigationContext
      value={{ activeValue, setActiveValue: handleSetActiveValue, scrollToAnchorItem }}
    >
      <Component
        ref={ref}
        className={cn('flex items-center overflow-x-scroll', className)}
        {...props}
      >
        {children}
      </Component>
    </AnchorNavigationContext>
  );
};

export const AnchorNavigationItem = ({
  href,
  children,
  className,
  onClick,
  ...props
}: ComponentProps<'a'>) => {
  const ref = useRef<HTMLAnchorElement>(null);
  const { activeValue, setActiveValue, scrollToAnchorItem } =
    useAnchorNavigationContext();
  const isActive = activeValue === href;

  useEffect(() => {
    const element = ref.current;
    if (!href || !element) return;

    const target = document.querySelector(href);
    if (!target) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry?.isIntersecting) {
          setActiveValue(href);
          scrollToAnchorItem(element);
        }
      },
      { rootMargin: '-50% 0px -50% 0px' }
    );

    observer.observe(target);

    return () => observer.disconnect();
  }, [href, scrollToAnchorItem, setActiveValue]);

  return (
    <a
      ref={ref}
      href={href}
      className={cn(
        'relative shrink-0 whitespace-nowrap h-10 flex items-center px-4 text-sm',
        'hover:text-foreground-secondary ring-inset input-focus-visible motion-safe:transition-colors',
        isActive && 'bg-radial-[at_50%_100%] from-accent/8 to-accent/0 to-75%',
        className
      )}
      onClick={(e) => {
        onClick?.(e);

        if (!e.defaultPrevented && href) {
          setActiveValue(href, true);
        }
      }}
      {...props}
    >
      {children}
      {isActive && (
        <motion.span
          className="absolute w-full h-0.5 bg-accent left-0 bottom-0 z-20"
          layoutId="anchor-tab-indicator"
          transition={{ type: 'spring', duration: 0.3, bounce: 0 }}
          inert
        />
      )}
    </a>
  );
};
