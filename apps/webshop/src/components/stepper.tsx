'use client';

import { cn } from '@allo/ui';
import { AnimatePresence, motion } from 'motion/react';
import { createContext, use, useCallback, useEffect, useState } from 'react';
import { Pin } from '~/components/pin';

type StepStatus = 'active' | 'completed' | 'pending';

const StepperContext = createContext<{
  registerStep: (value: string) => void;
  getStatus: (value: string) => StepStatus;
} | null>(null);

const useStepperContext = () => {
  const context = use(StepperContext);

  if (!context) {
    throw new Error('useStepperContext must be used within a Stepper component');
  }

  return context;
};

interface StepperProps extends React.ComponentPropsWithoutRef<'ol'> {
  step: string;
}

export const Stepper = ({ children, className, step, ...props }: StepperProps) => {
  const [steps, setSteps] = useState<string[]>([]);

  const registerStep = useCallback((value: string) => {
    setSteps((prev) => [...prev, value]);
  }, []);

  const getStatus = useCallback(
    (value: string) => {
      if (step === value) return 'active';
      if (steps.indexOf(step) > steps.indexOf(value)) return 'completed';
      return 'pending';
    },
    [steps, step]
  );

  return (
    <StepperContext value={{ registerStep, getStatus }}>
      <ol className={cn('space-y-1 [--transition-delay:400ms]', className)} {...props}>
        {children}
      </ol>
    </StepperContext>
  );
};

const StepContext = createContext<{
  status: StepStatus;
} | null>(null);

const useStepContext = () => {
  const context = use(StepContext);

  if (!context) {
    throw new Error('useStepContext must be used within a Step component');
  }

  return context;
};

interface StepProps extends React.ComponentPropsWithoutRef<'li'> {
  value: string;
  duration?: number;
}

export const Step = ({ children, className, value, duration, ...props }: StepProps) => {
  const { registerStep, getStatus } = useStepperContext();
  const status = getStatus(value);

  useEffect(() => {
    registerStep(value);
  }, [registerStep, value]);

  return (
    <StepContext value={{ status }}>
      <li
        className={cn(
          'grid grid-cols-[--spacing(3)_1fr] grid-rows-[--spacing(4.5)_minmax(--spacing(8),1fr)] gap-x-2.5 gap-y-1',
          'last:[&_[data-progress-indicator]]:hidden',
          className
        )}
        {...props}
      >
        <div className="**:motion-safe:transition **:delay-(--transition-delay) mt-1">
          <Pin
            size="xs"
            variant={status === 'pending' ? 'secondary' : 'accent'}
            pulse={status === 'active'}
          />
        </div>
        {/* <StepMarker status={status} className="mt-1.25" /> */}
        {duration !== undefined && (
          <StepProgressIndicator
            data-progress-indicator
            status={status}
            duration={duration}
          />
        )}
        <div className="col-start-2 row-start-1 row-span-full leading-none">
          {children}
        </div>
      </li>
    </StepContext>
  );
};

export const StepLabel = ({
  children,
  className,
  ...props
}: React.ComponentPropsWithoutRef<'span'>) => {
  const { status } = useStepContext();

  return (
    <span
      className={cn(
        'text-foreground-secondary text-sm motion-safe:transition-colors delay-(--transition-delay)',
        status === 'active' && 'text-foreground',
        className
      )}
      {...props}
    >
      {children}
    </span>
  );
};

interface StepContentProps {
  children: React.ReactNode;
}

export const StepContent = ({ children }: StepContentProps) => {
  const { status } = useStepContext();

  return (
    <AnimatePresence>
      {status === 'active' && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{
            opacity: 1,
            height: 'auto',
            transition: {
              duration: 0.3,
              delay: 0.5,
              ease: 'easeOut',
              opacity: { duration: 0.6, delay: 0.6 },
            },
          }}
          exit={{
            opacity: 0,
            height: 0,
            transition: {
              duration: 0.25,
              delay: 0.5,
              ease: 'easeIn',
              opacity: { duration: 0.3, delay: 0.5 },
            },
          }}
        >
          <div
            className={cn(
              'min-h-0 pt-2 pb-6 motion-safe:transition-opacity duration-250 delay-(--transition-delay) ease-in-out'
            )}
          >
            {children}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

interface StepProgressIndicatorProps extends React.ComponentPropsWithoutRef<'div'> {
  status: StepStatus;
  duration: number;
}

const StepProgressIndicator = ({
  status,
  className,
  duration,
  ...props
}: StepProgressIndicatorProps) => {
  const [progress, setProgress] = useState(status === 'completed' ? 1 : 0);

  useEffect(() => {
    if (status === 'completed') {
      setProgress(1);
      return;
    }
    if (status === 'pending') {
      setProgress(0);
      return;
    }

    if (duration === 0) return;

    const controller = new AbortController();
    const startSimulatedProgress = async () => {
      const MAX_PROGRESS = 0.85;
      const transformer = (p: number) => MAX_PROGRESS * Math.pow(p, 0.667);

      for await (const p of simulatedProgressGenerator(
        duration,
        Math.floor(duration / 400),
        transformer
      )) {
        if (controller.signal.aborted) break;
        setProgress(p);
      }
    };

    startSimulatedProgress();

    return () => {
      controller.abort();
    };
  }, [status, duration]);

  return (
    <div
      className={cn(
        'relative w-0.5 h-full bg-border mx-auto rounded-full overflow-hidden',
        className
      )}
      {...props}
      style={{ '--progress': progress }}
    >
      <div
        className={cn(
          'absolute inset-0 bg-accent origin-top translate-y-[calc(-100%*(1-var(--progress)))] rounded-full',
          'motion-safe:transition-transform ease-in-out duration-(--transition-delay)'
        )}
      />
    </div>
  );
};

export async function* simulatedProgressGenerator(
  duration: number,
  iterations: number,
  easing = (t: number) => t
) {
  const stepTime = duration / iterations;

  for (let i = 0; i <= iterations; i++) {
    const rawProgress = i / iterations;
    const progress = easing(rawProgress);

    yield progress;

    if (i < iterations) {
      await new Promise((resolve) => setTimeout(resolve, stepTime));
    }
  }
}
