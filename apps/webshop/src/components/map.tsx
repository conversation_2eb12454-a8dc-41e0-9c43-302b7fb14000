'use client';

import { cn } from '@allo/ui';
import {
  Map as GoogleMap,
  type MapProps as GoogleMapProps,
} from '@vis.gl/react-google-maps';

import {
  AdvancedMarker,
  AdvancedMarkerAnchorPoint,
  type AdvancedMarkerProps,
} from '@vis.gl/react-google-maps';
import { env } from 'next-runtime-env';
import { useState } from 'react';
import { Pin } from '~/components/pin';
import { useDebouncedValue } from '~/lib/hooks/use-debounced-value';

export interface MapProps extends GoogleMapProps {
  mapId: string;
}

const key =
  env('NEXT_PUBLIC_GOOGLE_MAPS_API_KEY') || process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;

export const Map = ({ children, className, defaultZoom = 17, ...props }: MapProps) => {
  if (!key) {
    return (
      <div
        className={cn(
          'size-full rounded-lg overflow-hidden border border-border-soft bg-background-low text-sm flex items-center justify-center text-foreground-secondary',
          className
        )}
      >
        Error loading map
      </div>
    );
  }

  return (
    <GoogleMap
      className={cn(
        'size-full rounded-lg overflow-hidden border border-border-soft bg-border-soft',
        className
      )}
      defaultZoom={defaultZoom}
      mapTypeControl={false}
      fullscreenControl={false}
      streetViewControl={false}
      zoomControl={false}
      cameraControl={false}
      {...props}
    >
      {children}
    </GoogleMap>
  );
};

interface MarkerProps extends Omit<AdvancedMarkerProps, 'anchorPoint' | 'children'> {
  pulse?: boolean;
  label?: React.ReactNode;
  labelVisibility?: 'hover' | 'always';
}

export const Marker = ({
  label,
  labelVisibility = 'hover',
  pulse,
  ...props
}: MarkerProps) => {
  const [isLabelVisible, setIsLabelVisible] = useState(labelVisibility === 'always');
  const labelZIndex = useDebouncedValue(isLabelVisible ? 1000 : -1, 150);

  return (
    <>
      <AdvancedMarker
        {...props}
        onMouseEnter={(e) => {
          props.onMouseEnter?.(e);

          if (!e.defaultPrevented && labelVisibility === 'hover') {
            setIsLabelVisible(true);
          }
        }}
        onMouseLeave={(e) => {
          props.onMouseLeave?.(e);

          if (!e.defaultPrevented && labelVisibility === 'hover') {
            setIsLabelVisible(false);
          }
        }}
        anchorPoint={AdvancedMarkerAnchorPoint.CENTER}
      >
        <Pin size="sm" pulse={pulse} />
      </AdvancedMarker>
      {label && (
        <AdvancedMarker
          {...props}
          anchorPoint={['50%', '150%']}
          zIndex={isLabelVisible ? 1000 : labelZIndex}
          onMouseEnter={() =>
            labelVisibility === 'hover' && isLabelVisible && setIsLabelVisible(true)
          }
          onMouseLeave={() => labelVisibility === 'hover' && setIsLabelVisible(false)}
        >
          <div
            inert
            className={cn(
              'mb-1.5 relative max-w-80 rounded-md bg-black px-1.5 py-1 text-xs break-words text-white',
              'outline outline-white/16 -outline-offset-1',
              isLabelVisible
                ? 'opacity-100 translate-y-0 pointer-events-auto'
                : 'opacity-0 translate-y-0.5 pointer-events-none',
              'motion-safe:transition-all motion-safe:duration-300'
            )}
          >
            {label}
            <svg
              className="absolute top-full left-1/2 -translate-x-1/2 text-black"
              width="32"
              height="6"
              viewBox="0 0 32 6"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M18.5456 3.45441C17.485 4.515 16.9547 5.04529 16.3129 5.14695C16.1056 5.17978 15.8944 5.17978 15.6871 5.14695C15.0453 5.04529 14.515 4.515 13.4544 3.45442L10 0L22 0L18.5456 3.45441Z"
                fill="currentColor"
              />
            </svg>
          </div>
        </AdvancedMarker>
      )}
    </>
  );
};
