'use client';

import {
  Avatar,
  AvatarFallback,
  AvatarImage,
  Button,
  cn,
  Dialog,
  DialogClose,
  DialogContent,
  DialogTitle,
  DialogTrigger,
  Dropdown,
  DropdownItem,
  DropdownItems,
  DropdownTrigger,
  Field,
  Input,
  Label,
} from '@allo/ui';
import { LogOutIcon, ReceiptTextIcon, UserIcon, XIcon } from 'lucide-react';
import { use, useState, useTransition } from 'react';
import { IconButton } from '~/components/icon-button';
import { Link } from '~/i18n/navigation';
import { fetchUser } from '~/lib/mock-data/api';
import { RestaurantContext } from '~/lib/restaurant';
import { useSession } from '~/lib/session';
import { sleep } from '~/lib/utils/async';

export const SessionButton = () => {
  const { user } = useSession();

  return user ? <Authenticated /> : <Unauthenticated />;
};

const Authenticated = () => {
  const { user, logout } = useSession();
  const restaurant = use(RestaurantContext);

  return (
    <Dropdown>
      <DropdownTrigger asChild>
        <button
          className={cn(
            'flex items-center gap-2 h-10 p-3 outline-none ring-focus focus-visible:ring-4 rounded-sm',
            'hover:text-foreground-secondary motion-safe:transition-colors'
          )}
        >
          <span className="text-sm max-md:hidden">{user!.name}</span>
          <Avatar size="xs">
            {user!.avatar && <AvatarImage src={user!.avatar} />}
            <AvatarFallback>{user!.name.slice(0, 2)}</AvatarFallback>
          </Avatar>
        </button>
      </DropdownTrigger>
      <DropdownItems className="w-40">
        {restaurant && (
          <DropdownItem asChild>
            <Link href={`/restaurant/${restaurant.slug}/orders`}>
              <ReceiptTextIcon />
              Orders
            </Link>
          </DropdownItem>
        )}
        <DropdownItem className="text-negative" onClick={logout}>
          <LogOutIcon />
          Logout
        </DropdownItem>
      </DropdownItems>
    </Dropdown>
  );
};

export const Unauthenticated = () => {
  const { login } = useSession();
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [isPending, startTransition] = useTransition();
  const [error, setError] = useState<string | null>(null);

  const handleLogin = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    const formData = new FormData(event.target as HTMLFormElement);
    const email = formData.get('email') as string;
    const password = formData.get('password') as string;

    startTransition(async () => {
      try {
        const user = await fetchUser(email, password);

        setIsDrawerOpen(false);

        // wait a bit so we don't lose the entire dialog's exit animation
        await sleep(50);
        login(user);
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Invalid email or password');
      }
    });
  };

  return (
    <Dialog
      open={isDrawerOpen}
      onOpenChange={(open) => setIsDrawerOpen(isPending ? true : open)}
    >
      <DialogTrigger asChild>
        <button className="h-full p-3 outline-none *:ring-focus focus-visible:*:ring-4">
          <Avatar size="xs">
            <UserIcon className="size-3 text-foreground-secondary" />
          </Avatar>
        </button>
      </DialogTrigger>
      <DialogContent>
        <div className="relative flex items-center justify-between pb-4 mb-4 after:absolute after:-inset-x-4 after:h-px after:bg-border after:bottom-0">
          <DialogTitle className="leading-none text-base pb-0">Login</DialogTitle>
          <DialogClose asChild>
            <IconButton>
              <XIcon />
            </IconButton>
          </DialogClose>
        </div>
        <form
          className="space-y-2"
          onSubmit={handleLogin}
          onChange={() => setError(null)}
          inert={isPending}
        >
          <Field className="space-y-1">
            <Label>Email</Label>
            <Input name="email" type="email" placeholder="Email" />
          </Field>
          <Field className="space-y-1">
            <Label>Password</Label>
            <Input name="password" type="password" placeholder="Password" />
          </Field>
          <Button
            type="submit"
            variant="accent"
            isLoading={isPending}
            className={cn('w-full mt-2', error && 'animate-shake')}
          >
            Login
          </Button>
          {error && (
            <div className="h-0 overflow-visible">
              <p className="text-negative py-3">{error}</p>
            </div>
          )}
        </form>
      </DialogContent>
    </Dialog>
  );
};
