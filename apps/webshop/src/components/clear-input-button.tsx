import { cn } from '@allo/ui';
import { XIcon } from 'lucide-react';

export const ClearInputButton = ({
  className,
  ...props
}: Omit<React.ComponentPropsWithoutRef<'button'>, 'children'>) => {
  return (
    <button
      className={cn(
        'relative size-3 shrink-0 flex items-center justify-center rounded-full bg-foreground-tertiary after:absolute after:-inset-2',
        'hover:scale-105 active:scale-100 motion-safe:transition-transform',
        className
      )}
      {...props}
    >
      <XIcon className="size-2 stroke-3 text-background-high" />
    </button>
  );
};
