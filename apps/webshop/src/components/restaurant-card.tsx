'use client';

import { Bad<PERSON>, Card, cn } from '@allo/ui';
import { ClockIcon, MapPinIcon, MoonIcon } from 'lucide-react';
import Image from 'next/image';
import { ComponentProps } from 'react';
import { Property } from '~/components/property';
import { Link } from '~/i18n/navigation';
import { WebshopBranchRestaurant } from '~/lib/api/types.ts';
import { getRestaurantOpenStatus, isRestaurantOpen } from '~/lib/utils/dates.ts';

interface RestaurantCardProps extends ComponentProps<typeof Card> {
  webshopBranchRestaurant: WebshopBranchRestaurant;
}

export const RestaurantCard = ({
  className,
  webshopBranchRestaurant,
  ...props
}: RestaurantCardProps) => {
  const {
    slug,
    name,
    address,
    covers: images,
    openingHours,
    hasDelivery,
    hasPickup,
  } = webshopBranchRestaurant;
  const isOpen = isRestaurantOpen(openingHours);
  const openStatus = getRestaurantOpenStatus(openingHours);

  return (
    <Card depth asChild className={cn('p-2', className)} {...props}>
      <Link href={`/restaurant/${slug}`}>
        <div className="p-2 overflow-hidden">
          <div className="flex gap-2.5">
            <figure className="relative shrink-0 overflow-hidden size-12 rounded-md border border-border-soft bg-background">
              <Image
                src="/imgs/king-loui.png"
                alt="Restaurant Image"
                fill
                className="object-contain"
                sizes="200px"
              />
            </figure>
            <div className="grow space-y-2">
              <h2 className="text-base leading-none truncate">{name}</h2>
              <div className=" flex items-center gap-1 flex-wrap">
                <Badge size="sm" variant={isOpen ? 'positive' : 'negative'}>
                  {isOpen ? 'Open' : 'Closed'}
                </Badge>
                {hasDelivery && <Badge size="sm">Delivery</Badge>}
                {hasPickup && <Badge size="sm">Pickup</Badge>}
              </div>
            </div>
          </div>
          <div className="mt-4 space-y-1">
            <Property>
              <MapPinIcon />
              <span>{address}</span>
            </Property>
            <Property>
              <ClockIcon />
              <span>{openStatus}</span>
            </Property>
          </div>
        </div>
        <div className="mt-2 relative flex gap-px w-full h-30 rounded-lg overflow-hidden">
          {images &&
            images.slice(0, 3).map((image) => (
              <div className="relative grow" key={image.alt}>
                <Image
                  src={image.src || ''}
                  alt={image.alt || 'Restaurant Image'}
                  fill
                  className="object-cover"
                  sizes="600px"
                />
              </div>
            ))}
          {!isOpen && (
            <div className="absolute inset-0 bg-black/48 flex gap-1 items-center justify-center text-white">
              <MoonIcon className="size-3.5" />
              <span className="text-sm leading-none">Closed</span>
            </div>
          )}
        </div>
      </Link>
    </Card>
  );
};
