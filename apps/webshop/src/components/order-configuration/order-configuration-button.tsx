'use client';

import { Button } from '@allo/ui';
import { ChevronDownIcon } from 'lucide-react';
import { useOrderConfiguration } from '~/lib/order/store';
import { formatScheduledTime } from '~/lib/utils/dates';
import { useOrderConfigurationDialog } from './order-configuration-dialog';

export const OrderConfigurationButton = () => {
  const { type, address, time } = useOrderConfiguration();
  const { open } = useOrderConfigurationDialog();

  return (
    <Button onClick={open}>
      <div className="flex items-center gap-1 max-md:hidden">
        <span>{type === 'delivery' ? 'Delivery' : 'Pick up'}</span>
        <span>·</span>
        <span>{time.type === 'standard' ? 'Now' : formatScheduledTime(time.date)}</span>
        {type === 'delivery' && (
          <>
            <span>·</span>
            {address ? (
              <span className="max-w-[20ch] truncate" title={address?.formattedAddress}>
                {address?.formattedAddress}
              </span>
            ) : (
              <span className="text-foreground-secondary">No address added yet</span>
            )}
          </>
        )}
      </div>
      <div className="md:hidden">Modify Order</div>
      <ChevronDownIcon className="text-foreground-secondary" />
    </Button>
  );
};
