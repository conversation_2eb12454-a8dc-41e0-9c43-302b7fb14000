import {
  <PERSON><PERSON>,
  Card,
  Label,
  Listbox,
  ListboxEmpty,
  ListboxOption,
  ListboxOptions,
  ListboxTrigger,
  Radio,
  RadioGroup,
  Skeleton,
  Spinner,
} from '@allo/ui';
import { TZDate } from '@date-fns/tz';
import {
  add,
  format,
  isAfter,
  isFuture,
  isSameDay,
  isSameHour,
  isSameMinute,
  isToday,
  isTomorrow,
} from 'date-fns';
import { CalendarIcon, ClockAlertIcon, ClockIcon, ZapIcon } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import useSWR from 'swr';

import { fetchDeliverySlots, getCurrentDeliveryWindow } from '~/lib/mock-data/api';
import { OrderTime, OrderTimeType } from '~/lib/order/types';

interface TimeSelectionProps {
  value: OrderTime | null;
  onChange: (value: OrderTime | null) => void;
}

export const TimeSelection = ({ value, onChange }: TimeSelectionProps) => {
  // use a new state here instead of directly using the value
  // because we need to reset the time from time to time (e.g.: when changing date or hour)
  // and we don't want to trigger a `type` change
  const [type, setType] = useState<OrderTime['type']>(value?.type || 'standard');

  return (
    <div className="space-y-4">
      <RadioGroup className="flex gap-2" defaultValue={'standard'}>
        {(['standard', 'schedule'] as OrderTimeType[]).map((option) => (
          <Card
            key={option}
            depth
            className="text-sm px-3 py-2 flex flex-1 rounded-xl items-center justify-between gap-2"
            asChild
          >
            <label>
              {option === 'standard' && (
                <ZapIcon className="text-foreground-secondary size-3.5" />
              )}
              {option === 'schedule' && (
                <CalendarIcon className="text-foreground-secondary size-3.5" />
              )}
              <span className="capitalize mr-auto">{option}</span>
              <Radio
                id={option}
                name="order-time"
                checked={type === option}
                onChange={() => {
                  setType(option);
                  onChange(option === 'standard' ? { type: 'standard' } : null);
                }}
              />
            </label>
          </Card>
        ))}
      </RadioGroup>
      {type === 'standard' && <StandardTimeWarning />}
      {type === 'schedule' && <ScheduleTimeSelection value={value} onChange={onChange} />}
    </div>
  );
};

export const StandardTimeWarning = () => {
  const { data, error, isLoading } = useSWR(['getCurrentDeliveryWindow'], async () => {
    return await getCurrentDeliveryWindow();
  });

  if (isLoading) {
    return <Skeleton className="inline-block align-middle w-3/4 h-[1.1em]" />;
  }

  return (
    <p>
      {data && (
        <span>
          Earliest possible arrival time is around {data[0]} to {data[1]}min
        </span>
      )}
      {error && (
        <span className="text-foreground-secondary">Unable to load delivery window</span>
      )}
    </p>
  );
};

const TIMEZONE = 'Europe/Berlin'; // TODO: timezone from restaurant

type ScheduleTimeSelectionProps = Pick<TimeSelectionProps, 'value' | 'onChange'>;

export const ScheduleTimeSelection = ({
  value,
  onChange,
}: ScheduleTimeSelectionProps) => {
  const dayOptions = useMemo(() => {
    return Array.from({ length: 7 }, (_, i) => add(TZDate.tz(TIMEZONE), { days: i }));
  }, []);
  const [day, setDay] = useState<Date>(
    value?.type === 'schedule'
      ? (dayOptions.find((day) => isSameDay(day, value.date)) ?? dayOptions[0]!)
      : dayOptions[0]!
  );

  const { data: slots = [], isLoading } = useSWR(
    ['getCurrentDeliveryWindow', day],
    async () => {
      const slots = await fetchDeliverySlots(day);

      return slots.map((slot) => new TZDate(slot, TIMEZONE));
    }
  );

  const fullHours = useMemo(
    () => slots.filter((date) => isFuture(date) && date.getMinutes() === 0),
    [slots]
  );
  const [hour, setHour] = useState<Date | null>(
    value?.type === 'schedule'
      ? (fullHours.find((hour) => isSameHour(hour, value.date)) ?? null)
      : null
  );

  // auto-select the best hour
  useEffect(() => {
    if (hour || !fullHours.length) return;

    const nextHour = fullHours.find((date) => {
      const oneHourFromNow = add(TZDate.tz(TIMEZONE), { hours: 1 }).getHours();
      const dateHour = date.getHours();
      return dateHour >= oneHourFromNow;
    });

    setHour(nextHour ?? fullHours[0]!);
  }, [fullHours, hour]);

  const availableSlots = useMemo(() => {
    return hour ? slots.filter((slot) => isAfter(slot, hour)) : [];
  }, [slots, hour]);

  const formatWeekday = (day: Date) => {
    if (isToday(day)) return 'Today';

    if (isTomorrow(day)) return 'Tomorrow';

    return format(day, 'cccc');
  };

  return (
    <div className="space-y-3">
      <Label>Select day and time</Label>
      <div className="grid grid-cols-2 gap-2">
        <Listbox
          value={day}
          onChange={(day) => {
            setDay(day);
            setHour(null);
            onChange(null);
          }}
        >
          <ListboxTrigger>
            <CalendarIcon className="shrink-0 text-foreground-secondary" />
            <span className="leading-none capitalize truncate">{formatWeekday(day)}</span>
          </ListboxTrigger>
          <ListboxOptions>
            {dayOptions.map((day) => (
              <ListboxOption key={day.toISOString()} value={day}>
                <div className="text-sm leading-tight">
                  <p>{formatWeekday(day)}</p>
                  <p className="text-foreground-tertiary">{format(day, 'e MMM')}</p>
                </div>
              </ListboxOption>
            ))}
          </ListboxOptions>
        </Listbox>
        <Listbox
          value={hour}
          onChange={(hour) => {
            setHour(hour);
            onChange(null);
          }}
        >
          <ListboxTrigger id="time-listbox">
            <span className="text-foreground-secondary inline-flex size-4 items-center justify-center">
              {isLoading ? <Spinner size="sm" /> : <ClockIcon />}
            </span>
            <span>{hour ? format(hour, 'HH:mm') : 'Select'}</span>
          </ListboxTrigger>
          <ListboxOptions>
            {fullHours.map((hour) => (
              <ListboxOption key={hour.toISOString()} value={hour} className="capitalize">
                {format(hour, 'HH:mm')}
              </ListboxOption>
            ))}
            {fullHours.length === 0 && (
              <ListboxEmpty className="text-sm">No options</ListboxEmpty>
            )}
          </ListboxOptions>
        </Listbox>
      </div>
      {isLoading ? (
        <div className="grid grid-cols-3 gap-2">
          {new Array(6).fill(null).map((_, i) => (
            <Skeleton key={i} />
          ))}
        </div>
      ) : (
        <>
          {availableSlots.length > 0 ? (
            <div className="grid grid-cols-3 gap-2">
              {availableSlots.slice(0, 6).map((slot, index) => (
                <Button
                  key={index}
                  onClick={() => onChange({ type: 'schedule', date: slot })}
                  variant={
                    value?.type === 'schedule' && isSameMinute(value.date, slot)
                      ? 'accent'
                      : 'primary'
                  }
                >
                  {format(slot, 'HH:mm')}
                </Button>
              ))}
            </div>
          ) : (
            <p>
              <ClockAlertIcon className="size-4 inline-block mr-1.5 text-foreground-secondary mb-0.5" />
              <span className="text-foreground-secondary">
                No slots available for this day and time
              </span>
            </p>
          )}
        </>
      )}
    </div>
  );
};
