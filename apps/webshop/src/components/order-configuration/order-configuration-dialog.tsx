'use client';

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  cn,
  <PERSON><PERSON>,
  Di<PERSON><PERSON>ontent,
  Di<PERSON><PERSON>rigger,
  Drawer,
  DrawerContent,
} from '@allo/ui';
import {
  BanIcon,
  CalendarIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  MapIcon,
  StoreIcon,
} from 'lucide-react';
import posthog from 'posthog-js';
import {
  ComponentPropsWithoutRef,
  ComponentPropsWithRef,
  createContext,
  ReactNode,
  use,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { InlineButton } from '~/components/inline-button';
import { Property } from '~/components/property';
import { SegmentedControl, SegmentedControlItem } from '~/components/segmented-control';
import { Link } from '~/i18n/link.ts';
import { useTailwindBreakpoint } from '~/lib/hooks/use-tailwind-breakpoint';
import { useOrderConfiguration } from '~/lib/order/store';
import { OrderAddress, OrderTime, OrderType } from '~/lib/order/types';
import { isValidOrderConfiguration } from '~/lib/order/utils';
import { useRestaurant } from '~/lib/restaurant';
import { formatScheduledTime, isRestaurantOpen } from '~/lib/utils/dates';
import { getCaptureId } from '~/lib/utils/posthog';
import { AddressSelection } from './address-selection';
import { TimeSelection } from './time-selection';

type View = 'index' | 'time' | 'address';

interface OrderConfigurationDialogContextType {
  open: () => void;
  close: () => void;
}

const OrderConfigurationDialogContext =
  createContext<OrderConfigurationDialogContextType>({
    close: () => {},
    open: () => {},
  });

export const useOrderConfigurationDialog = () => {
  const ctx = use(OrderConfigurationDialogContext);

  if (!ctx) {
    throw new Error(
      'useOrderConfigurationDialog must be used within a OrderConfigurationDialogProvider'
    );
  }
  return ctx;
};

interface OrderConfigurationDialogProviderProps {
  children: ReactNode;
  onOrderTypeChange?: (type: OrderType) => void;
}

export const OrderConfigurationDialogProvider = ({
  children,
  onOrderTypeChange,
}: OrderConfigurationDialogProviderProps) => {
  const isDesktop = useTailwindBreakpoint('sm');

  const [view, setView] = useState<View>('index');
  const [isOpen, setIsOpen] = useState(false);

  const open = useCallback(() => setIsOpen(true), []);
  const close = useCallback(() => setIsOpen(false), []);

  const context = useMemo(
    () => ({ open, close, view, setView }),
    [open, close, view, setView]
  );

  return (
    <OrderConfigurationDialogContext value={context}>
      {children}
      {isDesktop ? (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          {/* TEMP: hacky way to circumvent a dialog bug (it doesn't show the mount animation without a trigger) */}
          <DialogTrigger className="hidden sr-only" aria-hidden />
          <DialogContent className="max-w-85 text-sm mb-auto">
            <OrderConfigurationDialogContent onOrderTypeChange={onOrderTypeChange} />
          </DialogContent>
        </Dialog>
      ) : (
        <Drawer open={isOpen} onOpenChange={setIsOpen}>
          <DrawerContent className="p-4 text-sm h-full bg-background-highlight !max-h-[90dvh]">
            <OrderConfigurationDialogContent onOrderTypeChange={onOrderTypeChange} />
          </DrawerContent>
        </Drawer>
      )}
    </OrderConfigurationDialogContext>
  );
};

export const OrderConfigurationDialogContent = ({
  onOrderTypeChange,
}: {
  onOrderTypeChange?: (type: OrderType) => void;
}) => {
  const { close } = use(OrderConfigurationDialogContext);
  const config = useOrderConfiguration();
  const restaurant = useRestaurant();
  debugger
  const [view, setView] = useState<View>('index');
  const [type, setType] = useState<OrderType>(
    restaurant.hasDelivery ? 'DELIVERY' : 'PICKUP'
  );
  const [time, setTime] = useState<OrderTime>(config.time);
  const [address, setAddress] = useState<OrderAddress | null>(config.address ?? null);

  useEffect(() => {
    if (onOrderTypeChange) {
      onOrderTypeChange(type);
    }
  }, [type, onOrderTypeChange]);

  const isValidConfig = useMemo(() => {
    return isValidOrderConfiguration({ type, time, address });
  }, [type, address, time]);

  const handleConfirm = () => {
    if (!isValidConfig) return;

    posthog.capture(getCaptureId('order_configuration_dialog', 'confirm', 'click'), {
      type,
      time,
      address,
    });

    config.set({ type, time, address });
    close();
  };

  return (
    <>
      {view === 'index' && (
        <>
          <RestaurantBanner />

          {/* order type */}
          <SegmentedControl
            className="mb-2"
            defaultValue={type}
            onChange={(value) => setType(value as OrderType)}
          >
            <SegmentedControlItem value="DELIVERY" disabled={!restaurant.hasDelivery}>
              Delivery
              {!restaurant.hasDelivery && (
                <Badge size="xs">
                  <BanIcon />
                </Badge>
              )}
            </SegmentedControlItem>
            <SegmentedControlItem value="PICKUP" disabled={!restaurant.hasPickup}>
              Pickup
              {!restaurant.hasPickup && (
                <Badge size="xs">
                  <BanIcon />
                </Badge>
              )}
            </SegmentedControlItem>
          </SegmentedControl>

          {/* time */}
          <ConfigurationPropertyButton onClick={() => setView('time')}>
            <Property>
              <CalendarIcon className="size-3.5" />
              {time.type === 'standard' ? 'Now' : formatScheduledTime(time.date)}
            </Property>
            <Property>
              Schedule
              <ChevronRightIcon />
            </Property>
          </ConfigurationPropertyButton>

          {/* address */}
          {type === 'DELIVERY' && (
            <ConfigurationPropertyButton onClick={() => setView('address')}>
              <Property>
                <MapIcon className="size-3.5" />
                <span className="truncate max-w-44">
                  {address ? address.formattedAddress : 'No address added yet'}
                </span>
              </Property>
              <Property>
                Addresses
                <ChevronRightIcon />
              </Property>
            </ConfigurationPropertyButton>
          )}
          <Footer>
            <Button onClick={close}>Cancel</Button>
            <Button variant="accent" onClick={handleConfirm} disabled={!isValidConfig}>
              Confirm
            </Button>
          </Footer>
        </>
      )}
      {view === 'time' && (
        <TimeSection
          type={type}
          initialValue={time}
          onConfirm={setTime}
          setView={setView}
        />
      )}
      {view === 'address' && (
        <AddressSection initialValue={address} onConfirm={setAddress} setView={setView} />
      )}
    </>
  );
};

const RestaurantBanner = () => {
  const { name, openingHours, branchRestaurants = [], slug } = useRestaurant();
  const isOpen = isRestaurantOpen(openingHours);

  return (
    <Header className="flex flex-wrap gap-1.5 justify-start leading-none mb-4 *:relative text-sm before:bg-background">
      {!isOpen && (
        <div className="w-full">
          <Badge variant="negative" size="xs" className="mb-0.5">
            Closed
          </Badge>
        </div>
      )}
      <Property>
        <StoreIcon />
        <span>{name}</span>
      </Property>
      {branchRestaurants.length > 0 && (
        <InlineButton variant="accent" asChild>
          <Link href={`/locations/${slug}`}>Change store</Link>
        </InlineButton>
      )}
    </Header>
  );
};

interface TimeSectionProps {
  type: OrderType;
  initialValue: OrderTime | null;
  onConfirm: (time: OrderTime) => void;
  setView: (view: View) => void;
}

const TimeSection = ({ type, initialValue, onConfirm, setView }: TimeSectionProps) => {
  const [time, setTime] = useState(initialValue);

  const handleConfirm = () => {
    if (!time) return;

    onConfirm(time);
    setView('index');
  };

  return (
    <>
      <Header>
        <InlineButton onClick={() => setView('index')} className="py-1">
          <ChevronLeftIcon />
          When to {type === 'DELIVERY' ? 'deliver' : 'pick up'}
        </InlineButton>
      </Header>
      <TimeSelection value={time} onChange={setTime} />
      <Footer>
        <Button onClick={handleConfirm} disabled={!time}>
          Done
        </Button>
      </Footer>
    </>
  );
};

interface AddressSectionProps {
  initialValue: OrderAddress | null;
  onConfirm: (address: OrderAddress) => void;
  setView: (view: View) => void;
}

const AddressSection = ({ initialValue, onConfirm, setView }: AddressSectionProps) => {
  return (
    <>
      <Header>
        <InlineButton onClick={() => setView('index')} className="py-1">
          <ChevronLeftIcon />
          Address
        </InlineButton>
      </Header>
      <AddressSelection
        initialValue={initialValue}
        onConfirm={(address) => {
          if (!address) return;
          onConfirm(address);
          setView('index');
        }}
        renderActions={({
          onConfirm,
          isValid,
          onDetailsConfirm,
          onDetailsCancel,
          isDetailsValid,
        }) => {
          return (
            <Footer>
              {onConfirm && (
                <Button onClick={onConfirm} disabled={!isValid}>
                  Done
                </Button>
              )}
              {onDetailsCancel && <Button onClick={onDetailsCancel}>Cancel</Button>}
              {onDetailsConfirm && (
                <Button
                  variant="accent"
                  onClick={onDetailsConfirm}
                  disabled={!isDetailsValid}
                >
                  Confirm
                </Button>
              )}
            </Footer>
          );
        }}
      />
    </>
  );
};

// layout utils

const Header = ({
  children,
  className,
  ...props
}: ComponentPropsWithoutRef<'header'>) => {
  return (
    <header
      className={cn(
        'relative flex justify-between gap-2 pb-4 mb-4',
        'max-md:sticky max-md:top-0 max-md:z-20',
        'before:absolute before:-inset-x-4 before:bottom-0 before:-top-4 before:bg-background-highlight before:border-b before:border-border-soft ',
        className
      )}
      {...props}
    >
      {children}
    </header>
  );
};

const Footer = ({ children, className, ...props }: ComponentPropsWithoutRef<'div'>) => {
  return (
    <div
      className={cn(
        'relative md:mt-2 flex gap-2 justify-end pt-4 z-20',
        'max-md:sticky w-full max-md:bottom-0 max-md:mt-auto max-md:pb-6',
        'before:absolute before:-inset-x-4 before:top-0 before:-bottom-4 before:bg-background-highlight before:border-t before:border-border-soft ',
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

const ConfigurationPropertyButton = ({
  children,
  className,
  ...props
}: ComponentPropsWithRef<'button'>) => {
  return (
    <button
      className={cn(
        'px-2 flex justify-between w-full gap-2 h-10 items-center rounded-lg',
        'hover:bg-background motion-safe:transition-colors',
        className
      )}
      {...props}
    >
      {children}
    </button>
  );
};
