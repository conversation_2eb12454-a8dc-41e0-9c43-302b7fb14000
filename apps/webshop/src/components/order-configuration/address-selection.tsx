'use client';

import {
  Badge,
  cn,
  <PERSON>,
  FieldError,
  Input,
  InputGroup,
  InputPrefix,
  InputSuffix,
  Label,
  Radio,
  RadioGroup,
  Spinner,
  Textarea,
} from '@allo/ui';
import { LocateFixedIcon, MapPinIcon } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState, useTransition } from 'react';
import useSWR from 'swr';
import { ClearInputButton } from '~/components/clear-input-button';
import { InlineButton } from '~/components/inline-button';
import { Property } from '~/components/property';
import { useDebouncedValue } from '~/lib/hooks/use-debounced-value';
import { fetchPlaceFromCoordinates, fetchPlacesByQuery } from '~/lib/mock-data/api';
import { Place } from '~/lib/mock-data/places';
import { OrderAddress } from '~/lib/order/types';
import { areSameOrderAddress } from '~/lib/order/utils';
import { useRestaurant } from '~/lib/restaurant';
import { useSession } from '~/lib/session';
import { haversine } from '~/lib/utils/math';

type AddressSelectionActionsProps = {
  onConfirm?: () => void;
  isValid?: boolean;
  onDetailsConfirm?: () => void;
  onDetailsCancel?: () => void;
  isDetailsValid?: boolean;
};

type AddressActionsComponent = (props: AddressSelectionActionsProps) => React.ReactNode;

interface AddressSelectionProps {
  initialValue?: OrderAddress | null;
  onConfirm: (value: OrderAddress) => void;
  renderActions: AddressActionsComponent;
}

export const AddressSelection = ({
  initialValue,
  onConfirm,
  renderActions,
}: AddressSelectionProps) => {
  const placeValidation = usePlaceValidation();
  const { user } = useSession();

  const [value, setValue] = useState<OrderAddress | null>(initialValue ?? null);
  const [presets, setPresets] = useState<OrderAddress[]>([
    ...(value ? [value] : []),
    ...(user?.addresses ?? []),
  ]);

  const [detailsForm, setDetailsForm] = useState<{
    initialValue: OrderAddressDetails | null;
    onConfirm: (details: OrderAddressDetails) => void;
  } | null>(null);

  const handleSelectPlace = (place: Place) => {
    debugger
    setDetailsForm({
      initialValue: null,
      onConfirm: (details) => {
        const newOrderAddress = {
          ...value,
          ...details,
          coordinates: place.coordinates,
          formattedAddress: place.formattedAddress,
        };

        setValue(newOrderAddress);
        setPresets((prev) => [newOrderAddress, ...prev]);
        onConfirm(newOrderAddress);
      },
    });
  };

  const handleEditOption = (option: OrderAddress) => {
    const index = presets.findIndex((o) => areSameOrderAddress(o, option));

    setDetailsForm({
      initialValue: {
        additionalAddressInfo: option.additionalAddressInfo,
        apartment: option.apartment,
        instructions: option.instructions,
      },
      onConfirm: (details) => {
        const newOrderAddress = {
          ...option,
          ...details,
        };

        setValue(newOrderAddress);
        setPresets((prev) => [newOrderAddress, ...prev.filter((_, i) => i !== index)]);
        onConfirm(newOrderAddress);

        // FUTURE: here we should commit these changes to the user's saved addresses in the backend
      },
    });
  };

  return (
    <>
      {!detailsForm && (
        <>
          <PlaceSelection
            onSelect={handleSelectPlace}
            //validation={placeValidation}
            className="has-[[data-place-results]]:[&+*]:hidden" // hide address when there are results
          />
          {presets.length > 0 && (
            <div className="mt-4">
              <p className="text-sm text-foreground-secondary">Address</p>
              <RadioGroup>
                {presets.map((address, index) => {
                  const isValid = !placeValidation(address);

                  return (
                    <AddressPreset
                      key={index}
                      address={address}
                      isValid={isValid}
                      onSelect={setValue}
                      onEdit={handleEditOption}
                      initialSelected={
                        !!(isValid && value && areSameOrderAddress(address, value))
                      }
                    />
                  );
                })}
              </RadioGroup>
            </div>
          )}
          {renderActions({
            onConfirm: () => value && onConfirm(value),
            isValid: !!value,
          })}
        </>
      )}
      {detailsForm && (
        <AddressDetails
          initialValue={detailsForm.initialValue}
          renderActions={renderActions}
          onCancel={() => setDetailsForm(null)}
          onConfirm={(details) => {
            detailsForm.onConfirm(details);
            setDetailsForm(null);
          }}
        />
      )}
    </>
  );
};

interface AddressPresetProps {
  address: OrderAddress;
  isValid: boolean;
  onSelect: (address: OrderAddress) => void;
  onEdit: (address: OrderAddress) => void;
  initialSelected: boolean;
}

const AddressPreset = ({
  address,
  isValid,
  onSelect,
  onEdit,
  initialSelected,
}: AddressPresetProps) => {
  return (
    <label
      className={cn(
        'flex flex-row-reverse gap-3 px-2 py-3 rounded-md mt-1 w-full',
        'has-[input:not(:checked)]:hover:bg-background has-[input:not(:checked)]:hover:cursor-pointer',
        'motion-safe:transition-colors',
        !isValid && 'pointer-events-none opacity-70'
      )}
      inert={!isValid}
    >
      <Radio
        name="address"
        value={address.formattedAddress}
        defaultChecked={initialSelected}
        onChange={(e) => e.target.checked && onSelect(address)}
        disabled={!isValid}
      />
      <div className="leading-none grow">
        <Property>
          <MapPinIcon />
          <span>{address.formattedAddress}</span>
        </Property>
        {isValid ? (
          <InlineButton
            type="button"
            variant="accent"
            className="relative ml-4.5 w-fit"
            onClick={() => onEdit(address)}
          >
            Edit
          </InlineButton>
        ) : (
          <Badge size="sm" className="mt-2">
            Too far away
          </Badge>
        )}
      </div>
    </label>
  );
};

type OrderAddressDetails = Omit<OrderAddress, 'coordinates' | 'formattedAddress'>;

interface AddressDetailsProps {
  initialValue: OrderAddressDetails | null;
  onCancel: () => void;
  onConfirm: (value: OrderAddressDetails) => void;
  renderActions: AddressActionsComponent;
}

const AddressDetails = ({
  initialValue,
  onCancel,
  onConfirm,
  renderActions,
}: AddressDetailsProps) => {
  const [internalValue, setInternalValue] = useState<OrderAddressDetails>({
    additionalAddressInfo: '',
    apartment: '',
    instructions: '',
    ...(initialValue || {}),
  });

  const handleChange = (patch: Partial<OrderAddressDetails>) => {
    const newInternalValue = { ...internalValue, ...patch };
    setInternalValue(newInternalValue);
  };

  const isValid = useMemo(
    () => internalValue.additionalAddressInfo.length > 0,
    [internalValue]
  );

  return (
    <>
      <div className="space-y-4 *:space-y-2 mb-4">
        <Field>
          <Label required>Additional address information</Label>
          <Input
            placeholder="e.g. Street Address, building name"
            value={internalValue?.additionalAddressInfo || ''}
            onChange={(e) => handleChange({ additionalAddressInfo: e.target.value })}
          />
        </Field>
        <Field>
          <Label>Apt / Suite / Floor</Label>
          <Input
            placeholder="e.g. 123"
            value={internalValue?.apartment || ''}
            onChange={(e) => handleChange({ apartment: e.target.value })}
          />
        </Field>
        <Field>
          <Label>Instructions</Label>
          <Textarea
            className="block"
            placeholder="e.g. Leave at the door"
            rows={5}
            value={internalValue?.instructions || ''}
            onChange={(e) => handleChange({ instructions: e.target.value })}
          />
        </Field>
      </div>
      {renderActions({
        onDetailsConfirm: () => onConfirm(internalValue),
        onDetailsCancel: onCancel,
        isDetailsValid: isValid,
      })}
    </>
  );
};

const usePlaceValidation = () => {

  const restaurant = useRestaurant();

  return useCallback(
    (place: Place) => {
      const distance = haversine(restaurant.address.coordinates, place.coordinates);

      if (distance > restaurant.deliveryRadiusKm) {
        return 'Too far away to deliver';
      }

      return null;
    },
    [restaurant.deliveryRadiusKm, restaurant.address.coordinates]
  );
};

interface PlaceSelectionProps {
  onSelect: (place: Place) => void;
  validation: (place: Place) => string | null;
  className?: string;
}

export const PlaceSelection = ({
  onSelect,
  validation,
  className,
}: PlaceSelectionProps) => {
  const [query, setQuery] = useState('');
  const debouncedQuery = useDebouncedValue(query, 200);
  const [results, setResults] = useState<Place[] | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoadingCurrentLocation, startLoadingCurrentLocation] = useTransition();

  const { data, isLoading } = useSWR(['places', debouncedQuery], async () => {
    if (!debouncedQuery || debouncedQuery.length < 1) return null;
    return await fetchPlacesByQuery(debouncedQuery);
  });

  useEffect(() => {
    if (!isLoading) setResults(data ?? null);
  }, [data, isLoading]);

  const handleSelectPlace = (place: Place) => {
    setResults(null);

    const error = validation(place);

    if (!error) {
      onSelect(place);
    } else {
      setError(error);
    }
  };

  const handleGetCurrentPlace = () => {
    startLoadingCurrentLocation(async () => {
      try {
        const position = await new Promise<GeolocationPosition>((resolve, reject) => {
          navigator.geolocation.getCurrentPosition(resolve, reject, {
            enableHighAccuracy: true,
            timeout: 3000,
            maximumAge: 0,
          });
        });

        const place = await fetchPlaceFromCoordinates({
          lat: position.coords.latitude,
          lng: position.coords.longitude,
        });

        if (!place) throw new Error('No place found');
        handleSelectPlace(place);
      } catch (error) {
        console.log(error);
        setError('Failed to get current location');
      }
    });
  };

  return (
    <div className={className} inert={isLoadingCurrentLocation}>
      <Field>
        <Label className="text-sm mb-2">Enter your address</Label>
        <InputGroup>
          <InputPrefix>
            <button
              className={cn(
                'text-xs outline-none focus-visible:ring-focus focus-visible:ring-4 rounded-sm after:-inset-1.5 after:absolute',
                error ? 'text-negative' : 'text-foreground-secondary'
              )}
              onClick={handleGetCurrentPlace}
              disabled={isLoading || isLoadingCurrentLocation}
            >
              {isLoading || isLoadingCurrentLocation ? (
                <Spinner className="size-3.5" />
              ) : (
                <LocateFixedIcon className="size-3.5 hover:text-foreground motion-safe:transition-colors" />
              )}
            </button>
          </InputPrefix>
          <Input
            autoFocus
            value={query}
            onChange={(e) => {
              setQuery(e.target.value);
              setError(null);
            }}
          />
          <InputSuffix interactive>
            <ClearInputButton
              className={cn(!query && 'opacity-0 pointer-events-none')}
              onClick={() => {
                setQuery('');
                setError(null);
                setResults(null);
              }}
            />
          </InputSuffix>
        </InputGroup>
        {error && <FieldError className="text-sm mt-2 text-negative">{error}</FieldError>}
      </Field>
      {!error && results && (
        <ul className="mt-2" data-place-results>
          {results.map((place, index) => (
            <li key={index}>
              <button
                className={cn(
                  'w-full text-left h-9.5 px-2 hover:bg-background rounded-sm motion-safe:transition-colors',
                  'outline-none ring-focus focus-visible:ring-4'
                )}
                onClick={() => handleSelectPlace(place)}
              >
                <Property>
                  <MapPinIcon className="size-3.5" />
                  <span>{place.formattedAddress}</span>
                </Property>
              </button>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};
