'use client';

import { cva, Slot } from '@allo/ui';

const inlineButtonStyle = cva({
  base: [
    'relative inline-flex w-fit items-center gap-1 text-sm text-foreground leading-none ring-focus outline-none focus-visible:ring-4 rounded-sm',
    'motion-safe:transition active:scale-98 cursor-pointer',
    'after:absolute after:-inset-1.5', // extend clickable area
    '[&_.lucide]:size-3.5 [&_.lucide]:stroke-[1.5px] [&_.lucide]:text-foreground-secondary',
    'disabled:opacity-60 disabled:pointer-events-none',
  ],
  variants: {
    variant: {
      primary: 'text-foreground hover:text-foreground/70',
      secondary: 'text-foreground-secondary hover:text-foreground',
      accent: 'text-accent hover:text-accent/80',
    },
  },
  defaultVariants: {
    variant: 'primary',
  },
});

interface InlineButtonProps extends React.ComponentPropsWithRef<'button'> {
  variant?: 'primary' | 'secondary' | 'accent';
  asChild?: boolean;
}

export const InlineButton = ({
  asChild,
  children,
  className,
  variant = 'primary',
  ...props
}: InlineButtonProps) => {
  const Component = asChild ? Slot : 'button';
  return (
    <Component className={inlineButtonStyle({ variant, className })} {...props}>
      {children}
    </Component>
  );
};
