'use client';

import { Card, cn } from '@allo/ui';
import { AnimatePresence, motion } from 'motion/react';
import { createContext, use, useId, useMemo, useState } from 'react';

type BoxContextType = {
  id: string;
  isCollapsible: boolean;
  expanded: boolean;
  setExpanded: (expanded: boolean) => void;
};

const BoxContext = createContext<BoxContextType | null>(null);

const useBoxContext = () => {
  const ctx = use(BoxContext);
  if (!ctx) {
    throw new Error('useBox must be used within a Box');
  }
  return ctx;
};

interface BoxProps extends React.ComponentProps<typeof Card> {
  collapsible?: boolean;
  initialExpanded?: boolean;
}

const Box = ({
  collapsible = false,
  initialExpanded = true,
  className,
  ...props
}: BoxProps) => {
  const id = useId();
  const [expanded, setExpanded] = useState(initialExpanded);

  const ctx = useMemo(
    () => ({ id, isCollapsible: collapsible, expanded, setExpanded }),
    [id, collapsible, expanded, setExpanded]
  );

  return (
    <BoxContext value={ctx}>
      <Card className={cn('overflow-hidden', className)} {...props} />
    </BoxContext>
  );
};

const BoxHeader = ({
  className,
  children,
  ...props
}: React.ComponentPropsWithRef<'header'>) => {
  const { id, isCollapsible, expanded, setExpanded } = useBoxContext();

  const collapsibleProps = useMemo(() => {
    if (!isCollapsible) return {};

    return {
      role: 'button',
      onClick: () => setExpanded(!expanded),
      onKeyDown: (e: React.KeyboardEvent<HTMLDivElement>) => {
        if (e.key === 'Enter' || e.key === ' ') {
          setExpanded(!expanded);
        }
      },
      'aria-expanded': expanded,
      'aria-controls': id,
      tabIndex: 0,
    };
  }, [isCollapsible, expanded, id, setExpanded]);

  return (
    <header
      className={cn(
        'relative flex items-center justify-between px-3 md:px-4 h-10 border-b border-border-soft -mb-px',
        isCollapsible && [
          'cursor-pointer select-none',
          'after:absolute after:h-full after:top-0 after:right-0 after:aspect-square after:motion-safe:transition-transform',
          'after:bg-[url(/imgs/chevron-down.svg)] after:bg-no-repeat after:[background-position:calc(100%-(--spacing(3)))_center]',
          expanded && 'after:rotate-180 after:origin-center',
        ],
        className
      )}
      {...collapsibleProps}
      {...props}
    >
      {children}
    </header>
  );
};

const BoxTitle = ({
  className,
  children,
  ...props
}: React.ComponentPropsWithRef<'h2'>) => {
  return (
    <h2
      className={cn('text-sm text-foreground-secondary leading-none', className)}
      {...props}
    >
      {children}
    </h2>
  );
};

const BoxContent = ({
  className,
  children,
  ...props
}: React.ComponentPropsWithRef<'div'>) => {
  const { id, isCollapsible, expanded } = useBoxContext();

  if (!isCollapsible) {
    return (
      <div className={cn('p-3 md:p-4', className)} {...props}>
        {children}
      </div>
    );
  }

  return (
    <AnimatePresence>
      <motion.div
        id={id}
        className="overflow-hidden"
        initial={{ height: expanded ? 'auto' : 0 }}
        animate={{ height: expanded ? 'auto' : 0 }}
        exit={{ height: 0 }}
        inert={!expanded}
      >
        <div className={cn('p-3 md:p-4', className)} {...props}>
          {children}
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

const BoxFooter = ({
  className,
  children,
  ...props
}: React.ComponentPropsWithRef<'footer'>) => {
  return (
    <footer
      className={cn('p-3 md:p-4 border-t border-border-soft', className)}
      {...props}
    >
      {children}
    </footer>
  );
};

export { Box, BoxContent, BoxFooter, BoxHeader, BoxTitle };
