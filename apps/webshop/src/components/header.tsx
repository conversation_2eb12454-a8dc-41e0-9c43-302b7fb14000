import { cn } from '@allo/ui';
import { ComponentPropsWithoutRef } from 'react';

export const Header = ({
  children,
  className,
  ...props
}: ComponentPropsWithoutRef<'header'>) => {
  return (
    <header
      data-fixed-header
      className={cn(
        'fixed top-0 w-full z-30 bg-background-high h-(--header-height) border-b border-border-soft px-(--px)',
        '[--px:calc(0.5*(100vw-var(--spacing-container))-(--spacing(3)))]',
        'grid grid-cols-[1fr_1fr_1fr] items-center',
        className
      )}
      {...props}
    >
      {children}
    </header>
  );
};

export const HeaderLogo = ({
  children,
  className,
  ...props
}: ComponentPropsWithoutRef<'figure'>) => {
  return (
    <figure
      className={cn('h-full flex items-center mr-auto px-3 *:h-6.5', className)}
      {...props}
    >
      {children}
    </figure>
  );
};

export const HeaderPrimaryAction = ({
  children,
  className,
  ...props
}: ComponentPropsWithoutRef<'div'>) => {
  return (
    <div className={cn('mx-auto', className)} {...props}>
      {children}
    </div>
  );
};

export const HeaderActions = ({
  children,
  className,
  ...props
}: ComponentPropsWithoutRef<'div'>) => {
  return (
    <div
      className={cn('col-start-3 ml-auto flex gap-1 items-center', className)}
      {...props}
    >
      {children}
    </div>
  );
};
