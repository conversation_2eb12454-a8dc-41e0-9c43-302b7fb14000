'use client';

import { cva } from '@allo/ui';

interface PinProps extends Omit<React.ComponentPropsWithoutRef<'div'>, 'children'> {
  size?: 'xs' | 'sm';
  variant?: 'accent' | 'secondary';
  pulse?: boolean;
}

const pinStyle = cva({
  base: [
    'relative rounded-full z-0',
    '*:z-1 *:absolute *:inset-(--inset) *:rounded-full *:bg-(--background)',
    'bg-(--border)',
  ],
  variants: {
    variant: {
      accent:
        '[--background:var(--color-accent)] [--border:--alpha(var(--color-accent)/20%)]',
      secondary:
        '[--background:var(--color-background-highlight)] [--border:var(--color-border)]',
    },
    size: {
      xs: 'size-3 [--inset:--spacing(0.5)]',
      sm: 'size-6 [--inset:--spacing(1.5)]',
    },
    pulse: {
      true: [
        'bg-transparent',
        'before:absolute before:inset-(--inset) before:rounded-full before:bg-(--border)',
        'after:absolute after:inset-(--inset) after:rounded-full after:bg-(--border)',
        'after:animate-scale-pulse before:animate-scale-pulse before:[animation-delay:0.4s]',
      ],
      false: '',
    },
    defaultVariants: {
      variant: 'accent',
      size: 'xs',
      pulse: false,
    },
  },
});

export const Pin = ({
  size = 'xs',
  variant = 'accent',
  pulse = false,
  className,
  ...props
}: PinProps) => {
  return (
    <div className={pinStyle({ size, variant, pulse, className })} {...props}>
      <div />
    </div>
  );
};
