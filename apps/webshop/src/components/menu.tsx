'use client';

import { cn, cva } from '@allo/ui';
import { SearchIcon } from 'lucide-react';
import posthog from 'posthog-js';
import { ComponentProps, useMemo, useState } from 'react';
import { AnchorNavigation, AnchorNavigationItem } from '~/components/anchor-navigation';
import { ClearInputButton } from '~/components/clear-input-button';
import { ProductCard } from '~/components/product-card';
import { WebshopMenu, WebshopMenuItem } from '~/lib/api/types.ts';
import { useDebouncedValue } from '~/lib/hooks/use-debounced-value';
import { useOrderCart } from '~/lib/order/store.ts';
import { getCaptureId } from '~/lib/utils/posthog';

const searchContainerStyle = cva({
  base: 'relative h-10 flex gap-1.5 items-center text-sm [&>.lucide]:size-3.5 [&>.lucide]:text-foreground-secondary *:shrink-0',
});

interface MenuProps {
  readOnly?: boolean;
  menus?: WebshopMenu[];
}

/**
 * Basically we are prefixing the id with `id-` to make it valid for an anchor
 * @param id
 */
const getValidSelector = (id: string) => {
  return `id-${id}`;
};

export const Menu = ({ readOnly = false, menus }: MenuProps) => {
  const [isSearching, setIsSearching] = useState(false);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const debouncedSearchQuery = useDebouncedValue(searchQuery, 256);
  const { filters, productsByFilter } = useMenuFilters(menus || []);
  const { hasCartId } = useOrderCart();
  const allProducts: WebshopMenuItem[] = useMemo(() => {
    if (!menus || !Array.isArray(menus)) {
      return [];
    }

    return menus.reduce((acc, menu) => {
      if (menu.items && Array.isArray(menu.items)) {
        acc.push(...menu.items);
      }
      return acc;
    }, [] as WebshopMenuItem[]);
  }, [menus]);

  const searchResults: WebshopMenuItem[] = useMemo(() => {
    if (!debouncedSearchQuery) return allProducts;

    const query = debouncedSearchQuery.toLowerCase();

    return allProducts.filter(
      (product) =>
        product.name?.toLowerCase().includes(query) ||
        product.description?.toLowerCase().includes(query)
    );
  }, [debouncedSearchQuery, allProducts]);

  if (allProducts.length === 0) return null;

  return (
    <div className="max-w-container mx-auto mb-5">
      <div
        className={cn(
          'sticky top-(--header-height) z-20 flex items-center gap-1.5 pt-4 bg-background-high border-b border-border-soft',
          'before:absolute before:-inset-x-4 before:bg-background-high before:inset-y-0 *:relative'
        )}
      >
        {isSearching ? (
          <div className={searchContainerStyle()}>
            <SearchIcon />
            <input
              className="outline-none w-full md:w-64"
              autoFocus
              placeholder="Search products..."
              value={searchQuery}
              onChange={(e) => {
                setSearchQuery(e.target.value);

                posthog.capture(getCaptureId('menu', 'search_input', 'change'), {
                  search_query: e.target.value,
                });
              }}
              onKeyDown={(e) => {
                if (e.key === 'Escape') {
                  setIsSearching(false);
                  setSearchQuery('');
                }
              }}
            />
            <ClearInputButton onClick={() => setIsSearching(false)} />
          </div>
        ) : (
          <div className="flex items-center w-full">
            <button
              className={searchContainerStyle({
                className:
                  'pr-4 input-focus-visible ring-inset hover:text-foreground-secondary motion-safe:transition-colors',
              })}
              onClick={() => setIsSearching(true)}
            >
              <SearchIcon />
              Search
            </button>
            {filters.length > 0 && (
              <AnchorNavigation
                className="grow -mb-px"
                defaultValue={filters[0] && `#${getValidSelector(filters[0].id || '')}`}
              >
                {filters.map((filter) => {
                  const key = getValidSelector(filter.id || '');

                  return (
                    <AnchorNavigationItem
                      key={key}
                      href={`#${key}`}
                      onClick={() => {
                        posthog.capture(getCaptureId('menu', 'filter_tab', 'click'), {
                          filter_name: filter,
                          filter_products: productsByFilter[filter.title],
                        });
                      }}
                    >
                      {filter.title}
                    </AnchorNavigationItem>
                  );
                })}
              </AnchorNavigation>
            )}
          </div>
        )}
      </div>
      {isSearching ? (
        <ProductSection
          title={`${searchResults.length} result${searchResults.length === 1 ? '' : 's'}`}
          products={searchResults}
          size="sm"
          readOnly={readOnly}
        />
      ) : (
        filters.map((filter, index) => {
          if (!filter.id) return null;
          const products = productsByFilter[filter.id];
          if (!products) return null;

          return (
            <ProductSection
              key={index}
              id={`${getValidSelector(filter.id || '')}`}
              title={filter.title}
              products={products}
              size={index === 0 ? 'md' : 'sm'}
              readOnly={readOnly}
              hasCartId={hasCartId}
            />
          );
        })
      )}
    </div>
  );
};

interface ProductSectionProps extends Omit<ComponentProps<'section'>, 'children'> {
  title: string;
  products: WebshopMenuItem[];
  hasCartId?: boolean;
  size: 'sm' | 'md';
  readOnly?: boolean;
}

const ProductSection = ({
  title,
  products,
  size,
  readOnly = false,
  hasCartId = true,
  ...props
}: ProductSectionProps) => {
  return (
    <section className="pt-10 pb-5 space-y-4 scroll-mt-10" {...props}>
      <h2 className="text-base">{title}</h2>
      {products.length > 0 ? (
        <ul
          className={cn(
            'grid gap-3',
            size === 'sm' && 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3',
            size === 'md' && [
              'flex max-md:*:w-64 *:shrink-0 md:grid md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5',
              'max-md:overflow-x-auto max-md:w-dvw max-md:-ml-4 max-md:px-4 max-md:pb-4',
              'max-md:snap-x max-md:snap-mandatory max-md:*:snap-center',
            ]
          )}
        >
          {products.map((product) => (
            <li key={product.id}>
              <ProductCard
                product={product}
                size={size}
                readOnly={readOnly || !hasCartId}
              />
            </li>
          ))}
        </ul>
      ) : (
        <p className="text-foreground-secondary">No products found</p>
      )}
    </section>
  );
};

const useMenuFilters = (menus: WebshopMenu[]) => {
  const filters = useMemo(
    () =>
      menus.reduce(
        (acc, menu) => {
          // Only add the title if it exists
          if (menu.title && !acc.some((item) => item.title === menu.title)) {
            acc.push({
              title: menu.title,
              id: menu.id,
            });
          }
          return acc;
        },
        [] as Array<{ title: string; id: string | undefined }>
      ),
    [menus]
  );

  const productsByFilter = useMemo(
    () =>
      menus.reduce(
        (acc, menu) => {
          // Only proceed if menu and title exist
          if (menu?.id) {
            const id = menu.id;

            // Initialize the array if it doesn't exist
            if (!acc[id]) {
              acc[id] = [];
            }

            // Now safely push items if they exist
            if (menu.items && Array.isArray(menu.items)) {
              acc[id].push(...menu.items);
            }
          }

          return acc;
        },
        {} as Record<string, WebshopMenuItem[]>
      ),
    [menus]
  );

  return { filters, productsByFilter };
};
