'use client';

import {
  Badge,
  cn,
  Price,
  ProductCard,
  ProductCardContent,
  ProductCardCover,
  ProductCardDescription,
  ProductCardFooter,
  ProductCardTitle,
  QuantitySelector,
  QuantitySelectorDecrease,
  QuantitySelectorDelete,
  QuantitySelectorIncrease,
  QuantitySelectorQuantity,
} from '@allo/ui';
import { useCallback, useMemo, useState } from 'react';
import ImageWithFallback from '~/components/ImageWithFallback/image-with-fallback';
import { useOrderConfigurationDialog } from '~/components/order-configuration/order-configuration-dialog';
import { ProductConfigurationDrawer } from '~/components/product-configuration/product-configuration-drawer';
import { WebshopMenuItem } from '~/lib/api/types.ts';
import { calculateProductQuantityInCart } from '~/lib/order/cart/utils';
import { useIsValidOrderConfiguration, useOrderCart } from '~/lib/order/store';
import { isInteractiveElement } from '~/lib/utils/dom';
import { getImageSizes } from '~/lib/utils/next';

interface BaseProductCardProps extends React.ComponentPropsWithRef<'div'> {
  product: WebshopMenuItem;
  size?: 'sm' | 'md';
  variant?: 'default' | 'cross-sell';
  actions?: {
    quantity: number;
    increment: () => void;
    decrement: () => void;
  };
}

// base product card — readonly is not actions are passed
const BaseProductCard = ({
  product,
  size = 'md',
  variant = 'default',
  className,
  actions,
  ...props
}: BaseProductCardProps) => {
  return (
    <ProductCard
      orientation={size === 'sm' ? 'horizontal' : 'vertical'}
      className={cn(
        'text-left w-full h-full',
        size === 'sm' && 'max-h-30',
        actions && 'cursor-pointer',
        className
      )}
      onClick={(e) => {
        if (!actions) return;

        props.onClick?.(e);
        if (e.defaultPrevented || isInteractiveElement(e.target)) return;

        actions.increment();
      }}
      {...props}
    >
      <ProductCardCover className={cn(size === 'sm' && 'h-full')}>
        {product?.tags &&
          product.tags.length > 0 &&
          product.tags.map((tag, index) => (
            <Badge key={index} size="xs" variant="over-media">
              {tag.label}
            </Badge>
          ))}

        <ImageWithFallback
          style={{
            borderRadius: 'inherit',
            objectFit: 'cover',
            width: '100%',
          }}
          src={product.thumbnailUrl}
          alt={product.name}
          width={0}
          height={0}
          sizes={
            size === 'md'
              ? getImageSizes({
                  default: '100vw',
                  sm: '50vw',
                  md: '33vw',
                  lg: '25vw',
                })
              : '176px'
          }
          fallbackSrc={'/item-no-image.png'}
        />
      </ProductCardCover>

      <ProductCardContent>
        <ProductCardTitle>{product.name}</ProductCardTitle>
        {product.description && (
          <ProductCardDescription>{product.description}</ProductCardDescription>
        )}
        <ProductCardFooter>
          {/* TODO: add promo price */}
          {/*{product.promoPrice ? (*/}
          {/*  <div className="space-x-1">*/}
          {/*    <Price*/}
          {/*      prefix={variant === 'cross-sell' ? '+' : ''}*/}
          {/*      amount={product.promoPrice}*/}
          {/*    />*/}
          {/*    <Price*/}
          {/*      amount={product.price}*/}
          {/*      className="line-through text-foreground-secondary"*/}
          {/*    />*/}
          {/*  </div>*/}
          {/*) : (*/}
          {/*  <Price prefix={variant === 'cross-sell' ? '+' : ''} amount={product.price} />*/}
          {/*)}*/}
          <Price
            prefix={variant === 'cross-sell' ? '+' : ''}
            amount={product.unitPrice || 0}
          />
          {actions && (
            <QuantitySelector
              quantity={actions.quantity}
              onChange={(qty) => {
                if (qty > actions.quantity) actions.increment();
                if (qty < actions.quantity) actions.decrement();
              }}
            >
              {actions.quantity > 0 && (
                <>
                  {actions.quantity > 1 && <QuantitySelectorDecrease size="sm" />}
                  {actions.quantity === 1 && (
                    <QuantitySelectorDelete
                      size="sm"
                      onClick={() => actions.decrement()}
                    />
                  )}
                  <QuantitySelectorQuantity />
                </>
              )}
              <QuantitySelectorIncrease size="sm" />
            </QuantitySelector>
          )}
        </ProductCardFooter>
      </ProductCardContent>
    </ProductCard>
  );
};

// extended base product card with cart actions
const EditableProductCard = ({
  product,
  ...props
}: Omit<BaseProductCardProps, 'actions'>) => {
  const [drawerProduct, setDrawerProduct] = useState<WebshopMenuItem | null>(null);
  const { items, addItem, removeItem } = useOrderCart();
  const { open } = useOrderConfigurationDialog();
  const isValidOrder = useIsValidOrderConfiguration();

  const quantity = useMemo(
    () => calculateProductQuantityInCart(product.id || '', items),
    [product.id, items]
  );

  const handleIncrement = useCallback(() => {
    // don't allow to do anything (add or show the product) if it's a delivery and no address is set
    console.log(`isValidOrder: ${isValidOrder}`);
    if (!isValidOrder) return open();
    setDrawerProduct(product);
    // addItem(productToCartItem(product));
    // TODO: Add the options drawer
    //
    // const hasConfiguration = product.configuration.length > 0;
    //
    // if (hasConfiguration) {
    //   setDrawerProduct(product);
    // } else {
    //   addItem(productToCartItem(product));
    // }
  }, [product, isValidOrder, open, addItem]);

  const handleDecrement = useCallback(() => {
    const item = items.find(({ productId }) => productId === product.id);
    if (!item) return;
    removeItem(item.id, 1);
  }, [product, items, removeItem]);

  return (
    <>
      <BaseProductCard
        product={product}
        {...props}
        actions={{
          quantity,
          increment: handleIncrement,
          decrement: handleDecrement,
        }}
      />
      <ProductConfigurationDrawer
        product={drawerProduct}
        onOpenChange={() => setDrawerProduct(null)}
      />
    </>
  );
};

interface ProductCardRecipeProps extends Omit<BaseProductCardProps, 'actions'> {
  readOnly?: boolean;
}

const ProductCardRecipe = ({ readOnly, ...props }: ProductCardRecipeProps) => {
  if (readOnly) return <BaseProductCard {...props} />;
  return <EditableProductCard {...props} />;
};

export { ProductCardRecipe as ProductCard };
