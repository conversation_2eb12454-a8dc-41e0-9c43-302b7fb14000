import { cn } from '@allo/ui';

export const Hero = ({
  children,
  ...props
}: React.ComponentPropsWithoutRef<'header'>) => {
  return <header {...props}>{children}</header>;
};

export const HeroCover = ({
  children,
  className,
  ...props
}: React.ComponentPropsWithoutRef<'figure'>) => {
  return (
    <figure
      className={cn('relative h-60 w-full overflow-hidden *:object-cover', className)}
      {...props}
    >
      {children}
    </figure>
  );
};

export const HeroBody = ({
  children,
  className,
  ...props
}: React.ComponentPropsWithoutRef<'div'>) => {
  return (
    <div
      className={cn(
        'max-w-container mx-auto py-8 md:py-10 flex flex-col md:grid md:grid-cols-[4fr_3fr] lg:grid-cols-[3fr_4fr] gap-8',
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

export const HeroContent = ({
  children,
  className,
  ...props
}: React.ComponentPropsWithoutRef<'div'>) => {
  return (
    <div {...props} className={cn('', className)}>
      {children}
    </div>
  );
};

export const HeroMedia = ({
  children,
  className,
  ...props
}: React.ComponentPropsWithoutRef<'div'>) => {
  return (
    <div
      {...props}
      className={cn(
        'max-md:h-60 md:min-h-60 border border-solid border-border-soft rounded-3xl overflow-hidden',
        className
      )}
    >
      {children}
    </div>
  );
};
