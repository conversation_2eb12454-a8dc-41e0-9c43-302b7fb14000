'use client';

import { cn, Slot } from '@allo/ui';
import { useState } from 'react';
import { useMousePan } from '~/lib/hooks/use-mouse-pan';
import { useScrollObserver } from '~/lib/hooks/use-scroll-observer';
import { composeRefs } from '~/lib/utils/react';

interface ScrollContainerProps extends React.ComponentPropsWithRef<'div'> {
  asChild?: boolean;
}

export const ScrollContainer = ({
  className,
  asChild,
  ref,
  ...props
}: ScrollContainerProps) => {
  const { ref: internalRef } = useMousePan();
  const [hasScroll, setHasScroll] = useState(false);

  useScrollObserver(internalRef, (el) => {
    setHasScroll(el.scrollWidth > el.clientWidth);
  });

  const Comp = asChild ? Slot : 'div';
  return (
    <Comp
      ref={composeRefs(internalRef, ref)}
      className={cn(
        '[--px:calc(0.5*(100vw-var(--width)))] [--width:var(--spacing-container)]',
        'scrollbar-hidden flex items-stretch overflow-scroll px-(--px) scroll-px-(--px)',
        hasScroll &&
          'data-[panning]:select-none data-[panning]:*:pointer-events-none cursor-grab active:cursor-grabbing',
        className
      )}
      {...props}
    />
  );
};
