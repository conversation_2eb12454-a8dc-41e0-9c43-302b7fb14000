import { cn, Slot } from '@allo/ui';

interface IconButtonProps extends React.ComponentPropsWithoutRef<'button'> {
  asChild?: boolean;
}

export const IconButton = ({
  children,
  className,
  asChild,
  ...props
}: IconButtonProps) => {
  const Component = asChild ? Slot : 'button';
  return (
    <Component
      className={cn(
        'relative size-5 flex items-center justify-center [&>svg]:size-4 rounded-sm *:relative',
        'before:ring-focus outline-none focus-visible:before:ring-4',
        'before:absolute before:-inset-1.5 before:rounded-[inherit] before:bg-foreground before:opacity-0',
        'hover:before:opacity-3 motion-safe:transition-colors active:before:opacity-5',
        className
      )}
      {...props}
    >
      {children}
    </Component>
  );
};
