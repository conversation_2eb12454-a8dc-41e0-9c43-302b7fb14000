import { cn } from '@allo/ui';
import { motion } from 'motion/react';
import { ComponentPropsWithRef, createContext, use, useId, useState } from 'react';

interface SegmentedControlContextType {
  id: string;
  value: string | undefined;
  setValue: (value: string) => void;
}

const SegmentedControlContext = createContext<SegmentedControlContextType>({
  id: '',
  value: undefined,
  setValue: () => {},
});

const useSegmentedControl = () => {
  const context = use(SegmentedControlContext);

  if (!context) {
    throw new Error('useSegmentedControl must be used within a SegmentedControl');
  }
  return context;
};

interface SegmentedControlProps
  extends Omit<ComponentPropsWithRef<'div'>, 'defaultValue' | 'onChange'> {
  value?: string;
  defaultValue?: string;
  onChange?: (value: string) => void;
}

export const SegmentedControl = ({
  className,
  defaultValue,
  value: propValue,
  onChange,
  ...props
}: SegmentedControlProps) => {
  const id = useId();
  const [internalValue, setInternalValue] = useState(defaultValue);

  const value = propValue ?? internalValue;

  const handleSetValue = (value: string) => {
    setInternalValue(value);
    if (onChange) onChange(value);
  };

  return (
    <SegmentedControlContext value={{ id, value, setValue: handleSetValue }}>
      <div
        id={id}
        className={cn(
          'outline-border-soft bg-background flex rounded-xl outline -outline-offset-1',
          className
        )}
        {...props}
      />
    </SegmentedControlContext>
  );
};

interface SegmentedControlItemProps extends Omit<ComponentPropsWithRef<'span'>, 'value'> {
  value: string;
  disabled?: boolean;
}

export const SegmentedControlItem = ({
  className,
  children,
  value,
  disabled,
  ...props
}: SegmentedControlItemProps) => {
  const id = useId();
  const ctx = useSegmentedControl();
  const isActive = ctx.value === value;

  return (
    <label
      htmlFor={id}
      className={cn(
        'relative rounded-[inherit] motion-safe:transition-colors flex-1',
        !isActive && 'hover:text-foreground-secondary text-foreground cursor-pointer',
        disabled && 'text-foreground-secondary cursor-not-allowed select-none'
      )}
    >
      <input
        id={id}
        type="radio"
        className="peer sr-only"
        name={ctx.id}
        value={id}
        onChange={(e) => {
          const isChecked = e.target.checked;
          if (isChecked) ctx.setValue(value);
        }}
        disabled={disabled}
      />
      {isActive && (
        <motion.span
          className="bg-background-highlight border-border-soft absolute inset-0 rounded-[inherit] border"
          layoutId={`segmented-control-${ctx.id}`}
          transition={{ type: 'spring', duration: 0.3, bounce: 0.1 }}
        />
      )}

      <span
        className={cn(
          'relative z-10 flex h-10 gap-1 items-center justify-center rounded-[inherit] px-4 text-sm',
          'ring-focus peer-focus-visible:ring-4',
          className
        )}
        {...props}
      >
        {children}
      </span>
    </label>
  );
};
