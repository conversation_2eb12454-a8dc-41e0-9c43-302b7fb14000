import { Button } from '@allo/ui';
import Image from 'next/image';
import notFoundImage from '~/assets/illustrations/not-found.png';
import { Link } from '~/i18n/navigation';

const TITLE = 'Page not found';
const DESCRIPTION = `It seems the page you're looking for doesn't exist. Let's get you back on track.`;

export default function NotFound() {
  return (
    <main className="grow text-center h-full w-full mx-auto max-w-sm flex flex-col items-center justify-center gap-6 px-4">
      <Image
        src={notFoundImage.src}
        width={notFoundImage.width}
        height={notFoundImage.height}
        alt="Not found illustration"
      />
      <div className="space-y-1.5">
        <h1 className="text-xl">{TITLE}</h1>
        <p className="text-base">{DESCRIPTION}</p>
      </div>
      <Button asChild variant="accent">
        <Link href="/restaurant/king-loui">Return to homepage</Link>
      </Button>
    </main>
  );
}
