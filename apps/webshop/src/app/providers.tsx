'use client';

import { IntlProvider } from '@allo/ui';
import { GoogleMapsAPIProvider } from '~/lib/google-maps';
import { PostHogProvider } from '~/lib/posthog';
import { SessionProvider } from '~/lib/session';

interface ProvidersProps {
  children: React.ReactNode;
}

export default function Providers({ children }: ProvidersProps) {
  return (
    <IntlProvider locale="de" currency="EUR">
      <GoogleMapsAPIProvider>
        <PostHogProvider>
          <SessionProvider>{children}</SessionProvider>
        </PostHogProvider>
      </GoogleMapsAPIProvider>
    </IntlProvider>
  );
}
