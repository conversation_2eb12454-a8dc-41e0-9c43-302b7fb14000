import Image from 'next/image';
import { notFound } from 'next/navigation';
import { Header, HeaderActions, HeaderLogo } from '~/components/header';
import { SessionButton } from '~/components/session-button';
import { getRestaurant } from '~/lib/api';

interface LocationLayoutProps {
  children: React.ReactNode;
  params: Promise<{ slug: string }>;
}

export default async function LocationLayout({ children, params }: LocationLayoutProps) {
  const { slug } = await params;

  const webshopRestaurant = await getRestaurant(slug);
  const webshopRestaurantData = webshopRestaurant.data;
  if (!webshopRestaurantData) return notFound();
  return (
    <>
      <Header>
        <HeaderLogo>
          <Image
            src={webshopRestaurantData?.logo || ''}
            alt={`${webshopRestaurantData?.name}-logo`}
            width="26"
            height="26"
          />
        </HeaderLogo>
        <HeaderActions>
          <SessionButton />
        </HeaderActions>
      </Header>
      {children}
    </>
  );
}
