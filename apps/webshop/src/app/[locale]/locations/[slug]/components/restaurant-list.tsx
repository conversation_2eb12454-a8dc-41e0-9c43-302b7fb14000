'use client';

import { Input, InputGroup, InputPrefix, InputSuffix } from '@allo/ui';
import { LocateFixedIcon, SearchIcon } from 'lucide-react';
import posthog from 'posthog-js';
import { useMemo, useState } from 'react';
import { RestaurantCard } from '~/components/restaurant-card';
import { WebshopBranchRestaurant } from '~/lib/api/types.ts';
import { isRestaurantOpen } from '~/lib/utils/dates.ts';
import { getCaptureId } from '~/lib/utils/posthog';

interface RestaurantListProps {
  restaurants: WebshopBranchRestaurant[];
}

export const RestaurantList = ({ restaurants }: RestaurantListProps) => {
  const [searchQuery, setSearchQuery] = useState('');

  const sortedRestaurants = useMemo(() => {
    return restaurants.sort((a) => {
      const isOpen = isRestaurantOpen(a.openingHours);
      return isOpen ? -1 : 1;
    });
  }, [restaurants]);

  const filteredRestaurants = useMemo(() => {
    const matches = (str: string) =>
      str.toLowerCase().includes(searchQuery.toLowerCase());

    return sortedRestaurants.filter((restaurant) => {
      return (
        (restaurant.name && matches(restaurant.name)) ||
        (restaurant.address && matches(restaurant.address))
      );
    });
  }, [sortedRestaurants, searchQuery]);

  return (
    <div className="max-w-container mx-auto">
      <div className="py-2 sticky top-(--header-height) bg-background-high z-10">
        <InputGroup className="md:max-w-75">
          <InputPrefix>
            <SearchIcon className="text-foreground-secondary" />
          </InputPrefix>
          <Input
            className="transition-none truncate"
            placeholder="Find restaurants near you..."
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value);

              posthog.capture(getCaptureId('locations_page', 'search_input', 'change'), {
                search_query: e.target.value,
              });
            }}
          />
          <InputSuffix interactive>
            <button className="relative text-xs outline-none focus-visible:ring-focus focus-visible:ring-4 rounded-sm after:-inset-1.5 after:absolute">
              <LocateFixedIcon className="text-foreground-secondary size-4 hover:text-foreground motion-safe:transition-colors" />
            </button>
          </InputSuffix>
        </InputGroup>
      </div>
      {filteredRestaurants.length > 0 ? (
        <ul className="py-2 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          {filteredRestaurants.map((restaurant, index) => (
            <li key={index}>
              <RestaurantCard webshopBranchRestaurant={restaurant} />
            </li>
          ))}
        </ul>
      ) : (
        <div className="text-foreground-secondary py-4 text-sm">
          No restaurants found matching your search.
        </div>
      )}
    </div>
  );
};
