import Image from 'next/image';
import { notFound } from 'next/navigation';
import { Menu } from '~/components/menu';
import { getRestaurant, getRestaurantMenus } from '~/lib/api';
import { BookATableBanner } from './components/book-a-table-banner';

interface MenuPageProps {
  params: Promise<{ slug: string }>;
}

export default async function MenuPage({ params }: MenuPageProps) {
  const { slug } = await params;
  const webshopRestaurant = await getRestaurant(slug);

  const webshopRestaurantData = webshopRestaurant.data;
  if (!webshopRestaurantData) return notFound();

  const webshopRestaurantMenus = await getRestaurantMenus(webshopRestaurantData.id || '');
  const menus = webshopRestaurantMenus.data;

  if (!menus) return notFound();

  return (
    <>
      <BookATableBanner slug={slug} restaurant={webshopRestaurantData} />
      <main>
        <header className="sticky top-0 left-0 h-(--header-height) w-full flex items-center justify-center z-20 bg-background-high">
          <Image
            src={webshopRestaurantData?.logo || ''}
            alt="Restaurant Logotype"
            width="26"
            height="26"
          />
        </header>
        <Menu menus={menus} readOnly />
      </main>
    </>
  );
}
