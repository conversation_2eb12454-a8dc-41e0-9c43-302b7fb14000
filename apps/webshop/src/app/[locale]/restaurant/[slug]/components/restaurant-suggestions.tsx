import { cn } from '@allo/ui';
import { RestaurantCard } from '~/components/restaurant-card';
import { ScrollContainer } from '~/components/scroll-container';
import { WebshopBranchRestaurant } from '~/lib/api/types.ts';

interface RestaurantSuggestionsProps extends React.ComponentPropsWithoutRef<'section'> {
  webshopBranchRestaurants: WebshopBranchRestaurant[];
}

export const RestaurantSuggestions = ({
  className,
  webshopBranchRestaurants,
}: RestaurantSuggestionsProps) => {
  return (
    <section className={cn('my-10', className)}>
      <div className="max-w-container mx-auto">
        <h2 className="text-lg max-w-90">Other locations</h2>
      </div>
      <ScrollContainer className="gap-2 md:gap-3 snap-x snap-mandatory py-4">
        {webshopBranchRestaurants.map((restaurant) => (
          <RestaurantCard
            key={restaurant.slug}
            webshopBranchRestaurant={restaurant}
            className="w-container md:w-100 lg:w-[calc(33%-(--spacing(1)))] shrink-0 snap-start"
          />
        ))}
      </ScrollContainer>
    </section>
  );
};
