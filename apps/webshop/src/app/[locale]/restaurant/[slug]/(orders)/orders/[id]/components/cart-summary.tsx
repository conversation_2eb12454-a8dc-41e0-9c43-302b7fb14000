import { Badge } from '@allo/ui';
import { Box, BoxContent, BoxFooter, BoxHeader, BoxTitle } from '~/components/box';
import { CartItemCard } from '~/components/cart/cart-item-card';
import { CartPriceSummary } from '~/components/cart/cart-price-summary';
import { WebShopOrder } from '~/lib/api/types.ts';
import { Order } from '~/lib/mock-data/orders';
import { CartItem } from '~/lib/order/types.ts';

interface CartSummaryProps {
  order: Order;
  items?: CartItem[];
  webShopOrder: WebShopOrder;
}

export const CartSummary = ({ order, items = [], webShopOrder }: CartSummaryProps) => {
  return (
    <Box collapsible>
      <BoxHeader>
        <BoxTitle>
          Cart Summary <Badge size="xs">{items.length} items</Badge>
        </BoxTitle>
      </BoxHeader>
      <BoxContent className="!p-0">
        <ul className="-mb-px">
          {items.map((item) => (
            <li key={item.id}>
              <CartItemCard item={item} className="p-3 md:p-4" />
            </li>
          ))}
        </ul>
      </BoxContent>
      <BoxFooter>
        <CartPriceSummary items={items} discount={order.discount} />
      </BoxFooter>
    </Box>
  );
};
