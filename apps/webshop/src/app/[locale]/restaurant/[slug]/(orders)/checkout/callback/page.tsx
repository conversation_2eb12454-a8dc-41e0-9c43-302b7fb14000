import { getCart, getRestaurant } from '~/lib/api';
import { notFound } from 'next/navigation';


interface Props {
  params: Promise<{ slug: string }>;
  searchParams: Promise<{
    cartId: string;
    success: boolean;
  }>;
}

/**
 * This page is used to handle the callback from the payment provider.
 * It will redirect based on the parameters to either the tracking page or back to the
 * checkout page.
 * @param props
 * @constructor
 */
export default async function CallbackPage({ params, searchParams }: Props) {
  const { slug } = await params;
  const { cartId, success } = await searchParams;
  const webshopRestaurant = await getRestaurant(slug);
  console.log(webshopRestaurant.data)
  if (!webshopRestaurant.data) return notFound();

  if (success) {
    const cart = await getCart(webshopRestaurant.data.id || '', cartId);
    console.log(cart);
    if (cart.data) {
      const order = cart.data.orderId;
      if (order) {
        window.location.href = `/en/restaurant/${slug}/orders/${order}`;
      }
    }
  } else {
    window.location.href = `/en/restaurant/${slug}/checkout`;
  }

  return null;
}
