import { PaymentMethods } from '@allo/ui';
import { Box, BoxContent, BoxHeader, BoxTitle } from '~/components/box';
import { Order } from '~/lib/mock-data/orders';

interface BillingSummaryProps {
  order: Order;
}

export const BillingSummary = ({ order }: BillingSummaryProps) => {
  return (
    <Box collapsible>
      <BoxHeader>
        <BoxTitle>Billing Details</BoxTitle>
      </BoxHeader>
      <BoxContent className="text-sm space-y-6">
        <div>
          <p>{order.billing.name}</p>
          <p>{order.billing.phone}</p>
        </div>
        <div className="flex items-center gap-2">
          <PaymentMethods methods={['mastercard']} />
          ********{order.billing.card.last4}
        </div>
      </BoxContent>
    </Box>
  );
};
