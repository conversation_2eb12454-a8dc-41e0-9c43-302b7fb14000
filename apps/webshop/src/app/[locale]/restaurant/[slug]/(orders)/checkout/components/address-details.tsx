'use client';

import { But<PERSON> } from '@allo/ui';
import { CalendarClock, MapPin, StoreIcon } from 'lucide-react';
import { useState } from 'react';
import { Box, BoxContent, BoxHeader, BoxTitle } from '~/components/box';
import { InlineButton } from '~/components/inline-button';
import { Map, Marker } from '~/components/map';
import { AddressSelection } from '~/components/order-configuration/address-selection';
import { TimeSelection } from '~/components/order-configuration/time-selection';
import { useOrderConfiguration } from '~/lib/order/store';
import { OrderTime } from '~/lib/order/types';
import { useRestaurant } from '~/lib/restaurant';
import { formatScheduledTime } from '~/lib/utils/dates';

export const AddressDetails = () => {
  const restaurant = useRestaurant();
  const { type, address, time } = useOrderConfiguration();
  const [editing, setEditing] = useState<'address' | 'schedule' | null>(null);

  return (
    <Box>
      <BoxHeader>
        <BoxTitle>{type === 'DELIVERY' ? 'Delivery details' : 'Pickup details'}</BoxTitle>
      </BoxHeader>
      <BoxContent>
        {editing === 'address' ? (
          <EditDetails exitEditMode={() => setEditing(null)} />
        ) : editing === 'schedule' ? (
          <EditSchedule exitEditMode={() => setEditing(null)} />
        ) : (
          <>
            {type === 'DELIVERY' && address && (
              <Map
                mapId="address-details"
                className="h-40"
                center={address.coordinates}
                zoom={17}
                controlled
              >
                <Marker
                  position={address.coordinates}
                  label="Delivery Address"
                  labelVisibility="always"
                />
              </Map>
            )}
            {/*{type === 'PICKUP' && (*/}
            {/*  <Map*/}
            {/*    mapId="address-details"*/}
            {/*    className="h-40"*/}
            {/*    center={restaurant?.address?.coordinates}*/}
            {/*    zoom={17}*/}
            {/*    controlled*/}
            {/*  >*/}
            {/*    <Marker*/}
            {/*      position={restaurant?.address?.coordinates}*/}
            {/*      label="Pickup Location"*/}
            {/*      labelVisibility="always"*/}
            {/*    />*/}
            {/*  </Map>*/}
            {/*)}*/}
            <div className="flex flex-col gap-1 mt-3">
              <div className="flex items-center gap-1.5 text-sm">
                {type === 'DELIVERY' && (
                  <>
                    <MapPin
                      className={!address ? 'text-accent' : 'text-foreground-secondary'}
                    />
                    {address && <p>{address?.formattedAddress}</p>}
                    <InlineButton
                      variant={address ? 'secondary' : 'accent'}
                      onClick={() => setEditing('address')}
                    >
                      {address ? 'Edit' : 'Add address'}
                    </InlineButton>
                  </>
                )}
                {type === 'PICKUP' && (
                  <>
                    <StoreIcon className="text-foreground-secondary" />
                    <p>Pickup at {restaurant?.address}</p>
                  </>
                )}
              </div>
              <div className="flex items-center gap-1.5 text-sm">
                <CalendarClock className="text-foreground-secondary" />
                <p>{time.type === 'standard' ? 'Now' : formatScheduledTime(time.date)}</p>
                <InlineButton variant="secondary" onClick={() => setEditing('schedule')}>
                  Edit
                </InlineButton>
              </div>
            </div>
          </>
        )}
      </BoxContent>
    </Box>
  );
};

interface EditDetailsProps {
  exitEditMode: () => void;
}

const EditDetails = ({ exitEditMode }: EditDetailsProps) => {
  const { type, address, setAddress } = useOrderConfiguration();

  return (
    <>
      {type === 'DELIVERY' && (
        <div>
          <h2 className="text-lg mb-4">Where to deliver</h2>
          <AddressSelection
            initialValue={address}
            onConfirm={(address) => {
              setAddress(address);
              exitEditMode();
            }}
            renderActions={({
              onConfirm,
              isValid,
              onDetailsConfirm,
              onDetailsCancel,
              isDetailsValid,
            }) => (
              <div className="flex gap-2 mt-4">
                {onConfirm && (
                  <>
                    <Button onClick={exitEditMode}>Cancel</Button>
                    <Button variant="accent" onClick={onConfirm} disabled={!isValid}>
                      Confirm Details
                    </Button>
                  </>
                )}
                {onDetailsCancel && <Button onClick={onDetailsCancel}>Cancel</Button>}
                {onDetailsConfirm && (
                  <Button
                    variant="accent"
                    onClick={onDetailsConfirm}
                    disabled={!isDetailsValid}
                  >
                    Confirm Details
                  </Button>
                )}
              </div>
            )}
          />
        </div>
      )}
    </>
  );
};

interface EditScheduleProps {
  exitEditMode: () => void;
}

const EditSchedule = ({ exitEditMode }: EditScheduleProps) => {
  const { type, time, setTime } = useOrderConfiguration();
  const [internalTime, setInternalTime] = useState<OrderTime | null>(time);

  const handleConfirm = () => {
    if (internalTime) {
      setTime(internalTime);
    }

    exitEditMode();
  };

  return (
    <div>
      <h2 className="text-lg mb-4">
        {type === 'PICKUP' ? 'Pick up window' : 'Delivery at'}
      </h2>
      <TimeSelection value={internalTime} onChange={setInternalTime} />
      <div className="flex gap-2 mt-4">
        <Button onClick={exitEditMode}>Cancel</Button>
        <Button variant="accent" onClick={handleConfirm} disabled={!internalTime}>
          Confirm Details
        </Button>
      </div>
    </div>
  );
};
