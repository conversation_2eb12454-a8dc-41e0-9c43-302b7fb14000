import { notFound } from 'next/navigation';
import { getOrderFromOrderId, getRestaurantFromSlug } from '~/lib/api';
import { productsToCartItems } from '~/lib/order/cart/utils.ts';
import { CartSummary } from './components/cart-summary';
import { DeliverySummary } from './components/delivery-summary';
import { OrderHero } from './components/order-hero';
import { OrderProgress } from './components/order-progress';

type Props = {
  params: Promise<{ id: string; slug: string }>;
};

export default async function OrderPage({ params }: Props) {
  const { id, slug } = await params;
  if (!slug) return notFound();

  // 1. From slug get restaurnt
  const restaurant = await getRestaurantFromSlug(slug).catch(() => notFound()); // FUTURE: handle error once we have a real API
  if (!restaurant.data) return notFound();
  const { id: restaurantId } = restaurant.data;
  // 2. From restaurant get order
  const orderFromApi = await getOrderFromOrderId(restaurantId || '', id).catch(() =>
    notFound()
  ); // FUTURE: handle error once we have a real API
  console.log(orderFromApi.data);
  if (!orderFromApi.data) return notFound();
  const items = productsToCartItems(orderFromApi.data.items || []);
  //const order = await fetchOrder(id).catch(() => notFound()); // FUTURE: handle error once we have a real API
  return (
    <main className="max-w-container-tight mx-auto mt-8 mb-16">
      <OrderHero webShopOrder={orderFromApi.data} />
      {orderFromApi.data.status !== 'PAYMENT_REQUESTED' &&
        orderFromApi.data.status !== 'CANCELLED' && (
          <OrderProgress webShopOrder={orderFromApi.data} />
        )}
      <div className="my-8 space-y-3">
        <DeliverySummary webShopOrder={orderFromApi.data} />
        {/*<BillingSummary order={order} />*/}
        <CartSummary items={items} webShopOrder={orderFromApi.data} />
      </div>
    </main>
  );
}
