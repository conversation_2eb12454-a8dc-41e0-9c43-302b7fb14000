'use client';

import { <PERSON><PERSON>, Card, cn, Price } from '@allo/ui';
import { BikeIcon, CalendarIcon, ShoppingBagIcon, StoreIcon } from 'lucide-react';
import Image from 'next/image';
import { useMemo } from 'react';
import { Property } from '~/components/property';
import { Link } from '~/i18n/navigation';
import { Order } from '~/lib/mock-data/orders';

interface OrderCardProps {
  order: Order;
}

export const OrderCard = ({ order }: OrderCardProps) => {
  const images = useMemo(() => {
    return order.items
      .map((item) => item.image)
      .filter((str) => typeof str === 'string')
      .splice(0, 4);
  }, [order.items]);

  return (
    <Card depth asChild className="p-4 flex gap-3 border-accent">
      <Link href={`/${order.restaurant.slug}/orders/${order.id}`}>
        <div
          className={cn(
            'size-22 shrink-0 grid overflow-hidden rounded-sm gap-px',
            images.length === 1 && 'grid-cols-1 grid-rows-1',
            images.length === 2 && 'grid-cols-2 grid-rows-1',
            images.length > 2 && 'grid-cols-2 grid-rows-2',
            images.length === 3 && '*:last:col-span-2'
          )}
        >
          {images.map((image, index) => (
            <Image
              key={index}
              src={image}
              alt="menu item"
              className="size-full object-cover bg-background"
              sizes="64px"
              width={64}
              height={64}
            />
          ))}
        </div>
        <div className="grow flex flex-col justify-between overflow-hidden">
          <header className="text-base flex items-center justify-between">
            <h2 className="leading-none">Order #{order.id}</h2>
            <Badge
              size="xs"
              className="capitalize"
              variant={
                (order.status === 'cancelled' && 'negative') ||
                (order.status === 'completed' && 'positive') ||
                'default'
              }
            >
              {order.status === 'in-progress' ? 'In Progress' : order.status}
            </Badge>
          </header>
          <div className="mt-2 mb-0.5 text-xs text-foreground-secondary space-y-1">
            <Property variant="secondary" size="xs">
              <CalendarIcon />
              <span>
                {order.createdAt.toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit',
                })}
              </span>
            </Property>
            <Property variant="secondary" size="xs">
              <ShoppingBagIcon />
              <span>
                {order.items.length} item{order.items.length > 1 ? 's' : ''}
                {' • '}
                <Price className="inline-block" amount={order.totalPrice} />
              </span>
            </Property>
            <Property variant="secondary" size="xs">
              {order.type === 'delivery' && (
                <>
                  <BikeIcon />
                  <span>{order.address?.formattedAddress}</span>
                </>
              )}
              {order.type === 'pickup' && (
                <>
                  <StoreIcon />
                  <span>Pickup at {order.restaurant.address.formattedAddress}</span>
                </>
              )}
            </Property>
          </div>
        </div>
      </Link>
    </Card>
  );
};
