'use client';

import { <PERSON><PERSON>, <PERSON><PERSON>, cn } from '@allo/ui';
import posthog from 'posthog-js';
import { useMemo } from 'react';
import { Box, BoxContent, BoxFooter, BoxHeader, BoxTitle } from '~/components/box';
import { CartItemCard } from '~/components/cart/cart-item-card';
import { CartPriceSummary } from '~/components/cart/cart-price-summary';
import { postRequestCartPayment } from '~/lib/api';
import { useTailwindBreakpoint } from '~/lib/hooks/use-tailwind-breakpoint';
import {
  useOrderCart,
  useOrderConfiguration,
  useOrderDiscount,
  useOrderStore,
} from '~/lib/order/store';
import { isValidOrder } from '~/lib/order/utils';
import { useRestaurant } from '~/lib/restaurant';
import { getCaptureId } from '~/lib/utils/posthog';

const isBillingMethodValid = (billingMethod: string) => {
  return (
    billingMethod === 'cash' || billingMethod === 'allo_pay_online'
  );
};

const getValidPaymentChannel = (billingMethod: string) => {
  switch (billingMethod) {
    case 'cash':
      return 'CASH';
    case 'allo_pay_online':
      return 'ALLO_PAY_ONLINE';
    default:
      return 'CASH';
  }
};

export const CartSummary = () => {
  const { items, cartId, hasCartId } = useOrderCart();
  const { discount, setDiscount } = useOrderDiscount();
  const { billing } = useOrderConfiguration();
  const { slug, id: restaurantId } = useRestaurant();
  const order = useOrderStore();
  const canOrder = useMemo(() => isValidOrder(order), [order]);

  const isMd = useTailwindBreakpoint('md');

  const onPlaceOrder = () => {
    if (!billing || !hasCartId) return;
    if (!isBillingMethodValid(billing.paymentMethod || '')) return;
    const paymentChannel = getValidPaymentChannel(billing.paymentMethod || '');
    postRequestCartPayment(restaurantId || '', cartId, paymentChannel)
      .then((response) => {
        const d = response.data;
        debugger;
        if (d) {
          const orderId = d.orderId;
          window.location.href = `/en/restaurant/${slug}/orders/${orderId}`;
          // TODO: Redirect to payment page
        } else {
          // TODO: ERROR
        }
      })
      .catch((err) => {
        debugger;
        // TODO: Error
      });
    posthog.capture(getCaptureId('checkout_page', 'place_order', 'click'), {
      items: order.items,
      discount: order.discount,
      type: order.type,
      address: order.type === 'DELIVERY' ? order.address : undefined,
      time: order.time,
      billing: order.billing,
    });
  };

  return (
    <Box className="md:flex flex-col" collapsible={!isMd} initialExpanded={false}>
      <BoxHeader className="relative shrink-0">
        <div className="flex items-center gap-2">
          <BoxTitle>Cart Summary</BoxTitle>
          <Badge size="xs">{`${items.length} item${items.length > 1 ? 's' : ''}`}</Badge>
        </div>
      </BoxHeader>
      <BoxContent className="p-0 md:p-0 md:overflow-y-auto">
        <div className="min-h-0 -mb-px">
          {items.map((item) => (
            <CartItemCard key={item.id} item={item} />
          ))}
        </div>
      </BoxContent>
      <BoxFooter className="shrink-0">
        <CartPriceSummary
          items={items}
          discount={discount}
          actions={{
            removeDiscount: () => {
              posthog.capture(getCaptureId('checkout_page', 'discount_code', 'remove'), {
                discount,
              });

              setDiscount(null);
            },
          }}
        />
        <Button
          variant="accent"
          className={cn('mt-3 w-full grow', !canOrder && 'opacity-50')}
          disabled={!canOrder}
          inert={!canOrder}
          onClick={onPlaceOrder}
        >
          Place Order
        </Button>
      </BoxFooter>
    </Box>
  );
};
