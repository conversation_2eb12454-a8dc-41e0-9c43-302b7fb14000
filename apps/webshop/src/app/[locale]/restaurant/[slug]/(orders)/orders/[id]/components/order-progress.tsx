'use client';

import { MapPinIcon } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Property } from '~/components/property';
import { Step, StepContent, StepLabel, Stepper } from '~/components/stepper';
import { getRestaurantOrderStatus } from '~/lib/api';
import { WebShopOrder } from '~/lib/api/types.ts';

const STEPS: { label: string; duration: number }[] = [
  { label: 'a', duration: 1500 },
  { label: 'b', duration: 3000 },
  { label: 'c', duration: 12000 },
  { label: 'd', duration: 1000 },
];

type Step = (typeof STEPS)[number]['label'];

interface OrderProgressProps {
  webShopOrder: WebShopOrder;
}

export const OrderProgress = ({ webShopOrder }: OrderProgressProps) => {
  const [activeStep, setActiveStep] = useState<Step>('a');
  if (!webShopOrder) return null;

  const getActiveStep = (
    status:
      | 'OPEN'
      | 'PAYMENT_REQUESTED'
      | 'PENDING'
      | 'PREPARING'
      | 'READY'
      | 'DELIVERING'
      | 'CLOSED'
      | 'CANCELLED'
  ) => {
    // PENDING => a
    // PREPARING => b
    // READY | DELIVERING => c
    // CLOSED => d
    switch (status) {
      case 'OPEN':
        return 'a';
      case 'PENDING':
      case 'PREPARING':
        return 'b';
      case 'READY':
      case 'DELIVERING':
        return 'c';
      case 'CLOSED':
        return 'd';
      default:
        return null;
    }
  };

  useEffect(() => {
    const fetchRestaurantStatus = () => {
      getRestaurantOrderStatus(webShopOrder.restaurantId || '', webShopOrder.id || '')
        .then((d) => {
          if (d.data) {
            const { status = 'OPEN' } = d.data;
            const step = getActiveStep(status);
            if (step && step !== activeStep) {
              setActiveStep(step);
            }
          }
        })
        .catch(console.error);
    };

    // Call immediately on mount
    fetchRestaurantStatus();

    // Set up interval to call every 5 seconds
    const interval = setInterval(fetchRestaurantStatus, 5000);

    // Cleanup function to clear interval when component unmounts
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    const step = getActiveStep(webShopOrder.status || 'CLOSED');
    if (step) {
      setActiveStep(step);
    }
  }, [webShopOrder.status]);

  return (
    <Stepper step={activeStep} className="my-8">
      <Step value="a" duration={800}>
        <StepLabel>Received by {webShopOrder.restaurant?.name} staff</StepLabel>
      </Step>
      <Step value="b" duration={9000}>
        <StepLabel>Preparing with care</StepLabel>
      </Step>
      {webShopOrder.type === 'DELIVERY' && (
        <Step value="c" duration={5000}>
          <StepLabel>Ready to be delivered</StepLabel>
          <StepContent>
            {/*<Map*/}
            {/*  mapId="order-progress"*/}
            {/*  className="w-full h-50"*/}
            {/*  defaultBounds={{*/}
            {/*    ...calculateContainerBounds([*/}
            {/*      order.restaurant.address.coordinates,*/}
            {/*      order.address!.coordinates,*/}
            {/*    ]),*/}
            {/*    padding: 25,*/}
            {/*  }}*/}
            {/*>*/}
            {/*  <Marker*/}
            {/*    position={order.address!.coordinates}*/}
            {/*    label="Delivery Address"*/}
            {/*    labelVisibility="always"*/}
            {/*  />*/}
            {/*  <Marker*/}
            {/*    position={order.restaurant.address.coordinates}*/}
            {/*    label={order.restaurant.name}*/}
            {/*    labelVisibility="always"*/}
            {/*  />*/}
            {/*</Map>*/}
            <Property className="mt-2">
              <MapPinIcon className="text-foreground-secondary" />
              {webShopOrder.formattedDeliveryAddress}
            </Property>
          </StepContent>
        </Step>
      )}
      {webShopOrder.type === 'PICKUP' && (
        <Step value="c" duration={0}>
          <StepLabel>Ready to be picked up</StepLabel>
          <StepContent>
            {/*<Map*/}
            {/*  mapId="order-progress"*/}
            {/*  defaultCenter={order.restaurant.address.coordinates}*/}
            {/*  className="w-full h-50"*/}
            {/*>*/}
            {/*  <Marker*/}
            {/*    position={order.restaurant.address.coordinates}*/}
            {/*    label="Pickup Location"*/}
            {/*    labelVisibility="always"*/}
            {/*  />*/}
            {/*</Map>*/}
            <Property className="mt-2">
              <MapPinIcon className="text-foreground-secondary" />
              {webShopOrder.formattedDeliveryAddress}
            </Property>
          </StepContent>
        </Step>
      )}
      <Step value="d">
        <StepLabel>We hope you enjoy your order</StepLabel>
      </Step>
    </Stepper>
  );
};
