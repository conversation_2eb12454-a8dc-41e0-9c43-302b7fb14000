import { Badge } from '@allo/ui';
import { WebShopOrder } from '~/lib/api/types.ts';

const getOrderDescription = async (order: WebShopOrder) => {
  const isPendingOrder = ['CLOSED', 'CANCELED'].includes(order.status || '');
  if (!isPendingOrder) {
    return 'Estimated time 14:45 - 15:45 PM';
  }

  return new Date(order.actualDeliveryTime || '').toLocaleString('en-US', {
    day: 'numeric',
    month: 'long',
    hour: 'numeric',
    minute: 'numeric',
  });
};

interface OrderHeroProps {
  webShopOrder: WebShopOrder;
}

export const OrderHero = async ({ webShopOrder }: OrderHeroProps) => {
  return (
    <header>
      <Badge
        size="sm"
        className="capitalize"
        variant={
          (webShopOrder.status === 'CANCELLED' && 'negative') ||
          (webShopOrder.status === 'CLOSED' && 'positive') ||
          'default'
        }
      >
        {webShopOrder.status === 'PENDING' ? 'In Progress' : webShopOrder.status}
      </Badge>
      <h1 className="mt-3 text-xl">Thank for your order #{webShopOrder.id}</h1>
      <p className="mt-1.5 text-base text-foreground-secondary">
        {await getOrderDescription(webShopOrder)}
      </p>
      <div className="flex items-center gap-2 mt-4">
        {/*<Button*/}
        {/*  size="sm"*/}
        {/*  onClick={() => {*/}
        {/*    posthog.capture(getCaptureId('order_page', 'show_receipt', 'click'), {*/}
        {/*      order,*/}
        {/*    });*/}
        {/*  }}*/}
        {/*>*/}
        {/*  <ReceiptTextIcon />*/}
        {/*  Receipt*/}
        {/*</Button>*/}
        {/*<Button*/}
        {/*  size="sm"*/}
        {/*  onClick={() => {*/}
        {/*    posthog.capture(getCaptureId('order_page', 'contact_us', 'click'), {*/}
        {/*      order,*/}
        {/*    });*/}
        {/*  }}*/}
        {/*>*/}
        {/*  <PhoneIcon />*/}
        {/*  Contact us*/}
        {/*</Button>*/}
      </div>
    </header>
  );
};
