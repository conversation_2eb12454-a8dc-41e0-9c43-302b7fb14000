import { fetchOrders } from '~/lib/mock-data/api';
import { OrderCard } from './components/order-card';

export default async function OrderPages() {
  const orders = await fetchOrders();

  return (
    <div className="max-w-container-tight mx-auto mt-10 mb-16">
      <header className="my-8">
        <h1 className="text-xl">My Orders</h1>
        <p className="mt-1.5 text-base text-foreground-secondary">
          View and track your order history.
        </p>
      </header>
      <main>
        <section className="my-6">
          <ul className="space-y-4">
            {orders.map((order) => (
              <li key={order.id}>
                <OrderCard order={order} />
              </li>
            ))}
          </ul>
        </section>
      </main>
    </div>
  );
}
