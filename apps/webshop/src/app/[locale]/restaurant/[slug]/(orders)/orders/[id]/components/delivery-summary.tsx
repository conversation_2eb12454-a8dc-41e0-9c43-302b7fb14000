import { CalendarIcon, MapPinIcon, StoreIcon } from 'lucide-react';
import { Box, BoxContent, BoxHeader, BoxTitle } from '~/components/box';
import { Property } from '~/components/property';
import { WebShopOrder } from '~/lib/api/types.ts';

interface DeliverySummaryProps {
  webShopOrder: WebShopOrder;
}

export const DeliverySummary = ({ webShopOrder }: DeliverySummaryProps) => {
  return (
    <Box collapsible>
      <BoxHeader>
        <BoxTitle>Delivery Details</BoxTitle>
      </BoxHeader>
      <BoxContent className="space-y-2">
        <Property>
          {webShopOrder.type === 'DELIVERY' && (
            <>
              <MapPinIcon />
              <span>{webShopOrder.formattedDeliveryAddress}</span>
            </>
          )}
          {webShopOrder.type === 'PICKUP' && (
            <>
              <StoreIcon />
              <span>Pickup at {webShopOrder.formattedDeliveryAddress}</span>
            </>
          )}
        </Property>
        {webShopOrder.estimatedDeliveryTime && (
          <Property>
            <CalendarIcon />
            {webShopOrder.estimatedDeliveryTime}
            {/*{order.time.type === 'standard' ? 'Standard' : 'Scheduled'} ·{' '}*/}
            {/*{order.time.type === 'standard'*/}
            {/*  ? '20-50min'*/}
            {/*  : formatScheduledTime(order.time.date)}*/}
          </Property>
        )}
      </BoxContent>
    </Box>
  );
};
