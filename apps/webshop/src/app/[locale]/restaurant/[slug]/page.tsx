import { Divider } from '@allo/ui';
import Image from 'next/image';
import { notFound } from 'next/navigation';
import { CartButton } from '~/components/cart/cart-button';
import {
  Header,
  HeaderActions,
  HeaderLogo,
  HeaderPrimaryAction,
} from '~/components/header';
import { Menu } from '~/components/menu';
import { OrderConfigurationButton } from '~/components/order-configuration/order-configuration-button';
import { OrderConfigurationDialogProvider } from '~/components/order-configuration/order-configuration-dialog';
import { SessionButton } from '~/components/session-button';
import { Link } from '~/i18n/navigation';
import { getRestaurant, getRestaurantMenus } from '~/lib/api';
import { WebshopBranchRestaurant } from '~/lib/api/types.ts';
import { RestaurantHero } from './components/restaurant-hero';
import { RestaurantSuggestions } from './components/restaurant-suggestions';

interface RestaurantPageProps {
  params: Promise<{ slug: string }>;
}

export default async function RestaurantPage({ params }: RestaurantPageProps) {
  const { slug } = await params;
  const webshopRestaurant = await getRestaurant(slug);

  const webshopRestaurantData = webshopRestaurant.data;
  if (!webshopRestaurantData) return notFound();

  const webshopRestaurantMenus = await getRestaurantMenus(webshopRestaurantData.id || '');
  const menus = webshopRestaurantMenus.data;
  if (!menus) return notFound();

  return (
    <OrderConfigurationDialogProvider>
      <Header>
        <HeaderLogo>
          <Link href={`/restaurant/${slug}`}>
            <Image
              src={webshopRestaurantData?.logo || ''}
              alt={`${webshopRestaurantData?.name}-logo`}
              width="26"
              height="26"
            />
          </Link>
        </HeaderLogo>
        <HeaderPrimaryAction>
          <OrderConfigurationButton />
        </HeaderPrimaryAction>
        <HeaderActions>
          <CartButton />
          <SessionButton />
        </HeaderActions>
      </Header>
      <main>
        <RestaurantHero webshopRestaurant={webshopRestaurantData} slug={slug} />
        {/*{!restaurant.isOpen && (*/}
        {/*  <CrossSell*/}
        {/*    webshopBranchRestaurants={webshopRestaurantData.branchRestaurants || []}*/}
        {/*  />*/}
        {/*)}*/}
        <Menu menus={menus} />
        {webshopRestaurantData &&
          webshopRestaurantData?.branchRestaurants &&
          webshopRestaurantData?.branchRestaurants?.length > 0 && (
            <CrossSell
              webshopBranchRestaurants={webshopRestaurantData.branchRestaurants || []}
            />
          )}
      </main>
    </OrderConfigurationDialogProvider>
  );
}

const CrossSell = async ({
  webshopBranchRestaurants = [],
}: {
  webshopBranchRestaurants: WebshopBranchRestaurant[];
}) => {
  if (webshopBranchRestaurants.length === 0) return null;

  return (
    <>
      <Divider />
      <RestaurantSuggestions webshopBranchRestaurants={webshopBranchRestaurants} />
    </>
  );
};
