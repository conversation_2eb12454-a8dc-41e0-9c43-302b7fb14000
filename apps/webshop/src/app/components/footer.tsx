import { Divider } from '@allo/ui';
import { ExternalLinkIcon } from 'lucide-react';
import { InlineButton } from '~/components/inline-button';
import { LanguageSwitcher } from './language-switcher';

export const Footer = () => (
  <footer className="pt-5 mt-auto">
    <Divider />
    <div className="flex flex-col md:flex-row gap-x-6 gap-y-2 md:p-6.5 xl:px-12 p-4">
      <InlineButton
        asChild
        className="mr-auto max-md:mt-2 max-md:order-99"
        variant="secondary"
      >
        <a href="https://allo.restaurant" target="_blank" rel="noopener noreferrer">
          <ExternalLinkIcon />
          allO 2025 @ all rights reserved
        </a>
      </InlineButton>
      <span className="flex max-md:-order-1 max-md:mb-4 align-baseline">
        <LanguageSwitcher />
      </span>
      <InlineButton asChild variant="secondary">
        <a
          href="https://allo.restaurant/privacy-policy"
          target="_blank"
          rel="noopener noreferrer"
        >
          Privacy Policy
        </a>
      </InlineButton>
      <InlineButton asChild variant="secondary">
        <a
          href="https://allo.restaurant/terms-guests"
          target="_blank"
          rel="noopener noreferrer"
        >
          Terms and Conditions
        </a>
      </InlineButton>
    </div>
  </footer>
);
