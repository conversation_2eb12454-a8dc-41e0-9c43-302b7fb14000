'use client';

import { Dropdown, DropdownItem, DropdownItems, DropdownTrigger } from '@allo/ui';
import { EarthIcon } from 'lucide-react';
import { useMemo, useState } from 'react';
import { InlineButton } from '~/components/inline-button';

const LANGUAGES = {
  en: {
    label: 'English',
    flag: '/imgs/flags/uk.svg',
  },
  pt: {
    label: 'Português',
    flag: '/imgs/flags/pt.svg',
  },
  cn: {
    label: '中文',
    flag: '/imgs/flags/cn.svg',
  },
  de: {
    label: 'Deutsch',
    flag: '/imgs/flags/de.svg',
  },
  es: {
    label: 'Español',
    flag: '/imgs/flags/es.svg',
  },
  fr: {
    label: 'Français',
    flag: '/imgs/flags/fr.svg',
  },
  th: {
    label: 'ไทย',
    flag: '/imgs/flags/th.svg',
  },
  tk: {
    label: 'Türkçe',
    flag: '/imgs/flags/tk.svg',
  },
  vn: {
    label: 'Tiếng Việt',
    flag: '/imgs/flags/vn.svg',
  },
} as const;

type ShortLang = keyof typeof LANGUAGES;

export const LanguageSwitcher = () => {
  const [lang, setLang] = useState<ShortLang>('en');
  const language = useMemo(() => LANGUAGES[lang], [lang]);

  return (
    <Dropdown>
      <DropdownTrigger asChild>
        <InlineButton>
          <EarthIcon />
          {language.label}
        </InlineButton>
      </DropdownTrigger>
      <DropdownItems>
        {Object.entries(LANGUAGES).map(([key, { label, flag }]) => (
          <DropdownItem
            key={key}
            onClick={() => setLang(key as ShortLang)}
            disabled={key === lang}
          >
            {/* eslint-disable-next-line @next/next/no-img-element */}
            <img alt={`${label} flag`} src={flag} className="size-4" />
            {label}
          </DropdownItem>
        ))}
      </DropdownItems>
    </Dropdown>
  );
};
