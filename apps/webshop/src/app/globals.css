@import '@allo/ui/tailwind';

@source "../../node_modules/@allo/ui/dist";
@source "../../../../node_modules/@allo/ui/dist/";

@theme {
  /* max-width container that works on all screen sizes */
  --spacing-container: calc(min(--spacing(340), 100vw) - 2 * var(--section-px, 0rem));
  --spacing-container-narrow: --spacing(228);
  --spacing-container-tight: calc(
    min(--spacing(141), 100vw) - 2 * var(--section-px, 0rem)
  );
  --spacing-em: 1em;

  /* used in stepper component */
  --animate-scale-pulse: scale-pulse 1.3s infinite ease-in-out;
  @keyframes scale-pulse {
    0% {
      transform: scale(1);
    }
    50% {
      opacity: 1;
    }
    100% {
      transform: scale(2.75);
      opacity: 0;
    }
  }
}

:root {
  @apply [--header-height:--spacing(13)];
  @apply [--section-px:--spacing(4)] md:[--section-px:--spacing(8)];
}

html {
  @apply scroll-pt-12 motion-safe:scroll-smooth;
}

body {
  @apply bg-background-high;
}

button {
  @apply cursor-pointer;
}

@layer utilities {
  .input-focus-visible {
    @apply focus-visible:ring-focus focus-visible:ring-4 focus-visible:outline-none;
  }
}
