# allO Front-end

Monorepo resposible for all allO apps.

## What's inside?

This Turborepo includes the following packages/apps:

### Apps and Packages

- `reservations`: a [Next.js](https://nextjs.org/) app with [Tailwind CSS](https://tailwindcss.com/)
- `@monorepo/utils`: shared utilities
- `@monorepo/eslint-prettier-config`: `eslint` configurations (includes `eslint-config-next` and `eslint-config-prettier`)
- `@monorepo/typescript-config`: `tsconfig.json`s used throughout the monorepo

Each package/app is 100% [TypeScript](https://www.typescriptlang.org/).

### Utilities

This Turborepo has some additional tools already setup for you:

- [Tailwind CSS](https://tailwindcss.com/) for styles
- [TypeScript](https://www.typescriptlang.org/) for static type checking
- [ESLint](https://eslint.org/) for code linting
- [Prettier](https://prettier.io) for code formatting

### build with docker encapsulation

#### prepare the build image

```
docker build --rm -t node:20-slim-corepack -f .src/main/docker/Dockerfile.build-node20-corepack .
```

#### execute builds


```
rm -rf \
  package-lock.json \
  .npm \
  .turbo \
  node_modules \
  out \
  apps/reservations/.next \
  apps/reservations/node_modules \
  apps/reservations/.swc \
  apps/reservations/.turbo \
  apps/kiosk/.next \
  apps/kiosk/node_modules \
  apps/kiosk/.swc \
  apps/kiosk/.turbo

docker build --rm -t node:20-slim-corepack -f .src/main/docker/Dockerfile.build-node20-corepack .

docker run --rm -it --net=host \
  -u $(id -u ${USER}):$(id -g ${USER}) \
  -v "$(pwd)":"$(pwd)" \
  -w "$(pwd)" \
  -v "${HOME}/.gitconfig":"/home/<USER>/.gitconfig" \
  -e JS_BUILD_CUSTOM_SCOPE_REGISTRY_AUTHTOKEN=$(gcloud auth print-access-token --project=endless-gizmo-264508) \
  -e JS_BUILD_CUSTOM_SCOPE_REGISTRY_GCLOUD_PROJECT=endless-gizmo-264508 \
  -e JS_BUILD_REGISTRY_AUTHTOKEN=$(gcloud auth print-access-token --project=iac-dev-432418) \
  -e JS_BUILD_REGISTRY_GCLOUD_PROJECT=iac-dev-432418 \
  -e JS_BUILD_PREFER_CUSTOM_REGISTRY="" \
  -e NODE_ENV=development \
  node:20-slim-corepack turbo prune kiosk --docker

turbo 2.5.1

WARNING  Unable to calculate transitive closures: Missing version from non-workspace package: 'node_modules/@types/node'
Generating pruned monorepo for kiosk in /home/<USER>/_workspace/git-allo/basent/allo-frontend/out
 - Added @monorepo/eslint-prettier-config
 - Added @monorepo/jest-playwright-config
 - Added @monorepo/typescript-config
 - Added @monorepo/utils
 - Added kiosk


```


set authentication and pull dependencies (basically doing npm install or yarn)

```
docker run --rm -it --net=host \
  -u $(id -u ${USER}):$(id -g ${USER}) \
  -v "$(pwd)":"$(pwd)" \
  -w "$(pwd)" \
  -v "${HOME}/.gitconfig":"/home/<USER>/.gitconfig" \
  -e JS_BUILD_CUSTOM_SCOPE_REGISTRY_AUTHTOKEN=$(gcloud auth print-access-token --project=endless-gizmo-264508) \
  -e JS_BUILD_CUSTOM_SCOPE_REGISTRY_GCLOUD_PROJECT=endless-gizmo-264508 \
  -e JS_BUILD_REGISTRY_AUTHTOKEN=$(gcloud auth print-access-token --project=iac-dev-432418) \
  -e JS_BUILD_REGISTRY_GCLOUD_PROJECT=iac-dev-432418 \
  -e JS_BUILD_PREFER_CUSTOM_REGISTRY="" \
  node:20-slim-corepack .src/main/build-helper/build-dep.sh
```

set authentication and pull dependencies (basically doing npm run xyz or yarn run xyz)

```
docker run --rm -it --net=host \
  -u $(id -u ${USER}):$(id -g ${USER}) \
  -v "$(pwd)":"$(pwd)" \
  -w "$(pwd)" \
  -v "${HOME}/.gitconfig":"/home/<USER>/.gitconfig" \
  -e JS_BUILD_CUSTOM_SCOPE_REGISTRY_AUTHTOKEN=$(gcloud auth print-access-token --project=endless-gizmo-264508) \
  -e JS_BUILD_CUSTOM_SCOPE_REGISTRY_GCLOUD_PROJECT=endless-gizmo-264508 \
  -e JS_BUILD_REGISTRY_AUTHTOKEN=$(gcloud auth print-access-token --project=iac-dev-432418) \
  -e JS_BUILD_REGISTRY_GCLOUD_PROJECT=iac-dev-432418 \
  -e JS_BUILD_PREFER_CUSTOM_REGISTRY="" \
  node:20-slim-corepack .src/main/build-helper/build.sh run build
```

you can also run any other targets of the package.json

```
docker run --rm -it --net=host \
  -u $(id -u ${USER}):$(id -g ${USER}) \
  -v "$(pwd)":"$(pwd)" \
  -w "$(pwd)" \
  -v "${HOME}/.gitconfig":"/home/<USER>/.gitconfig" \
  -e JS_BUILD_CUSTOM_SCOPE_REGISTRY_AUTHTOKEN=$(gcloud auth print-access-token --project=endless-gizmo-264508) \
  -e JS_BUILD_CUSTOM_SCOPE_REGISTRY_GCLOUD_PROJECT=endless-gizmo-264508 \
  -e JS_BUILD_REGISTRY_AUTHTOKEN=$(gcloud auth print-access-token --project=iac-dev-432418) \
  -e JS_BUILD_REGISTRY_GCLOUD_PROJECT=iac-dev-432418 \
  -e JS_BUILD_PREFER_CUSTOM_REGISTRY="" \
  node:20-slim-corepack .src/main/build-helper/build.sh run build-storybook
```

## Installation in your own project:

make sure you have latest 20 version

```
nvm install 20.18
nvm use node 20.18
```

make sure you have the latest corepack version installed:

```
  corepack -v
  corepack disable
  npm install -g corepack@latest --force
  corepack enable
  corepack -v
```

It should be showing 0.31.0 or higher.

make sure you defined the package manager you want to use in the package.json

```
  "devDependencies": {
    ...
    "corepack": "^0.31.0",
    ...
  },
  "packageManager": "npm@10.8.2",
  "nodeLinker": "node-modules",
```

run
`.src/main/build-helper/build-dep.sh`

then either run (will autodetect what tool to use)
`.src/main/build-helper/build.sh run build`

or depends on what packagemanager youre using
`npm run build`
`yarn run build`
